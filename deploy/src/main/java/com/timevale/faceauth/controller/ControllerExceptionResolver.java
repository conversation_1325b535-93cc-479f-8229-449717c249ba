package com.timevale.faceauth.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/11 11
 */
@Slf4j
@ControllerAdvice(basePackages = "com.timevale.faceauth.controller")
class ControllerExceptionResolver {

  @ExceptionHandler(Throwable.class)
  public void resolveTargetException(
      Throwable targetError, HttpServletRequest request, HttpServletResponse response) {
    log.warn(targetError.getMessage(), targetError);
    try {
      response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, targetError.getMessage());
    } catch (Exception cause) {
      log.warn("Error response on request[" + request.getRequestURL().toString() + "] .", cause);
    }
  }
}
