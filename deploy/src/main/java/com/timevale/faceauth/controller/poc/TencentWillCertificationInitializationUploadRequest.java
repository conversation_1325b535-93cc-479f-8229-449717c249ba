package com.timevale.faceauth.controller.poc;

import com.google.common.collect.Lists;
import com.timevale.faceauth.service.core.support.ArgumentUtil;
import lombok.experimental.Accessors;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 腾讯云刷脸认证初始化上送请求
 *
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/18 17
 */
@Accessors(chain = true)
class TencentWillCertificationInitializationUploadRequest
    extends TencentWillCertificationRequest {

  private String nonce;

  private String speed;

  //应用模式意愿核身默认上传 2
  private String liveService = "2";
  //意愿核身类型产品服务类型为“2”时，需要填写参数，配置参数：0-问答模式，1-播报模式默认配置为0
  private String willType;
  private List<WillContentModel> willContentList = Lists.newArrayList(new WillContentModel());

  /** 用户 ID ，用户的唯一标识（不能带有特殊字符） */
  private final String userId;
  /** 证件号码 */
  private final String idNo;
  /**
   * 枚举值：不传该字段、传空、01为国内二代身份证（原有的证件服务）；
   * 02：港澳居民来往内地通行证、03：台湾居民来往内地通行证定、04：居国外的中国公
   * 民护照、05：外国人永久居留身份证
   * 腾讯云建议：国内二代身份证不用做修改，idType 不传；新增的出入境证件类型增加传
   * idType字段。
   * */
  private final String idType;
  /** 姓名 */
  private final String name;
  /** 比对源照片类型 参数值为1：水纹正脸照 参数值为2：高清正脸照 */
  private final Byte sourcePhotoType;
  /**
   * 比对源照片， 注意： 原始图片不能超过500KB，且必须为 JPG 或 PNG 格式 参数有值： 使用合作伙伴提供的比对源照片进行比对，必须注照片是正脸可信照片，照片质量由合作方保证
   * 参数为空： 根据身份证号+姓名使用权威数据源比对
   */
  private String sourcePhotoStr;

  public TencentWillCertificationInitializationUploadRequest(TencentCloudCertificationRequestBuilder builder, String userId, String idNo, String idType, String name, Byte sourcePhotoType) {
    super(builder);
    this.userId = userId;
    this.idNo = idNo;
    this.idType = idType;
    this.name = name;
    this.sourcePhotoType = sourcePhotoType;
  }

  private TencentWillCertificationInitializationUploadRequest(
      TencentCloudCertificationInitializationUploadRequestBuilder builder) {
    super(builder);
    this.userId = builder.userId;
    this.idNo = builder.idNo;
    this.idType = builder.idType;
    this.name = builder.name;
    this.sourcePhotoStr = builder.sourcePhotoStr;
    this.sourcePhotoType = (StringUtils.isEmpty(builder.sourcePhotoStr) ? null : builder.sourcePhotoType);
  }

  public List<WillContentModel> getWillContentList() {
    return willContentList;
  }

  public void setWillContentList(List<WillContentModel> willContentList) {
    this.willContentList = willContentList;
  }

  public String getUserId() {
    return userId;
  }

  public String getIdNo() {
    return idNo;
  }

  public String getSpeed() {
    return speed;
  }

  public void setSpeed(String speed) {
    this.speed = speed;
  }

  public String getWillType() {
    return willType;
  }

  public void setWillType(String willType) {
    this.willType = willType;
  }

  public String getNonce() {
    return nonce;
  }

  public void setNonce(String nonce) {
    this.nonce = nonce;
  }

  public void setLiveService(String liveService) {
    this.liveService = liveService;
  }

  public String getIdType() {
    return idType;
  }

  public String getLiveService() {
    return liveService;
  }

  public String getName() {
    return name;
  }

  public String getSourcePhotoStr() {
    return sourcePhotoStr;
  }

  public Byte getSourcePhotoType() {
    return sourcePhotoType;
  }

  void setSourcePhotoStr(String sourcePhotoStr) {
    this.sourcePhotoStr = sourcePhotoStr;
  }

  static TencentCloudCertificationInitializationUploadRequestBuilder createBuilder() {
    return (new TencentCloudCertificationInitializationUploadRequestBuilder());
  }

  static class TencentCloudCertificationInitializationUploadRequestBuilder
      extends TencentCloudCertificationRequestBuilder<
          TencentWillCertificationInitializationUploadRequest,
          TencentCloudCertificationInitializationUploadRequestBuilder> {

    private String userId;
    private String idNo;
    private String idType;
    private String name;
    private String sourcePhotoStr;
    private Byte sourcePhotoType = null;

    @Override
    TencentWillCertificationInitializationUploadRequest build() {
      return (new TencentWillCertificationInitializationUploadRequest(this));
    }

    @Override
    protected TencentCloudCertificationInitializationUploadRequestBuilder self() {
      return this;
    }

    TencentCloudCertificationInitializationUploadRequestBuilder setUserId(String userId) {
      ArgumentUtil.throwIfEmptyArgument(userId, "userId");
      this.userId = userId;
      return this;
    }

    TencentCloudCertificationInitializationUploadRequestBuilder setIdNo(String idNo) {
      ArgumentUtil.throwIfEmptyArgument(idNo, "idNo");
      this.idNo = idNo;
      return this;
    }

    TencentCloudCertificationInitializationUploadRequestBuilder setIdType(String idType) {
      this.idType = idType;
      return this;
    }

    TencentCloudCertificationInitializationUploadRequestBuilder setName(String name) {
      ArgumentUtil.throwIfEmptyArgument(name, "name");
      this.name = name;
      return this;
    }

    TencentCloudCertificationInitializationUploadRequestBuilder setSourcePhotoStr(
        String sourcePhotoStr) {
      this.sourcePhotoStr = sourcePhotoStr;
      return this;
    }

    TencentCloudCertificationInitializationUploadRequestBuilder setSourcePhotoType(
        byte sourcePhotoType) {
      ArgumentUtil.throwIfNotTrue(sourcePhotoType > (byte) 0x00, "sourcePhotoType");
      this.sourcePhotoType = sourcePhotoType;
      return this;
    }
  }
}
