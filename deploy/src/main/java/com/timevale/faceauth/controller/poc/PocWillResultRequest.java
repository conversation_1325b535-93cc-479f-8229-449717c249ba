package com.timevale.faceauth.controller.poc;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @since 2020/2/27 上午11:27
 */
@ApiModel("刷脸用户信息结果请求")
@Data
public class PocWillResultRequest extends ToString {


    @ApiModelProperty(value = "任务ID", required = true)
    private String faceId;

}
