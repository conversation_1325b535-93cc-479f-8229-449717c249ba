package com.timevale.faceauth.controller.poc;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.provider.support.RestTemplateRequestResolver;
import com.timevale.faceauth.service.domain.provider.tencent.TencentCloudFaceInvocationErrorHandler;
import com.timevale.faceauth.service.domain.provider.tencent.TencentCloudUtil;
import com.timevale.faceauth.service.domain.provider.tencent.TencentWebAppIdVersion;
import com.timevale.faceauth.service.domain.provider.tencent.domain.TencentWebAppIdVersionEnum;
import com.timevale.faceauth.service.utils.UUIDUtil;
import com.timevale.faceauth.service.utils.providerLog.result.TencentCloudInitResult;
import com.timevale.mandarin.base.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.ArrayList;
import java.util.List;

/**
 * https://cloud.tencent.com/document/product/1007/77305
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/18 18
 */
@Component
@Slf4j
public class TencentWillCertificationResultnvocationHandler
    extends TencentCloudFaceInvocationErrorHandler
    implements TencentWillCertificationSignatureResolver<
        TencentWillCertificationResultRequest> {

  @Autowired
  private  RestTemplateRequestResolver requestResolver;

  @Override
  public String resolveSignature(TencentWillCertificationResultRequest request)
      throws FaceException {
    return TencentCloudUtil.signature(
        request.getAppId(),
        request.getOrderNo(),
        request.getNonce(),
        request.getVersion(),
        request.getTicket());
  }

  TencentWillCertificationResult invoke(
          String orderNo)
      throws FaceException {
    TencentWillCertificationResultRequest resultRequest =
        prepareRequest(orderNo);
    URI uri = requestResolver.resolveURI("https://kyc1.qcloud.com/api/server/getWillFaceResult?orderNo="+orderNo);
    RequestEntity<TencentWillCertificationResultRequest> requestRequestEntity =
        (new RequestEntity<>(resultRequest, HttpMethod.POST, uri));

    ResponseEntity<String> responseEntity =
        requestResolver.resolveResponse(
                orderNo,
            "xxx",
            requestRequestEntity,
            String.class
        );
    TencentWillCertificationResultResponse response;
    try {
      response =
          JsonUtils.json2pojo(
              responseEntity.getBody(),
                  TencentWillCertificationResultResponse.class);
    } catch (Exception cause) {
      log.error(
          "Data["
              + responseEntity.getBody()
              + "] could not instantiate type["
              + TencentWillCertificationInitializationUploadResponse.class
              + "] .",
          cause);
      throw FaceException.valueOf(FaceStatusCode.PROVIDER_ERROR_API_RESULT_SYNTAX);
    }


    TencentWillCertificationResult result = response.getResultIfAbsent();
    return result;
  }

  private TencentWillCertificationResultRequest prepareRequest(
          String orderNo)
      throws FaceException {


    TencentWebAppIdVersion appIdVersion = TencentWebAppIdVersionEnum.RECORD_VIDEO;
    TencentWillCertificationResultRequest resultRequest =
            TencentWillCertificationResultRequest.createBuilder()
             .setAppId(appIdVersion.getAccessHolder().getWebAppId())
            .setOrderNo(orderNo)
            .setSignatureResolver(this)
            .setTicket(
                TencentCloudUtil.getTicketCache(orderNo, appIdVersion))

            .build();

    resultRequest.setNonce(UUIDUtil.generateUUID());
    resultRequest.signatureRequest();
    return resultRequest;
  }
}
