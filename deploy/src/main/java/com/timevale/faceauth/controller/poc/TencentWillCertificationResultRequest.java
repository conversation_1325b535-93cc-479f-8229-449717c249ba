package com.timevale.faceauth.controller.poc;

import lombok.experimental.Accessors;

/**
 * 腾讯云刷脸认证初始化上送请求
 *
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/18 17
 */
@Accessors(chain = true)
class TencentWillCertificationResultRequest
    extends TencentWillCertificationRequest {

  private String nonce;

  private String getFile;
  private String getWillFile;


  public TencentWillCertificationResultRequest(TencentCloudCertificationRequestBuilder builder) {
    super(builder);

  }

  private TencentWillCertificationResultRequest(
      TencentCloudCertificationInitializationUploadRequestBuilder builder) {
    super(builder);

  }


  public String getGetFile() {
    return getFile;
  }

  public void setGetFile(String getFile) {
    this.getFile = getFile;
  }

  public String getGetWillFile() {
    return getWillFile;
  }

  public void setGetWillFile(String getWillFile) {
    this.getWillFile = getWillFile;
  }

  public String getNonce() {
    return nonce;
  }

  public void setNonce(String nonce) {
    this.nonce = nonce;
  }

  static TencentCloudCertificationInitializationUploadRequestBuilder createBuilder() {
    return (new TencentCloudCertificationInitializationUploadRequestBuilder());
  }

  static class TencentCloudCertificationInitializationUploadRequestBuilder
      extends TencentCloudCertificationRequestBuilder<
          TencentWillCertificationResultRequest,
          TencentCloudCertificationInitializationUploadRequestBuilder> {


    @Override
    TencentWillCertificationResultRequest build() {
      return (new TencentWillCertificationResultRequest(this));
    }

    @Override
    protected TencentCloudCertificationInitializationUploadRequestBuilder self() {
      return this;
    }


  }
}
