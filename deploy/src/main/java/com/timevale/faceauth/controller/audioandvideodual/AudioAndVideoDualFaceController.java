package com.timevale.faceauth.controller.audioandvideodual;

import com.timevale.faceauth.controller.ProviderFaceAuthorizationCompletionController;
import com.timevale.faceauth.dal.pfs.laudiovideodual.AudioAndVideoDuaFilelRepository;
import com.timevale.faceauth.service.api.FaceAuthServiceV3;
import com.timevale.faceauth.service.bean.FilePostTransferUrlResult;
import com.timevale.faceauth.service.bean.FileTransferUrlModel;
import com.timevale.faceauth.service.bean.control.*;
import com.timevale.faceauth.service.domain.provider.ConfigurableProviderService;
import com.timevale.faceauth.service.inner.FileService;
import com.timevale.faceauth.service.input.FaceAuthInput;
import com.timevale.faceauth.service.result.FaceAuthResult;
import com.timevale.faceauth.service.result.SupportResult;
import com.timevale.faceauth.service.thirdparty.AlipayUserCompareRequest;
import com.timevale.faceauth.service.thirdparty.alipay.AlipayLoginInvocationHandler;
import com.timevale.faceauth.service.thirdparty.alipay.AlipayUserCompareResult;
import com.timevale.faceauth.service.thirdparty.audiovideodual.AudioAndVideoDualFileTask;
import com.timevale.faceauth.service.thirdparty.audiovideodual.AudioAndVideoDualPhotoFileTask;
import com.timevale.faceauth.service.thirdparty.audiovideodual.cache.AudioVideoDualCache;
import com.timevale.faceauth.service.thirdparty.audiovideodual.cache.AudioVideoDualContext;
import com.timevale.faceauth.service.thirdparty.audiovideodual.context.AudioVideoDualAppConfig;
import com.timevale.faceauth.service.thirdparty.audiovideodual.context.AudioVideoDualConfig;
import com.timevale.faceauth.service.thirdparty.audiovideodual.context.AudioVideoDualResultEnum;
import com.timevale.faceauth.service.thirdparty.audiovideodual.context.FileTaskResult;
import com.timevale.faceauth.service.thirdparty.audiovideodual.exception.AudioAndVideoDualRuntimeException;
import com.timevale.faceauth.service.thirdparty.audiovideodual.repository.AudioVideoDualAuthorization;
import com.timevale.faceauth.service.thirdparty.audiovideodual.repository.AudioVideoDualAuthorizationRepository;
import com.timevale.faceauth.service.thirdparty.audiovideodual.repository.ProviderAudioVideoDualAlipayUserResult;
import com.timevale.faceauth.service.thirdparty.audiovideodual.repository.ProviderAudioVideoDualAuthorizeResult;
import com.timevale.faceauth.service.utils.task.AsyncTaskFactory;
import com.timevale.filesystem.common.service.result.GetFileInfoResult;
import com.timevale.footstone.antforest.common.rpc.model.response.AlipayUserResponse;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.base.enums.BaseResultCodeEnum;
import com.timevale.mandarin.base.exception.BaseRuntimeException;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

//@RestMapping(path = "faceauth-service/esign/audiovideodual")
//@ExternalService
//@Api(tags = "智能视频认证刷脸前端API")
@Deprecated
public class AudioAndVideoDualFaceController {

  private static final Logger log = LoggerFactory.getLogger(AudioAndVideoDualFaceController.class);
  // 10M

  @Autowired private FaceAuthServiceV3 faceAuthServiceV3;
  @Autowired private AudioVideoDualCache audioVideoDualCache;
  @Autowired private AudioVideoDualConfig audioVideoDualConfig;
  @Autowired AudioVideoDualAppConfig appVoiceConfig;
  @Autowired private FileService fileService;
  @Autowired private AlipayLoginInvocationHandler alipayLoginInvocationHandler;
  @Autowired private AudioVideoDualAuthorizationRepository repository;
  @Autowired private AudioAndVideoDuaFilelRepository filelRepository;
  @Autowired private ProviderFaceAuthorizationCompletionController completionController;

  @RestMapping(path = "startFace", method = RequestMethod.POST)
  @ApiOperation(value = "获取刷脸地址", httpMethod = "POST")
  public SupportResult<FaceAuthResult> startNewFaceAuth(@RequestBody FaceAuthInput faceAuthInput) {
    return faceAuthServiceV3.startNewFaceAuth(faceAuthInput);
  }

  /**
   * 小程序登录
   *
   * @param token
   */
  @RestMapping(path = "{token}/login", method = RequestMethod.POST)
  @ApiOperation(value = "小程序登录", httpMethod = "POST")
  public BaseResult<AudioVideoDualLoginResponse> login(
      @PathVariable("token") String token,
      @RequestBody(required = false) AudioVideoDualLoginRequest request) {
    AudioVideoDualContext context = checkTokenAngGet(token);
    if (context.isCompleted()) {
      return BaseResult.fail(AudioVideoDualResultEnum.FLOW_OVER);
    }

    AudioVideoDualAuthorization authorization = repository.get(context.getCertifyId());
    context.setCheckLimit(audioVideoDualConfig.getCheckLimit());
    updateContext(context);

    AudioVideoDualLoginResponse response =
        AudioVideoDualLoginResponse.builder()
            .checkLimit(context.getCheckLimit())
            .checkMode(authorization.getCheckMode())
            .homePage(authorization.getHomePage())
            .voiceCheck(authorization.getVoiceResult())
            .voiceTemplate(authorization.getVoiceTemplate())
            .voiceTemplateAppend(authorization.getVoiceTemplateAppend())
            .returnUrl(context.getReturnUrl())
            .build();

    return BaseResult.success(response);
  }

  /**
   * 用户身份核对
   *
   * @param token
   */
  @RestMapping(path = "{token}/checkUser", method = RequestMethod.POST)
  @ApiOperation(value = "用户身份核对", httpMethod = "POST")
  public BaseResult checkUser(
      @PathVariable("token") String token, @RequestBody AudioVideoDualCheckUserRequest request) {
    AudioVideoDualContext context = checkTokenAngGet(token);
    if (context.isCompleted()) {
      return BaseResult.fail(AudioVideoDualResultEnum.FLOW_OVER);
    }
    if (request.checkParamError()) {
      return BaseResult.fail(AudioVideoDualResultEnum.PARAM_WRONG);
    }

    AudioVideoDualAuthorization authorization = repository.get(context.getCertifyId());
    AlipayUserCompareRequest compareRequest =
        AlipayUserCompareRequest.builder()
            .alipayAppId(request.getAlipayAppId())
            .alipayAuthCode(request.getAlipayAuthCode())
            .certNo(authorization.getIdNo())
            .name(authorization.getName())
            .build();
    AlipayUserCompareResult compareResult =
        alipayLoginInvocationHandler.compareAlipayUser(compareRequest);
    if (!compareResult.isPass()) {
      return BaseResult.fail(AudioVideoDualResultEnum.USER_IDNO_NAME_MIS_MATCH);
    }

    AlipayUserResponse alipayUserResponse = compareResult.getAlipayUser();
    repository.alipayUserUpdate(
        ProviderAudioVideoDualAlipayUserResult.builder()
            .certifyId(authorization.getCertifyId())
            .alipayUserId(alipayUserResponse.getUserId())
            .alipayUserMobile(alipayUserResponse.getMobile())
            .build());
    return BaseResult.success(null);
  }

  /**
   * 获取上传原始视频地址
   *
   * @param token
   */
  @RestMapping(path = "{token}/uploadUrl", method = RequestMethod.POST)
  @ApiOperation(value = "获取上传原始视频地址", httpMethod = "POST")
  public BaseResult<AudioVideoDualVideoUploadResponse> uploadUrl(
      @PathVariable("token") String token,
      @RequestBody(required = false) EsignFaceVideoUploadRequest request) {
    AudioVideoDualContext context = checkTokenAngGet(token);
    if (context.isCompleted()) {
      return BaseResult.fail(AudioVideoDualResultEnum.FLOW_OVER);
    }

    log.info("{} uploadUrl  file size:{}", token, request.getFileSize());
    if (request.getFileSize() > audioVideoDualConfig.getFaceFileVideoByteLimit()) {
      return BaseResult.fail(AudioVideoDualResultEnum.FILE_SIZE_LIMIT);
    }

    FileTransferUrlModel model = new FileTransferUrlModel();
    model.setContentMd5(request.getContentMd5());
    model.setContentType(request.getContentType());
    model.setFileName(request.getFileName());
    model.setFileSize(request.getFileSize());
    model.setEncryption(false);
    FilePostTransferUrlResult result = fileService.transferPostUrl(model);

    AudioVideoDualVideoUploadResponse response =
        AudioVideoDualVideoUploadResponse.builder()
            .accessId(result.getAccessId())
            .dir(result.getDir())
            .fileKey(result.getFileKey())
            .expire(result.getExpire())
            .host(result.getHost())
            .policy(result.getPolicy())
            .signature(result.getSignature())
            .build();
    return BaseResult.success(response);
  }

  /**
   * 检测结果
   *
   * @param token
   */
  @RestMapping(path = "{token}/check", method = RequestMethod.POST)
  @ApiOperation(value = "检测结果", httpMethod = "POST")
  public BaseResult<AudioVideoDualCheckResultResponse> check(
      @PathVariable("token") String token, @RequestBody AudioVideoDualCheckResultRequest request) {
    AudioVideoDualCheckResultV2Request V2request = new AudioVideoDualCheckResultV2Request();
    V2request.setVoiceResult(request.getVoiceResult());
    V2request.setVideo(request.getVideo());
    V2request.setVideoEncryption(request.getVideoEncryption());

    String[] images = request.getImages();
    if (images != null && images.length > 0) {
      List<AudioVideoDualCheckResultV2Request.FileResource> resources =
          new ArrayList<>(images.length);
      for (int i = 0; i < images.length; i++) {
        AudioVideoDualCheckResultV2Request.FileResource fileResource =
            new AudioVideoDualCheckResultV2Request.FileResource();
        fileResource.setFileId(images[i]);
        fileResource.setEncryption(request.getImageEncryption());
        fileResource.setFormat(request.getImageFormat());
        resources.add(fileResource);
      }
      V2request.setImages(resources);
    }
    return checkV2(token, V2request);
  }

  /**
   * 检测结果
   *
   * @param token
   */
  @RestMapping(path = "{token}/check/v2", method = RequestMethod.POST)
  @ApiOperation(value = "检测结果", httpMethod = "POST")
  public BaseResult<AudioVideoDualCheckResultResponse> checkV2(
      @PathVariable("token") String token,
      @RequestBody AudioVideoDualCheckResultV2Request request) {
    AudioVideoDualContext context = checkTokenAngGet(token);
    if (context.isCompleted()) {
      return BaseResult.fail(AudioVideoDualResultEnum.FLOW_OVER);
    }

    if (request.checkParamError()) {
      return BaseResult.fail(AudioVideoDualResultEnum.PARAM_WRONG);
    }

    this.checkImages(request);
    AudioVideoDualAuthorization authorization = repository.get(context.getCertifyId());
    boolean isLimit = this.checkAndUpdateLimit(context);
    if (isLimit) {
      return BaseResult.fail(AudioVideoDualResultEnum.REQUEST_LIMIT);
    }

    if (authorization.isAudioMode()
        && !audioVideoDualConfig.getVoiceCheck().equals(request.getVoiceResult())) {
      return BaseResult.success(
          AudioVideoDualCheckResultResponse.buildFail(
              context.getCheckLimit(), AudioVideoDualResultEnum.USER_VOICE_RESULT_ERROR));
    }

    AudioVideoDualResultEnum resultEnum = compareFace(request, authorization);
    this.synchFaceResult(resultEnum, authorization);

    // 核对成功
    if (resultEnum.isSuccess()) {
      this.callBackFaceResult(authorization.getBizId());
      context.setCompleted(true);
      updateContext(context);
      return BaseResult.success(
          AudioVideoDualCheckResultResponse.buildSuccess(context.getCheckLimit()));
    }

    // 最后一次失败
    if (context.getCheckLimit() <= 0) {
      this.callBackFaceResult(authorization.getBizId());
      context.setCompleted(true);
      updateContext(context);
    }

    return BaseResult.success(
        AudioVideoDualCheckResultResponse.buildFail(context.getCheckLimit(), resultEnum));
  }

  // 回调上游业务最终状态
  private void callBackFaceResult(String bizId) {
    completionController.onFaceAuthorizationCallback(
        ConfigurableProviderService.PROVIDER_AUDIO_VIDEO_DUAL, bizId, null);
  }

  // 同步刷脸结果
  private void synchFaceResult(
      AudioVideoDualResultEnum resultEnum, AudioVideoDualAuthorization authorization) {

    ProviderAudioVideoDualAuthorizeResult authorizeResult =
        ProviderAudioVideoDualAuthorizeResult.builder()
            .certifyId(authorization.certifyId)
            .completedStatus(resultEnum.isSuccess() ? 1 : 2)
            .completedTimestampMillis(System.currentTimeMillis())
            .message(resultEnum.getMsg())
            .build();
    repository.completed(authorizeResult);
  }
  /**
   * 查询刷脸结果
   *
   * @param token
   */
  @RestMapping(path = "{token}/result", method = RequestMethod.GET)
  @ApiOperation(value = "查询刷脸结果", httpMethod = "GET")
  public BaseResult<AudioVideoDualQueryResultResponse> result(@PathVariable("token") String token) {
    AudioVideoDualContext context = checkTokenAngGet(token);
    AudioVideoDualAuthorization authorization = repository.get(context.getCertifyId());
    AudioVideoDualQueryResultResponse resultResponse =
        AudioVideoDualQueryResultResponse.builder()
            .msg(authorization.getMessage())
            .status(authorization.getCompletedStatus())
            .build();
    return BaseResult.success(resultResponse);
  }

  private AudioVideoDualContext checkTokenAngGet(String token) {
    log.info("invalidParam {}", token);
    AudioVideoDualContext context = audioVideoDualCache.queryContextWithToken(token);
    if (context == null) {
      throw new AudioAndVideoDualRuntimeException(AudioVideoDualResultEnum.UNAUTH);
    }

    return context;
  }

  private void updateContext(AudioVideoDualContext context) {
    audioVideoDualCache.updateContextWithToken(context.getToken(), context);
  }

  // 是否限制请求
  private boolean checkAndUpdateLimit(AudioVideoDualContext context) {
    if (context.getCheckLimit() <= 0) {
      return true;
    }
    context.setCheckLimit(context.getCheckLimit() - 1);
    this.updateContext(context);
    return false;
  }

  private void checkImages(AudioVideoDualCheckResultV2Request request) {
    if (request.getImages().size() < audioVideoDualConfig.getFaceFileImageCountMin()
        || request.getImages().size() > audioVideoDualConfig.getFaceFileImageCountMax()) {
      throw new AudioAndVideoDualRuntimeException(AudioVideoDualResultEnum.USER_IMAGE_SIZE_ERROR);
    }
    for (AudioVideoDualCheckResultV2Request.FileResource fileResource : request.getImages()) {
      GetFileInfoResult imageResult = this.getFileInfo(fileResource.getFileId());
      if (imageResult.getFileSize() > audioVideoDualConfig.getFaceFileBImageByteLimit()) {
        throw new AudioAndVideoDualRuntimeException(AudioVideoDualResultEnum.FILE_SIZE_LIMIT);
      }
    }
  }

  private AudioVideoDualResultEnum compareFace(
      AudioVideoDualCheckResultV2Request request, AudioVideoDualAuthorization authorization) {
    // 白名单用户放行
    if (audioVideoDualConfig.getWhiteListSet().contains(authorization.getIdNo())) {
      return AudioVideoDualResultEnum.SUCCESS;
    }

    AsyncTaskFactory asynTaskFactory = AsyncTaskFactory.builder();
    List<AudioVideoDualCheckResultV2Request.FileResource> checkImages =
        filterCompareImages(request.getImages());
    for (int i = 0; i < checkImages.size(); i++) {
      asynTaskFactory.addTask(
          new AudioAndVideoDualPhotoFileTask(
              filelRepository, fileService, authorization, checkImages.get(i)));
    }
    asynTaskFactory.addTask(
        new AudioAndVideoDualFileTask(
            filelRepository,
            fileService,
            authorization,
            "VIDEO",
            request.getVideo(),
            request.getVideoEncryption()));

    List<FileTaskResult> resultList = asynTaskFactory.build().result();
    if (CollectionUtils.isEmpty(resultList) || resultList.size() != asynTaskFactory.size()) {
      return AudioVideoDualResultEnum.SYSTEM_ERROR;
    }
    for (FileTaskResult result : resultList) {
      if (!result.isSuccess()) {
        return result.getResultEnum();
      }
    }
    return AudioVideoDualResultEnum.SUCCESS;
  }

  private List<AudioVideoDualCheckResultV2Request.FileResource> filterCompareImages(
      List<AudioVideoDualCheckResultV2Request.FileResource> images) {
    List<AudioVideoDualCheckResultV2Request.FileResource> realImages =
        images.stream().filter(im -> im != null).collect(Collectors.toList());
    int minImage = audioVideoDualConfig.getFaceFileImageCountMin();
    if (realImages.size() <= minImage) {
      return realImages;
    }

    List<AudioVideoDualCheckResultV2Request.FileResource> mustCheckImages =
        realImages.stream().filter(im -> im.isMustCheck()).collect(Collectors.toList());
    if (mustCheckImages.size() >= minImage) {
      return mustCheckImages;
    }
    List<AudioVideoDualCheckResultV2Request.FileResource> notMustCheckImages =
        realImages.stream().filter(im -> !im.isMustCheck()).collect(Collectors.toList());

    for (int i = 0; i < notMustCheckImages.size(); i++) {
      mustCheckImages.add(notMustCheckImages.get(i));
      if (mustCheckImages.size() == minImage) {
        break;
      }
    }
    return mustCheckImages;
  }

  private GetFileInfoResult getFileInfo(String fileKey) {
    try {
      return fileService.getFileInfo(fileKey);
    } catch (BaseRuntimeException e) {
      log.warn("get file info error : " + fileKey, e);
      if (BaseResultCodeEnum.SYSTEM_ERROR.getCode().equals(e.getCode())) {
        throw new AudioAndVideoDualRuntimeException(AudioVideoDualResultEnum.PARAM_WRONG);
      }
      throw e;
    }
  }
}
