package com.timevale.faceauth.controller;

import com.timevale.faceauth.service.constant.SystemConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashSet;
import java.util.Set;

/**
 * 跨域支持
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Order(1)
@Configuration
@Slf4j
public class CrossFilter implements Filter {

  @Autowired SystemConfig systemConfig;
  private static final String HEADER_CORS_ORIGIN = "Access-Control-Allow-Origin";
  private static final String HEADER_CORS_CREDENTIALS = "Access-Control-Allow-Credentials";
  private static final String HEADER_CORS_METHODS = "Access-Control-Allow-Methods";
  private static final String HEADER_CORS_HEADERS = "Access-Control-Allow-Headers";
  private static final String METHOD_OPTIONS = "OPTIONS";
  private static final Set<String> IGNORE_ACTIONS = new HashSet<>();

  static {
    IGNORE_ACTIONS.add("/health");
  }

  @Override
  public void init(FilterConfig filterConfig) throws ServletException {}

  @Override
  public void doFilter(
      ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
      throws IOException, ServletException {
    HttpServletResponse hsResponse = (HttpServletResponse) servletResponse;
    HttpServletRequest hsRequest = (HttpServletRequest) servletRequest;
    // 设置跨域
    doCorsResponse(hsRequest, hsResponse);
    // OPTIONS 跨域处理
    if (METHOD_OPTIONS.equalsIgnoreCase(hsRequest.getMethod())) {
      return;
    }
    filterChain.doFilter(hsRequest, hsResponse);
  }

  private void doCorsResponse(HttpServletRequest request, HttpServletResponse response) {
    String action = request.getRequestURI();
    if (IGNORE_ACTIONS.contains(action)) {
      return;
    }
    log.info("doCorsResponse,{} : {}", request.getMethod(), request.getRequestURI());
    setResponseHeader(response, HEADER_CORS_ORIGIN, fetchOrigin(request));
    setResponseHeader(response, HEADER_CORS_CREDENTIALS, "true");
    setResponseHeader(response, HEADER_CORS_METHODS, "GET, POST, PUT, DELETE, OPTIONS");
    setResponseHeader(
        response,
        HEADER_CORS_HEADERS, systemConfig.getAccessAllowHeaders());
  }

  private void setResponseHeader(HttpServletResponse response, String header, String value) {
    if (StringUtils.isEmpty(response.getHeader(header))) {
      response.setHeader(header, value);
    }
  }

  private String fetchOrigin(HttpServletRequest request) {
    String origin = request.getHeader("Origin");
    return StringUtils.isEmpty(origin) ? "*" : origin;
  }

  @Override
  public void destroy() {}
}
