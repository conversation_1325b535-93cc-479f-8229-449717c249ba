package com.timevale.faceauth.controller.esignface;

import com.alibaba.fastjson.JSON;
import com.timevale.faceauth.service.bean.FileTransferUrlModel;
import com.timevale.faceauth.service.bean.FileTransferUrlResult;
import com.timevale.faceauth.service.bean.control.*;
import com.timevale.faceauth.service.component.OpenPlatformClient;
import com.timevale.faceauth.service.enums.EsignFaceStatusEnum;
import com.timevale.faceauth.service.esignface.cache.EsignFaceCache;
import com.timevale.faceauth.service.esignface.cache.EsignFaceContext;
import com.timevale.faceauth.service.esignface.config.EsignFaceConfig;
import com.timevale.faceauth.service.esignface.config.FaceResultAdapter;
import com.timevale.faceauth.service.esignface.config.FaceResultEnum;
import com.timevale.faceauth.service.esignface.resolver.EsignFaceResolver;
import com.timevale.faceauth.service.inner.FileService;
import com.timevale.faceauth.service.liveness.facade.*;
import com.timevale.faceauth.service.liveness.repository.LivenessAuthorization;
import com.timevale.faceauth.service.liveness.repository.ProviderLivenessAuthorization;
import com.timevale.faceauth.service.liveness.repository.resolver.LivenessAuthorizationResolver;
import com.timevale.faceauth.service.liveness.repository.resolver.ProviderLivenessAuthorizationResolver;
import com.timevale.faceauth.service.liveness.resource.LivenessResource;
import com.timevale.faceauth.service.liveness.resource.LivenessResourceResolver;
import com.timevale.faceauth.service.liveness.support.LivenessAuthorizationCompletionService;
import com.timevale.faceauth.service.liveness.support.LivenessAuthorizationPhotoType;
import com.timevale.faceauth.service.result.SupportResult;
import com.timevale.faceauth.service.utils.CardUtils;
import com.timevale.faceauth.service.utils.FileUtils;
import com.timevale.filesystem.common.service.result.GetFileInfoResult;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Base64Utils;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

//@RestMapping(path = "faceauth-service/esign/face")
//@ExternalService
//@Api(tags = "e签宝刷脸前端API")
@Deprecated
public class EsignFaceController {

  private static final Logger log = LoggerFactory.getLogger(EsignFaceController.class);
  // 10M

  @Autowired private OpenPlatformClient platformClient;
  @Autowired private EsignFaceConfig faceConfig;
  @Autowired private EsignFaceCache faceCache;
  @Autowired private FileService fileService;
  @Autowired private LivenessAuthorizationService livenessAuthorizationService;
  @Autowired private LivenessResourceResolver livenessResourceResolver;
  @Autowired private LivenessAuthorizationResolver authorizationResolver;
  @Autowired private ProviderLivenessAuthorizationResolver providerLivenessAuthorizationResolver;
  @Autowired private LivenessAuthorizationCompletionService livenessAuthorizationCompletionService;

  /**
   * /** 获取刷脸信息
   *
   * @param faceId
   */
  @RestMapping(path = "{faceId}/liveness/info", method = RequestMethod.POST)
  @ApiOperation(value = "获取刷脸信息", httpMethod = "POST")
  public BaseResult<EsignFaceUserInfoResponse> faceInfo(@PathVariable("faceId") String faceId) {
    if (invalidParam(faceId)) {
      return BaseResult.fail(FaceResultEnum.PARAM_INVALID);
    }
    EsignFaceContext context = faceCache.queryContextWithFaceId(faceId);
    if (EsignFaceStatusEnum.isFinish(context.getStatus())) {
      return BaseResult.fail(FaceResultEnum.FORBIDDEN);
    }
    // 2. 获取指定动作
    LivenessActionInput livenessActionInput = new LivenessActionInput();
    livenessActionInput.setLivenessId(context.getLivenessId());
    livenessActionInput.setPhoto(context.getPhoto());
    // TODO 照片类型咋区分, 指定动作怎么满足, 活体动作枚举是啥
    // 活体动作枚举 LivenessAuthorizationActionType
    livenessActionInput.setPhotoType(
        LivenessAuthorizationPhotoType.TYPE_HIGH_DEFINITION.getValue());
    SupportResult<String> actionsSupportResult =
        livenessAuthorizationService.getLivenessActions(livenessActionInput);
    if (EsignFaceResolver.livenessFail(actionsSupportResult)) {
      return BaseResult.fail(FaceResultEnum.FACE_INIT_FACE_FAIL);
    }
    String actionResult = actionsSupportResult.getData();
    if (StringUtils.isBlank(actionResult)) {
      return BaseResult.fail(FaceResultEnum.FACE_INIT_FACE_FAIL);
    }

    // 刷脸动作
    String[] actionsArray = StringUtils.split(actionResult, EsignFaceConfig.SPLIT);
    List<Integer> livenessActions = new ArrayList<>();
    for (String action : actionsArray) {
      livenessActions.add(Integer.parseInt(action));
    }
    context.setLivenessActions(livenessActions);
    context.setLoginCount(context.getLoginCount() + 1);
    log.info("{} getLivenessActions， origin:{}, finally:{}", faceId, actionResult, livenessActions);
    faceCache.createContextWithFaceId(faceId, context);

    LivenessAuthorization authorization =
        authorizationResolver.resolveLiveness(context.getLivenessId());

    EsignFaceUserInfoResponse response = new EsignFaceUserInfoResponse();
    response.setActionList(livenessActions);
    response.setIdNo(CardUtils.hideIdNo(context.getIdNo()));
    response.setName(CardUtils.hideName(context.getName()));
    response.setReturnUrl(context.getReturnUrl());
    response.setPageColor(platformClient.getPageColor(context.getBizAppId()));
    response.setFileUploadMax(faceConfig.getFaceFileUploadMax());
    if (context.getLoginCount() > 1) {
      response.setShowTips(false);
    }
    response.setShowAgreement(authorization.isShowAgreement());
    return BaseResult.success(response);
  }

  /**
   * 获取上传原始视频地址
   *
   * @param faceId
   */
  @RestMapping(path = "{faceId}/liveness/video/uploadUrl", method = RequestMethod.POST)
  @ApiOperation(value = "获取上传原始视频地址", httpMethod = "POST")
  public BaseResult<EsignFaceVideoUploadResponse> uploadUrl(
      @PathVariable("faceId") String faceId,
      @RequestBody(required = false) EsignFaceVideoUploadRequest request) {
    if (invalidParam(faceId)) {
      return BaseResult.fail(FaceResultEnum.FACE_RESULT_FORBIDDEN);
    }
    EsignFaceContext context = faceCache.queryContextWithFaceId(faceId);
    if (EsignFaceStatusEnum.isFinish(context.getStatus())) {
      return BaseResult.fail(FaceResultEnum.FORBIDDEN);
    }

    BaseResult checkFileResult = checkFile(faceId, request);
    if (!checkFileResult.ifSuccess()) {
      return checkFileResult;
    }

    FileTransferUrlModel model = new FileTransferUrlModel();
    model.setContentMd5(request.getContentMd5());
    model.setContentType(request.getContentType());
    model.setFileName(request.getFileName());
    model.setFileSize(request.getFileSize());
    FileTransferUrlResult result = fileService.transferUrl(model);

    context.setVideoFileId(result.getFileKey());
    faceCache.createContextWithFaceId(faceId, context);

    EsignFaceVideoUploadResponse response = new EsignFaceVideoUploadResponse();
    response.setFileKey(result.getFileKey());
    response.setUploadUrl(result.getUploadUrl());
    return BaseResult.success(response);
  }

  /**
   * 检测活体视频
   *
   * @param faceId
   */
  @RestMapping(path = "{faceId}/liveness/video/check", method = RequestMethod.POST)
  @ApiOperation(value = "检测活体视频", httpMethod = "POST")
  public BaseResult<EsignFaceResultResponse> videoCheck(
      @PathVariable("faceId") String faceId,
      @RequestBody(required = false) EsignFaceVideoCheckRequest request) {
    if (invalidParam(faceId)) {
      return BaseResult.fail(FaceResultEnum.FACE_RESULT_FORBIDDEN);
    }
    EsignFaceContext context = faceCache.queryContextWithFaceId(faceId);
    // 防止多次提交第一次
    if (EsignFaceStatusEnum.isSuccess(context.getStatus())) {
      return BaseResult.success(EsignFaceResultResponse.success());
    }

    long startTime = System.currentTimeMillis();

    //    context.setVideoFileId("$65ea2ce648dc48bc8311df02274caeb7$748880960$H");
    BaseResult fileCheckResult = checkFaceFileAndGetBytes(faceId, startTime, request, context);
    if (!fileCheckResult.ifSuccess()) {
      return fileCheckResult;
    }
    byte[] videoBytes = (byte[]) fileCheckResult.getData();
    BaseResult uploadFaceVideoResult = uploadFaceVideo(faceId, startTime, videoBytes, context);
    if (!uploadFaceVideoResult.ifSuccess()) {
      return uploadFaceVideoResult;
    }
    boolean isUploadFaceFileSuccess = (boolean) uploadFaceVideoResult.getData();
    if (Boolean.FALSE.equals(isUploadFaceFileSuccess)) {
      return BaseResult.fail(FaceResultEnum.FACE_RESULT_VIDEO_UPLOAD_FAIL);
    }

    // 活体检测
    LivenessAuthorizationInput authorizationInput = new LivenessAuthorizationInput();
    authorizationInput.setLivenessId(context.getLivenessId());
    SupportResult<LivenessRecognition> livenessAuthenticationSupportResult =
        livenessAuthorizationService.recognition(authorizationInput);
    log.info(
        "{} videoCheck,cost={} , recognition:{}",
        faceId,
        (System.currentTimeMillis() - startTime),
        JSON.toJSONString(livenessAuthenticationSupportResult));

    // 防止多次提交第二次
    if (EsignFaceStatusEnum.isSuccess(context.getStatus())) {
      return BaseResult.success(EsignFaceResultResponse.success());
    }

    if (EsignFaceResolver.livenessFail(livenessAuthenticationSupportResult)) {
      return BaseResult.fail(FaceResultEnum.FACE_RESULT_FACE_FAIL);
    }

    LivenessRecognition recognition = livenessAuthenticationSupportResult.getData();
    LivenessRecognitionResult recognitionResult = recognition.getResult();
    if (recognitionResult.isSuccess()) {
      // success to completed liveness recognition .
      this.faceCompleted(faceId, EsignFaceStatusEnum.SUCCESS, context, recognition);
      return BaseResult.success(EsignFaceResultResponse.success());
    }
    String errorMessage = recognitionResult.getResultMsg();
    EsignFaceResultResponse resultResponse =
        EsignFaceResultResponse.fail(FaceResultEnum.FACE_RESULT_FACE_FAIL, errorMessage);

    log.info("{} check face error  , errorMessage:{}", faceId, errorMessage);
    String adapterMessage = FaceResultAdapter.checkLivenessFail(errorMessage);
    if (StringUtils.isNotBlank(adapterMessage)) {
      resultResponse =
          EsignFaceResultResponse.fail(
              FaceResultEnum.FACE_VEDIO_CHECK_FAIL.getCode(), adapterMessage);
    }

    // completed liveness recognition only .
    this.faceCompleted(faceId, EsignFaceStatusEnum.FAIL, context, recognition);
    return BaseResult.success(resultResponse);
  }

  private boolean invalidParam(String faceId) {
    log.info("invalidParam {}", faceId);
    EsignFaceContext context = faceCache.queryContextWithFaceId(faceId);
    if (context == null) {
      return true;
    }

    return false;
  }

  private BaseResult checkFile(String faceId, EsignFaceVideoUploadRequest request) {
    log.info(
        "{} uploadUrl video file size={}, timestamp={}",
        faceId,
        request.getFileSize(),
        request.getFileTimestamp());
    if (request.getFileSize() > faceConfig.getFaceFileUploadMax()) {
      return BaseResult.fail(FaceResultEnum.FACE_RESULT_FILE_SIZE_MAX_INVALID);
    }
    if (request.getFileSize() < faceConfig.getFaceFileUploadMin()) {
      return BaseResult.fail(FaceResultEnum.FACE_RESULT_FILE_INVALID);
    }

    if (!faceConfig.isFaceFileTimestampCheck()) {
      return BaseResult.success();
    }


    Long fileTimestamp = request.getFileTimestamp();
    if (fileTimestamp == null) {
      return BaseResult.fail(FaceResultEnum.PARAM_WRONG);
    }

    //存在前端识别时间戳错误的情况
    if (fileTimestamp < 0) {
      return BaseResult.success();
    }

    long expiredMax =
        System.currentTimeMillis() + faceConfig.getFaceFileTimestampScope() * 60 * 1000;
    long expiredMin =
        System.currentTimeMillis() - faceConfig.getFaceFileTimestampScope() * 60 * 1000;
    if (fileTimestamp > expiredMax || fileTimestamp < expiredMin) {
      return BaseResult.fail(FaceResultEnum.FACE_RESULT_FILE_INVALID);
    }

    return BaseResult.success();
  }

  private BaseResult<byte[]> checkFaceFileAndGetBytes(
      String faceId, long startTime, EsignFaceVideoCheckRequest request, EsignFaceContext context) {
    String fileKey = context.getVideoFileId();
    GetFileInfoResult videoResult = fileService.getFileInfo(fileKey);
    log.info(
        "{} videoCheck,cost={} , videoBytes fileKey:{},size:{}",
        faceId,
        (System.currentTimeMillis() - startTime),
        fileKey,
        videoResult.getFileSize());
    byte[] videoBytes;
    String fileSign;
    if (videoResult.getFileSize() > faceConfig.getFaceFileUploadCompressMin()) {
      byte[] sourceBytes = fileService.download(fileKey);
      fileSign = getVideoSign(sourceBytes);
      videoBytes = FileUtils.compressVideo(sourceBytes, videoResult.getFileName());
    } else {
      videoBytes = fileService.download(fileKey);
      fileSign = getVideoSign(videoBytes);
    }
    log.info(
        "{} videoCheck,cost={} , videoBytes ,request sign={}, response sign={}",
        faceId,
        (System.currentTimeMillis() - startTime),
        request.getSign(),
        fileSign);
    if (faceConfig.isFaceFileSignCheck() && !fileSign.equals(request.getSign())) {
      return BaseResult.fail(FaceResultEnum.FACE_RESULT_FILE_SIGN_INVALID);
    }
    return BaseResult.success(videoBytes);
  }

  private BaseResult<Boolean> uploadFaceVideo(
      String faceId, long startTime, byte[] videoBytes, EsignFaceContext context) {
    String livenessId = context.getLivenessId();
    String videoBase64 = Base64Utils.encodeToString(videoBytes);
    log.info(
        "{} videoCheck,cost={} , videoBase64 size:{}",
        faceId,
        (System.currentTimeMillis() - startTime),
        videoBase64.length());
    ProviderLivenessAuthorization providerLiveness =
        providerLivenessAuthorizationResolver.resolveProviderLiveness(livenessId);
    assert null != providerLiveness;
    log.info(
        "{} videoCheck,cost={} , resolveProviderLiveness result:{}",
        faceId,
        (System.currentTimeMillis() - startTime),
        JSON.toJSONString(providerLiveness));
    LivenessResource videoResource =
        livenessResourceResolver.resolvePersistentResource(
            videoBase64, providerLiveness.getPhotoLocation());
    assert null != videoResource;

    log.info(
        "{} videoCheck,cost={} , resolvePersistentResource result:{}",
        faceId,
        (System.currentTimeMillis() - startTime),
        JSON.toJSONString(videoResource));
    // 上传活体动作视频
    LivenessVideoOrAudioUploadInput videoUploadInput = (new LivenessVideoOrAudioUploadInput());
    videoUploadInput.setLivenessId(livenessId);
    videoUploadInput.setResources(
        new LivenessVideoOrAudioInput[] {
          (new LivenessVideoOrAudioInput() {
            @Override
            public String getActions() {
              return providerLiveness.getActions();
            }

            @Override
            public String getResource() {
              return videoResource.getLocation();
            }
          })
        });

    log.info(
        "{} videoCheck,cost={} , videoUploadInput:{}",
        faceId,
        (System.currentTimeMillis() - startTime),
        JSON.toJSONString(videoUploadInput));

    SupportResult<Boolean> uploadVideoSupportResult =
        livenessAuthorizationService.uploadVideoOrAudio(videoUploadInput);
    log.info(
        "{} videoCheck,cost={} , uploadVideoOrAudio:{}",
        faceId,
        (System.currentTimeMillis() - startTime),
        JSON.toJSONString(uploadVideoSupportResult));
    if (EsignFaceResolver.livenessFail(uploadVideoSupportResult)) {
      return BaseResult.fail(FaceResultEnum.FACE_RESULT_VIDEO_UPLOAD_FAIL);
    }
    return BaseResult.success(uploadVideoSupportResult.getData());
  }

  private void faceCompleted(
      String faceId,
      EsignFaceStatusEnum faceStatusEnum,
      EsignFaceContext context,
      LivenessRecognition recognition) {
    context.setStatus(faceStatusEnum.getStatus());
    faceCache.createContextWithFaceId(faceId, context);
    livenessAuthorizationCompletionService.completedRecognition(recognition);
  }

  private String getVideoSign(byte[] sourceBytes) {
    byte[] signBytes = Arrays.copyOfRange(sourceBytes, 100, 200);
    return DigestUtils.md5DigestAsHex(signBytes);
  }
}
