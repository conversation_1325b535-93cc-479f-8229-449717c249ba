package com.timevale.faceauth.controller.poc;

import com.google.common.base.Objects;
import com.timevale.faceauth.service.utils.UrlUtil;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.framework.tedis.core.Tedis;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

@RestMapping(path = "faceauth-service/poc/face")
@ExternalService
@Api(tags = "微信POC前端API")
@Slf4j
public class PocFaceController {


  @Autowired
  private TencentWillCertificationInitializationUploadInvocationHandler uploadInvocationHandler;

  @Autowired
  private TencentWillCertificationInitializationBeginningInvocationHandler beginningInvocationHandler;

  @Autowired
  private TencentWillCertificationResultnvocationHandler resultnvocationHandler;

  @Autowired
  private MegviiWillService megviiWillService;

  @RestMapping(path = "initial", method = RequestMethod.POST)
  @ApiOperation(value = "发起认证", httpMethod = "POST")
  public BaseResult<PocWillUploadResponse> initial(
      @RequestBody PocWillUploadRequest request) {
    PocWillContext context = new PocWillContext();
    context.setChannel(request.getChannel());
    String returnUrl = request.getReturnUrl();
    if(Objects.equal(returnUrl, "esign")){
      returnUrl = "http://realname-v3-videowill.projectk8s.tsign.cn/identity/vedioWillResult";
    }

    returnUrl = UrlUtil.appendParam(returnUrl,"faceId",request.getFaceId());
    context.setReturnUrl(returnUrl);
    request.setReturnUrl(returnUrl);
    if(Objects.equal(request.getChannel(), PocWillUploadRequest.channel_megvii) ||
            Objects.equal(request.getChannel(), PocWillUploadRequest.channel_esign)){
      PocWillUploadResponse response = megviiWillService.initial(request);

      context.setFaceId(response.getFaceId());
      context.setProviderOrderNo(response.getProviderOrderNo());
      TedisUtil.set("PocWillContext_"+response.getFaceId(), context, 30, TimeUnit.MINUTES);
      return BaseResult.success(response);
    }

    TencentWillCertificationInitializationUploadResultWrap uploadResultWrap =
            uploadInvocationHandler.invoke(request);
    PocWillUploadResponse response =
            beginningInvocationHandler.invoke(request,
                    uploadResultWrap.getResult());



    context.setFaceId(response.getFaceId());
    context.setProviderOrderNo(response.getProviderOrderNo());
    TedisUtil.set("PocWillContext_"+response.getFaceId(), context, 30, TimeUnit.MINUTES);
    return BaseResult.success(response);
  }


  @RestMapping(path = "result", method = RequestMethod.POST)
  @ApiOperation(value = "认证结果", httpMethod = "POST")
  public BaseResult<PocWillResultResponse> result(
          @RequestBody PocWillResultRequest request ){
    PocWillResultResponse response = new PocWillResultResponse();
    response.setCode("0");
    response.setMessage("认证完成");

    PocWillContext context = TedisUtil.get("PocWillContext_"+request.getFaceId());
    if(Objects.equal(context.getChannel(), PocWillUploadRequest.channel_tencent)){

      TencentWillCertificationResult result = resultnvocationHandler.invoke(context.getProviderOrderNo());
      if(StringUtils.isNoneBlank(result.getFaceMsg())){
        response.setCode(result.getFaceCode());
        response.setMessage(result.getFaceMsg());
      }

      return BaseResult.success(response);
    }

    response = megviiWillService.getFesult(context.getProviderOrderNo());
    return BaseResult.success(response);
  }


  @RestMapping(path = "callback", method = RequestMethod.POST)
  @ApiOperation(value = "回调", httpMethod = "POST")
  public void callback(
          @RequestParam String faceId , HttpServletRequest request, HttpServletResponse response){

    PocWillContext context = TedisUtil.get("PocWillContext_"+faceId);
    String returnUrl = context.getReturnUrl();


  }

  public  void redirect(HttpServletResponse targetResponse, String redirectUrl) throws IOException {
    targetResponse.sendRedirect(redirectUrl);
  }
}
