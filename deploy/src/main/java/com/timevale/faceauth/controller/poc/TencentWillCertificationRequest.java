package com.timevale.faceauth.controller.poc;

import com.timevale.faceauth.service.core.support.ArgumentUtil;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/18 17
 */
class TencentWillCertificationRequest extends TencentWillCertificationSignableRequest {

  private final String appId;
  private final String orderNo;
  private final String ticket;

  TencentWillCertificationRequest(TencentCloudCertificationRequestBuilder builder) {
    super(builder);
    this.appId = builder.appId;
    this.orderNo = builder.orderNo;
    this.ticket = builder.ticket;
  }

  public String getAppId() {
    return appId;
  }

  public String getOrderNo() {
    return orderNo;
  }

  public String getTicket() {
    return ticket;
  }

  abstract static class TencentCloudCertificationRequestBuilder<
          T extends TencentWillCertificationRequest,
          B extends TencentCloudCertificationRequestBuilder>
      extends TencentCloudCertificationSignableRequestBuilder<T, B> {

    private String appId;
    private String orderNo;
    private String ticket;

    B setAppId(String appId) {
      ArgumentUtil.throwIfEmptyArgument(appId, "appId");
      this.appId = appId;
      return self();
    }

    B setOrderNo(String orderNo) {
      ArgumentUtil.throwIfEmptyArgument(orderNo, "orderNo");
      this.orderNo = orderNo;
      return self();
    }

    B setTicket(String ticket) {
      ArgumentUtil.throwIfEmptyArgument(ticket, "ticket");
      this.ticket = ticket;
      return self();
    }
  }
}
