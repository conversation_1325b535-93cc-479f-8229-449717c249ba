package com.timevale.faceauth.controller.poc;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/8/20 19:06
 */
public class MegviiWillConstans {

    public static final Map<String,String> STATUS_MESSAGE_  = new HashMap<>();
    public static final Map<String,String> WILL_RESULT_  = new HashMap<>();
    public static final Map<String,String> WILL_MESSAGE_  = new HashMap<>();

    static {
        STATUS_MESSAGE_.put("NOT_STARTED", "还没有开始验证");
        STATUS_MESSAGE_.put("PROCESSING", "正在进行验证");
        STATUS_MESSAGE_.put("WEBRTC_UNSUPPORTED", "浏览器不支持引起失败");
        STATUS_MESSAGE_.put("OK", "完成验证");
        STATUS_MESSAGE_.put("FAILED", "验证流程未完成或出现异常");
        STATUS_MESSAGE_.put("CANCELLED", "用户主动取消了验证流程");
        STATUS_MESSAGE_.put("TIMEOUT", "流程超时");


        WILL_RESULT_.put("PASS", "意愿认证通过");
        WILL_RESULT_.put("FAIL", "意愿认证失败");

        WILL_MESSAGE_.put("ANSWER_INCONSISTENT", "意愿表达与标准答案不一致");
        WILL_MESSAGE_.put("NO_AUDIO", "意愿确认用户回答音频无声音");
        WILL_MESSAGE_.put("FUNASR_ERR", "语音识别服务异常");
        WILL_MESSAGE_.put("ACTION_FAIL", "动作检测失败");
    }
}
