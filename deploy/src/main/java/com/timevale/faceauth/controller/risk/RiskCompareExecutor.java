package com.timevale.faceauth.controller.risk;

import com.timevale.faceauth.service.domain.repository.FaceInfo;
import com.timevale.faceauth.service.domain.repository.FaceRepository;
import com.timevale.faceauth.service.domain.repository.ProviderFaceInfo;
import com.timevale.faceauth.service.domain.repository.ProviderFaceRepository;
import com.timevale.faceauth.service.utils.SpringUtil;
import com.timevale.faceauth.service.utils.exceptions.IgnoreExceptionUtil;
import com.timevale.mandarin.base.security.MD5Utils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.security.risk.spring.boot.service.RiskService;
import com.timevalue.security.risk.common.enums.ResponseCodeEnum;
import com.timevalue.security.risk.common.models.RiskEventData;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @DATE 2025/7/25 10:13
 */

@Service
@Slf4j
public class RiskCompareExecutor {
    @Value("${transfer.header.user.agent.max.length:1000}")
    private int transferHeaderUserAgentMaxLength;


    @Autowired
    private FaceRepository faceRepository;
    @Autowired
    ProviderFaceRepository providerFaceRepository;


    @Resource(name = "asyncDataReporting")
    AsyncTaskExecutor asyncDataReporting;


    public void execute(String faceId, HttpServletRequest httpServletRequest) {
        IgnoreExceptionUtil.ignore(() -> {
            if (!RiskConfig.switchOverallRiskOpen) {
                return;
            }

            // 2025/7/25  提前把header 数据解析
            String headerIp = httpServletRequest.getHeader("X-Tsign-Client-Ip");
            String headerUA = parseHeaderUA(httpServletRequest, faceId);

            asyncDataReporting.execute(() -> {

                FaceInfo faceInfo = faceRepository.getFaceInfoByFaceId(faceId);

                ProviderFaceInfo providerFaceInfo = providerFaceRepository.getByFaceId(faceId);

                ResponseCodeEnum responseCodeEnum = getResponseCodeEnum(faceInfo, providerFaceInfo);
                if (Objects.isNull(responseCodeEnum)) {
                    return;
                }

                RiskService riskService = SpringUtil.getBean(RiskService.class);

                Map<String, Object> request = new HashMap<>();
                request.put("ip", headerIp);
                request.put("userAgent", headerUA);
                request.put("userAgentDigest", MD5Utils.md5(headerUA));
                request.put("name", faceInfo.getName());
                request.put("idCard", faceInfo.getIdNo());
                request.put("appid", faceInfo.getAppId());
                request.put("faceMode", faceInfo.getProvider());


                Map<String, Object> response = new HashMap<>();
                response.put("code", responseCodeEnum.getCode());

                RiskEventData riskEventData = new RiskEventData();
                //
                riskEventData.setEventCode("H5FaceAuth");

                riskEventData.setRequestData(request);
                riskEventData.setResponseData(response);
                riskService.publishEvent(riskEventData);
                log.info("aop_logger , RiskService.publishEvent req :{} ", JsonUtils.obj2json(riskEventData));
            });
        });


    }

    private String parseHeaderUA(HttpServletRequest requestWrapper, String faceId) {
        String header = requestWrapper.getHeader("User-Agent");
        if (StringUtils.isBlank(header)) {
            header = requestWrapper.getHeader("userAgent");
        }
        if (StringUtils.isBlank(header)) {
            header = requestWrapper.getHeader("X-Tsign-Client-User-Agent");
        }

        if (StringUtils.isBlank(header)) {
            return StringUtils.EMPTY;
        }

        if (header.length() > transferHeaderUserAgentMaxLength) {
            log.warn("faceId={} . userAgent 超出长度预 {} , 截取上报", faceId, transferHeaderUserAgentMaxLength);
            // 2025/7/25  是否需要截断
            return header.substring(0,transferHeaderUserAgentMaxLength);
        }

        return header;
    }

    private static ResponseCodeEnum getResponseCodeEnum(FaceInfo faceInfo, ProviderFaceInfo providerFaceInfo) {
        if (!providerFaceInfo.isDone()) {
            log.info("未完成刷脸不进行数据上报  , faceInfo = {}", JsonUtils.obj2json(Lists.newArrayList(faceInfo, providerFaceInfo)));
            return null;
        }
        return faceInfo.isOk() ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.FAIL;
    }
}
