package com.timevale.faceauth.controller.poc;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import lombok.Data;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/18 18
 */
@Data
class TencentWillCertificationResponse<R> extends TencentWillCertificationStatusResponse {

  private R result;

  R getResultIfAbsent() throws FaceException {
    return result;
  }

  R getResultIfAbsent(R defaultResult){
    return (null == result ? defaultResult : result);
  }
}
