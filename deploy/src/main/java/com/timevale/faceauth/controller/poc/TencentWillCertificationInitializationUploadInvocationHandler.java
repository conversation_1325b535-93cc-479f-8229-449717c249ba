package com.timevale.faceauth.controller.poc;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.domain.FaceAuthorizationPhotoResolver;
import com.timevale.faceauth.service.domain.provider.support.RestTemplateRequestResolver;
import com.timevale.faceauth.service.domain.provider.tencent.TencentCloudFaceInvocationErrorHandler;
import com.timevale.faceauth.service.domain.provider.tencent.TencentCloudUtil;
import com.timevale.faceauth.service.domain.provider.tencent.TencentWebAppIdVersion;
import com.timevale.faceauth.service.domain.provider.tencent.domain.TencentWebAppIdVersionEnum;
import com.timevale.faceauth.service.utils.UUIDUtil;
import com.timevale.faceauth.service.utils.providerLog.ProviderLogService;
import com.timevale.faceauth.service.utils.providerLog.result.TencentCloudInitResult;
import com.timevale.mandarin.base.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.ArrayList;
import java.util.List;

/**
 * https://cloud.tencent.com/document/product/1007/77305
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/18 18
 */
@Component
@Slf4j
public class TencentWillCertificationInitializationUploadInvocationHandler
    extends TencentCloudFaceInvocationErrorHandler
    implements TencentWillCertificationSignatureResolver<
        TencentWillCertificationInitializationUploadRequest> {

  private final ConfigurableProperties properties;
  private final FaceAuthorizationPhotoResolver faceAuthorizationPhotoResolver;
  private final RestTemplateRequestResolver requestResolver;

  @Autowired private ProviderLogService providerLogService;

  @Autowired
  public TencentWillCertificationInitializationUploadInvocationHandler(
      ConfigurableProperties properties,
      FaceAuthorizationPhotoResolver faceAuthorizationPhotoResolver,
      RestTemplateRequestResolver requestResolver) {
    this.properties = properties;
    this.faceAuthorizationPhotoResolver = faceAuthorizationPhotoResolver;
    this.requestResolver = requestResolver;
  }

  @Override
  public String resolveSignature(TencentWillCertificationInitializationUploadRequest request)
      throws FaceException {
    return TencentCloudUtil.signature(
        request.getAppId(),
        request.getUserId(),
        request.getNonce(),
        request.getVersion(),
        request.getTicket());
  }

  TencentWillCertificationInitializationUploadResultWrap invoke(
          PocWillUploadRequest request)
      throws FaceException {
    TencentWillCertificationInitializationUploadRequest uploadRequest =
        prepareRequest(request);
    URI uri = requestResolver.resolveURI("https://kyc1.qcloud.com/api/server/getWillFaceId?orderNo="+uploadRequest.getOrderNo());
    RequestEntity<TencentWillCertificationInitializationUploadRequest> requestRequestEntity =
        (new RequestEntity<>(uploadRequest, HttpMethod.POST, uri));
    TencentCloudInitResult tencentCloudInitResult = new TencentCloudInitResult(uploadRequest.getOrderNo(), 0);
    ResponseEntity<String> responseEntity =
        requestResolver.resolveResponse(
                uploadRequest.getOrderNo(),
            "xxx",
            requestRequestEntity,
            String.class,
            tencentCloudInitResult,
            (x, y, z) -> providerLogService.logTencentCloudInit((TencentCloudInitResult) x, y, z),
            (x, y, z) -> providerLogService.logTencentCloudInitWithException(
                (TencentCloudInitResult) x, y, z)
        );
    TencentWillCertificationInitializationUploadResponse response;
    try {
      response =
          JsonUtils.json2pojo(
              responseEntity.getBody(),
              TencentWillCertificationInitializationUploadResponse.class);
    } catch (Exception cause) {
      log.error(
          "Data["
              + responseEntity.getBody()
              + "] could not instantiate type["
              + TencentWillCertificationInitializationUploadResponse.class
              + "] .",
          cause);
      throw FaceException.valueOf(FaceStatusCode.PROVIDER_ERROR_API_RESULT_SYNTAX);
    }


    TencentWillCertificationInitializationUploadResult result = response.getResultIfAbsent();
    String inputStr;
    try {

      inputStr = JsonUtils.obj2json(request);
    } catch (Exception cause) {
      log.error("Error serialize request .", cause);
      throw FaceException.valueOf(FaceStatusCode.PROVIDER_ERROR_API_RESULT_SYNTAX);
    }
    return (new TencentWillCertificationInitializationUploadResultWrap(inputStr, result));
  }

  private TencentWillCertificationInitializationUploadRequest prepareRequest(
          PocWillUploadRequest request)
      throws FaceException {

    String faceId = request.getFaceId();
    TencentWebAppIdVersion appIdVersion = TencentWebAppIdVersionEnum.RECORD_VIDEO;
    TencentWillCertificationInitializationUploadRequest uploadRequest =
        TencentWillCertificationInitializationUploadRequest.createBuilder()
             .setAppId(appIdVersion.getAccessHolder().getWebAppId())
            .setIdNo(request.getCertNo())
            .setName(request.getName())
            .setUserId(faceId)
            .setOrderNo(faceId)
            .setSignatureResolver(this)
            .setTicket(
                TencentCloudUtil.getTicketCache(faceId, appIdVersion))

            .build();

    List<WillContentModel> queuedComponents =  new ArrayList<>();

    int id = 0;
    for(PocWillQuestion question : request.getConfig().getQuestions()){

      WillContentModel contentModel = new WillContentModel();
      contentModel.setId(String.valueOf(id));
      contentModel.setAnswer(question.getAnswer());
      contentModel.setQuestion(question.getQuestion());
      id++;
      queuedComponents.add(contentModel);
    }
    uploadRequest.setWillContentList(queuedComponents);
    uploadRequest.setSpeed(request.getConfig().getSpeed());
    uploadRequest.setNonce(UUIDUtil.generateUUID());
    uploadRequest.signatureRequest();
    return uploadRequest;
  }
}
