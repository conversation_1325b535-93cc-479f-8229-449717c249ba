package com.timevale.faceauth.controller.risk;

import com.timevale.framework.puppeteer.model.ConfigChangeEvent;
import com.timevale.framework.puppeteer.spring.annotation.PuppeteerConfigChangeListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @DATE 2025/4/21 17:47
 */
@Slf4j
@Component
public class RiskConfig {


    /**
     * 全局开关控制，是否启用风控能力
     */
    public static boolean switchOverallRiskOpen = false;
//    /**
//     * 产品规则配置
//     */
//    public static Map<String, RiskConfigDTO> riskConfigDTOMap = new HashMap<>();


    @Value("${switchOverallRiskOpen:false}")
    public void setSwitchOverallRiskOpen(String result) {
        RiskConfig.switchOverallRiskOpen = Boolean.parseBoolean(result);
        log.info("PuppeteerConfigChangeListener success->  switchOverallRiskOpen  ,new value ={}", result);
    }


//    @Value("${risk.config.mapping:[]}")
//    public void setRiskConfigDTOMap(String result) {
//        List<RiskConfigDTO> dtos = JsonUtils.json2list(result, RiskConfigDTO.class);
//        RiskConfig.riskConfigDTOMap = dtos.stream().collect(Collectors.toMap(RiskConfigDTO::getInfoAuthServiceType, Function.identity(), (o, n) -> o));
//        log.info("PuppeteerConfigChangeListener success->  risk.config.mapping  ,new value ={}", JsonUtils.obj2json(riskConfigDTOMap));
//    }

    @PuppeteerConfigChangeListener
    private void configListener(ConfigChangeEvent changeEvent) {
        if (changeEvent.isChanged("switchOverallRiskOpen")) {
            setSwitchOverallRiskOpen(changeEvent.getChange("switchOverallRiskOpen").getNewValue());
        }
//        if (changeEvent.isChanged("risk.config.mapping")) {
//            setRiskConfigDTOMap(changeEvent.getChange("risk.config.mapping").getNewValue());
//        }
    }



//    public static RiskConfigDTO getRiskCode(InfoAuthServiceType serviceType) {
//        if (Objects.isNull(serviceType) || MapUtils.isEmpty(riskConfigDTOMap)) {
//            return null;
//        }
//        return riskConfigDTOMap.get(serviceType.name());
//    }
}
