package com.timevale.faceauth.controller.poc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2024/8/19 10:25
 */
@Data
public class PocWillConfig {



    @ApiModelProperty(value = "用于设置意愿活体播报问题语速\n" +
            "0：1倍速（默认值）\n" +
            "1：1.2倍速\n" +
            "2：1.35倍速\n" +
            "3：1.5倍速", required = true)
    private String speed  = "0";

    @ApiModelProperty(value = "回答问题列表", required = true)
    private List<PocWillQuestion> questions;
}
