package com.timevale.faceauth.controller.poc;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.support.ArgumentUtil;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/21 11
 */
class TencentWillCertificationSignableRequest {

  static final String VERSION_1_0 = "1.0.0";

  private final transient TencentWillCertificationSignatureResolver signatureResolver;
  private final String version;
  private String sign;

  TencentWillCertificationSignableRequest(
      TencentCloudCertificationSignableRequestBuilder builder) {
    this.signatureResolver = builder.signatureResolver;
    this.version = builder.version;
  }

  @SuppressWarnings("unchecked")
  void signatureRequest() throws FaceException {
    ArgumentUtil.throwIfNull(signatureResolver, "signatureResolver");
    this.sign = signatureResolver.resolveSignature(this);
  }

  public String getVersion() {
    return version;
  }

  public String getSign() {
    return sign;
  }

  abstract static class TencentCloudCertificationSignableRequestBuilder<
      T extends TencentWillCertificationSignableRequest,
      B extends TencentCloudCertificationSignableRequestBuilder> {

    private String version = VERSION_1_0;
    private TencentWillCertificationSignatureResolver<T> signatureResolver;

    abstract T build();

    protected B self() {
      @SuppressWarnings("unchecked")
      B builder = (B) this;
      return builder;
    }

    B setVersion(String version) {
      ArgumentUtil.throwIfEmptyArgument(version, "version");
      this.version = version;
      return self();
    }

    B setSignatureResolver(TencentWillCertificationSignatureResolver<T> signatureResolver) {
      ArgumentUtil.throwIfNull(signatureResolver, "signatureResolver");
      this.signatureResolver = signatureResolver;
      return self();
    }
  }
}
