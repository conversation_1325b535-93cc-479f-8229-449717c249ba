package com.timevale.faceauth.controller.poc;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @since 2020/2/27 上午11:27
 */
@ApiModel("刷脸用户结果返回请求")
@Data
public class PocWillResultResponse extends ToString {


    @ApiModelProperty(value = "刷脸任务Id", required = true)
    private String code;

    @ApiModelProperty(value = "刷脸任务地址", required = true)
    private String message;



}
