package com.timevale.faceauth.controller.poc;

import com.alibaba.fastjson.JSON;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.domain.processor.ClientApplicationType;
import com.timevale.faceauth.service.domain.processor.ConfigurableFaceProcessor;
import com.timevale.faceauth.service.domain.processor.ConfigurableFaceProcessors;
import com.timevale.faceauth.service.domain.provider.support.FaceAuthorizationInitializingContext;
import com.timevale.faceauth.service.domain.provider.support.RestTemplateRequestResolver;
import com.timevale.faceauth.service.domain.provider.tencent.*;
import com.timevale.faceauth.service.domain.provider.tencent.domain.TencentWebAppIdVersionEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/18 18
 */
@Slf4j
@Component
public class TencentWillCertificationInitializationBeginningInvocationHandler
    implements TencentWillCertificationSignatureResolver<
        TencentWillCertificationInitializationBeginningRequest> {

  private static final Map<ClientApplicationType, String> CLIENT_APPLICATION_TYPE_STRING_MAPPER;

  static {
    CLIENT_APPLICATION_TYPE_STRING_MAPPER = (new HashMap<>(4));
    CLIENT_APPLICATION_TYPE_STRING_MAPPER.put(
        ClientApplicationType.TYPE_BROWSER,
        TencentWillCertificationInitializationBeginningRequest.TYPE_FROM_BROWSER);
    CLIENT_APPLICATION_TYPE_STRING_MAPPER.put(
        ClientApplicationType.TYPE_APP,
        TencentWillCertificationInitializationBeginningRequest.TYPE_FROM_APP);
  }

  private final ConfigurableProperties properties;
  private final RestTemplateRequestResolver requestResolver;

  @Autowired
  public TencentWillCertificationInitializationBeginningInvocationHandler(
      ConfigurableProperties properties, RestTemplateRequestResolver requestResolver) {
    this.properties = properties;
    this.requestResolver = requestResolver;
  }

  @Override
  public String resolveSignature(TencentWillCertificationInitializationBeginningRequest request)
      throws FaceException {
    return TencentCloudUtil.signature(
        request.getAppId(),
        request.getUserId(),
        request.getOrderNo(),
        request.getVersion(),
        request.getFaceId(),
        request.obtainTicket(),
        request.getNonce());
  }

  PocWillUploadResponse invoke(
          PocWillUploadRequest request,
      TencentWillCertificationInitializationUploadResult uploadResult)
      throws FaceException {

    //播报问题文字语速：默认为1.0倍速"-1"代表0.8倍；"0"代表1.0倍（默认）；
    // "1"代表1.2倍；"1.5"代表1.35倍；"2"代表1.5倍
    TencentWebAppIdVersion appIdVersion = TencentWebAppIdVersionEnum.RECORD_VIDEO;
    TencentWillCertificationInitializationBeginningRequest beginRequest =
        TencentWillCertificationInitializationBeginningRequest.createBuilder()
            .setFrom("browser")
            .setFaceId(uploadResult.getFaceId())
            .setUserId(uploadResult.getOrderNo())
            .setUrl(getUrl(request))
            .setOrderNo(uploadResult.getOrderNo())
            .setSignatureResolver(this)
            .setAppId(appIdVersion.getAccessHolder().getWebAppId())
            .setTicket(
                TencentCloudUtil.getNonceTicket(uploadResult.getOrderNo(), appIdVersion))
            .build();
    beginRequest.signatureRequest();
    String queryString = requestResolver.resolveQueryString(beginRequest);
    log.info("TencentWill start : {}", queryString);
    String faceId = uploadResult.getOrderNo();
    String faceUrl = "https://kyc1.qcloud.com/api/web/willLogin?" + queryString;
    // 此处做校验
//    requestResolver.resolveURI(uriStr);
    PocWillUploadResponse response = new PocWillUploadResponse(faceId, faceUrl);
    response.setProviderOrderNo(uploadResult.getOrderNo());
    return response;
  }


  private String getFrom(FaceAuthorizationInitializingContext initializingContext)
      throws FaceException {
    ConfigurableFaceProcessor processor =
        ConfigurableFaceProcessors.getProcessor(initializingContext.getRequest().getClientType());
    ClientApplicationType clientApplicationType = processor.getClientApplicationType();
    String from = CLIENT_APPLICATION_TYPE_STRING_MAPPER.get(clientApplicationType);
    if (null == from) {
      from = CLIENT_APPLICATION_TYPE_STRING_MAPPER.get(ClientApplicationType.TYPE_BROWSER);
    }
    return from;
  }

  private String getUrl(PocWillUploadRequest request)
      throws FaceException {
    try {
      return URLEncoder.encode(request.getReturnUrl(), StandardCharsets.UTF_8.name());
    } catch (Exception cause) {
      throw FaceException.valueOf(FaceStatusCode.RESOURCE_ERROR_ENCODE_URL, cause);
    }
  }
}
