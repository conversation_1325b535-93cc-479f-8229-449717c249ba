package com.timevale.faceauth.controller.poc;

import com.timevale.faceauth.service.utils.UUIDUtil;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 *
 * <AUTHOR>
 * @since 2020/2/27 上午11:27
 */
@ApiModel("刷脸用户信息上传请求")
@Data
public class PocWillUploadRequest extends ToString {

    public static final String channel_tencent  = "tencent";
    public static final String channel_megvii  = "megvii";
    public static final String channel_esign  = "esign";

    @ApiModelProperty(value = "供应商渠道，tencent=腾讯云  megvii=旷视 esign= e签宝域名", required = true)
    private String channel = "tencent";

    @ApiModelProperty(value = "姓名", required = true)
    private String name;

    @ApiModelProperty(value = "证件号", required = true)
    private String certNo;

    @ApiModelProperty(value = "回调地址", required = true)
    private String returnUrl;

    @ApiModelProperty(value = "意愿配置", required = true)
    private PocWillConfig config;


    private String faceId = UUIDUtil.generateUUID();




}
