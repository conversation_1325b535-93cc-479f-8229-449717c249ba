package com.timevale.faceauth.controller.poc;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/8/16 16:13
 */
@Data
@Accessors(chain = true)
public class MegviiWillInput {

    private String api_key = "";
    private String api_secret = "";
    private String return_url;
    private String notify_url;
    private String biz_no;

    private String procedure_type = "will";
    private String comparison_type = "1";
    private String idcard_mode = "0";
    private String idcard_name;
    private String idcard_number;
    //选择意愿认证模式
    //0：问答模式（默认值）
    //1：点头模式
    private String will_type = "0";
    private String will_config_mode = "1";
    private String will_config;
    private String scene_id = "will01";
    private String speed = "0";
    private String action_http_method = "GET";

}
