package com.timevale.faceauth.controller.wechat.face;

import com.timevale.faceauth.service.api.FaceProviderService;
import com.timevale.faceauth.service.input.WeChatFaceCheckBizTokenInput;
import com.timevale.faceauth.service.result.SupportResult;
import com.timevale.faceauth.service.result.WeChatFaceCheckBizTokenResult;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;

@RestMapping(path = "faceauth-service/wechat/face")
@ExternalService
@Api(tags = "微信刷脸认证刷脸前端API")
@Slf4j
public class WeChatFaceController {

  @Autowired private FaceProviderService faceProviderService;

  @RestMapping(path = "checkBizToken", method = RequestMethod.POST)
  @ApiOperation(value = "检测刷脸业务TOKEN状态", httpMethod = "POST")
  public BaseResult<WeChatFaceCheckBizTokenResult> checkBizToken(
      @RequestBody WeChatFaceCheckBizTokenInput tokenRequest) {

    SupportResult<WeChatFaceCheckBizTokenResult> supportResult =
        faceProviderService.checkWeChatBizToken(tokenRequest);
    if (supportResult.ifFail()) {
      return BaseResult.fail(supportResult.getCode(), supportResult.getMessage());
    }
    return BaseResult.success(supportResult.getData());
  }
}
