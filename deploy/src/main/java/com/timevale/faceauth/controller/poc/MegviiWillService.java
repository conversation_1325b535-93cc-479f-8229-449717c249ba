package com.timevale.faceauth.controller.poc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Objects;
import com.timevale.faceauth.service.utils.OkHttpClient;
import com.timevale.faceauth.service.utils.UUIDUtil;
import com.timevale.faceauth.service.utils.UrlUtil;
import com.timevale.middleware.log.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * https://faceid.com/document/faceid-guide-docs/adv_h5_get_token
 * <AUTHOR>
 * @since 2024/8/16 16:10
 */
@Slf4j
@Component
public class MegviiWillService {

    public PocWillUploadResponse initial(PocWillUploadRequest request) {

        Map<String,Object> willConfigMap = new HashMap();
        willConfigMap.put("will_config", JSON.toJSONString(request.getConfig().getQuestions()));
        MegviiWillInput input = new MegviiWillInput();
        input.setBiz_no(request.getFaceId());
        input.setReturn_url(request.getReturnUrl());
        input.setNotify_url("https://esign.cn");
        input.setIdcard_name(request.getName());
        input.setIdcard_number(request.getCertNo());
        input.setSpeed(request.getConfig().getSpeed());
        input.setWill_config(JSON.toJSONString(request.getConfig().getQuestions()));
        if(Objects.equal(request.getConfig().getQuestions().get(0).getAnswerMode(), "1")){
            input.setWill_type("1");
        }
        String uriStr = "https://api.megvii.com/faceid/lite/get_token";

        JSONObject json = JSON.parseObject(JSON.toJSONString(input));
        Map<String, String> params = new HashMap<>();
        for(String key : json.getInnerMap().keySet()){
            params.put(key, json.getString(key));
        }



//        HttpHeaders headers = new HttpHeaders();
//
//        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
//
//        HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<MultiValueMap<String, Object>>(params, headers);

        MegviiWillOut out = null;
        try {
            String result = OkHttpClient.postByForm(uriStr,params);
            out = JSON.parseObject(result, MegviiWillOut.class);
            log.info("MegviiWillService start : {}", out);
        } catch (IOException e) {
            e.printStackTrace();
        }
//        RestTemplate restTemplate = RestTemplateUtil.getRestTemplate();

//        ResponseEntity<MegviiWillOut> responseEntity = restTemplate.postForEntity(uriStr, httpEntity, MegviiWillOut.class);



        String domian = "api.megvii.com";
        if(Objects.equal(request.getChannel(), PocWillUploadRequest.channel_esign)) {
            domian = "huoshan-poc.tsign.cn";
        }
        String faceId = input.getBiz_no();
        String faceUrl = "https://"+domian+"/faceid/lite/do?token="+ out.getToken();

        PocWillUploadResponse response =  new PocWillUploadResponse(faceId, faceUrl);
        response.setProviderOrderNo(out.getBiz_id());
        return response;
    }

    public PocWillResultResponse getFesult(String providerOrderNo){
        MegviiWillInput input = new MegviiWillInput();
        String urlStr =  "https://api.megvii.com/faceid/lite/get_result";
        urlStr = UrlUtil.appendParam(urlStr,"api_key",input.getApi_key());
        urlStr = UrlUtil.appendParam(urlStr,"api_secret",input.getApi_secret());
        urlStr = UrlUtil.appendParam(urlStr,"biz_id",providerOrderNo);
        urlStr = UrlUtil.appendParam(urlStr,"return_verify_time","1");
        urlStr = UrlUtil.appendParam(urlStr,"return_image","1");
        PocWillResultResponse response = new PocWillResultResponse();
        try {
            String result = OkHttpClient.get(urlStr,new HashMap<>());
            JSONObject json = JSON.parseObject(result);
            String error_message = json.getString("error_message");
            if(StringUtils.isNoneBlank(error_message)){
                response.setCode("FAIL");
                response.setMessage(error_message);
                return response;
            }
            String status = json.getString("status");
            if(!Objects.equal(status, "OK")){
                response.setCode(status);
                response.setMessage(MegviiWillConstans.STATUS_MESSAGE_.get(status));
                return  response;
            }

            String will_result = "PASS";
            String will_error_message = "NON";
            JSONArray willJSON = json.getJSONArray("will_result");
            for(Object o : willJSON){
                JSONObject obj = (JSONObject)o;
                if(will_result.equals("PASS")){
                     continue;
                }
                will_result = obj.getString("result");
                will_error_message = obj.getString("will_error_message");
            }



            if(Objects.equal(status, "OK") && Objects.equal(will_result,"PASS")){
                response.setCode(will_result);
                response.setMessage("认证成功");
            }else if(Objects.equal(will_result,"FAIL")){
                response.setCode(will_result);
                response.setMessage(MegviiWillConstans.WILL_MESSAGE_.get(will_error_message));
            }
            log.info("MegviiWillService getFesult : {}", result);

        } catch (IOException e) {
            e.printStackTrace();
        }

        return response;
    }


    public static void main(String[] args) {

        PocWillConfig config = new PocWillConfig();
        config.setSpeed("1");

        PocWillQuestion question = new PocWillQuestion();
        question.setQuestion("您好，为确保您本人操作，此次签约全程录音录像。请问您本次业务是本人自愿办理吗？请回答：我确认");
        question.setAnswer("我确认");

        config.setQuestions(new ArrayList<>());
        config.getQuestions().add(question);

        PocWillUploadRequest request  = new PocWillUploadRequest();
        request.setName("许华建");
        request.setCertNo("342622198905262396");
        request.setReturnUrl("https://baidu.com");
        request.setChannel("esign");
        request.setConfig(config);

        MegviiWillService willService = new MegviiWillService();
        PocWillUploadResponse response = willService.initial(request);
        System.out.println(JSON.toJSONString(response));

        PocWillResultResponse resultResponse = willService.getFesult(response.getProviderOrderNo());
        System.out.println(JSON.toJSONString(resultResponse));
    }
}
