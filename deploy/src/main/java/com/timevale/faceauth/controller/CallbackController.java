package com.timevale.faceauth.controller;

import com.timevale.component.identity.constant.servlet.RedirectResponseResolver;
import com.timevale.faceauth.service.bean.FaceCallbackInfo;
import com.timevale.faceauth.service.impl.api.DefaultFaceAuthorizationService;
import com.timevale.faceauth.service.inner.FaceAuthExtService;
import com.timevale.faceauth.service.input.FaceAuthResInput;
import com.timevale.faceauth.service.result.SupportResult;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import org.apache.commons.httpclient.URI;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/** <AUTHOR> on 2018/9/5 */
@ExternalService
@RestMapping(path = "/tsign")
public class CallbackController {

  private static final Logger logger = LoggerFactory.getLogger(CallbackController.class);

  @Autowired private FaceAuthExtService faceAuthExtService;

  @Autowired private DefaultFaceAuthorizationService faceAuthService;

  @RestMapping(
      path = "/face/alipaycallback",
      method = {RequestMethod.POST,RequestMethod.GET})
  public void aliPayFaceCallback(
      HttpServletRequest request,
      HttpServletResponse response)
      throws IOException {
    PrintWriter printWriter = response.getWriter();
    printWriter.println("success");
    printWriter.flush();
    printWriter.close();
  }

    @RestMapping(
      path = "/face/callback/{faceAuthId}/{transactionId}",
      method = {RequestMethod.GET})
  public void faceCallback(
      @PathVariable("faceAuthId") String faceAuthId,
      @PathVariable("transactionId") String transactionId,
      HttpServletRequest request,
      HttpServletResponse response)
      throws IOException {

    logger.info("{} face complete , transactionId {}", faceAuthId, transactionId);

    transactionId = transactionId.replace("_", ".");

    FaceCallbackInfo callbackInfo = faceAuthExtService.getFaceCallbackUrl(faceAuthId);

    FaceAuthResInput input = new FaceAuthResInput();
    input.setFaceAuthId(faceAuthId);
    input.setNeedFile(true);

    SupportResult supportResult = faceAuthService.handleFaceResult(input, transactionId);
    if (supportResult.ifFail()) {
      String msg = "faceCallback request is error ,faceAuthId::" + faceAuthId;
      logger.warn("face auth callback exception :{}", supportResult.getMessage());
      response.getWriter().println(msg);
      response.getWriter().close();
      return;
    }

    String callbackUrl = callbackInfo.getCallbackUrl();

    // 在上层的回调地址里 加上transactionId参数
    URI uri = new URI(callbackUrl, false);
    String query = uri.getQuery();
    if (StringUtils.isNotBlank(query)) {
      query = query + "&transactionId=" + transactionId;
    } else {
      query = "transactionId=" + transactionId;
    }
    uri.setQuery(query);

    callbackUrl = uri.toString();

    logger.info("face callback url : {}", callbackUrl);

    // 302 处理 使用webserver转发
    RedirectResponseResolver.deduceRedirectResponse(request, response, callbackUrl);
  }
}
