package com.timevale.faceauth.controller.poc;

import com.timevale.faceauth.service.core.support.ArgumentUtil;

import java.util.UUID;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/18 18
 */
class TencentWillCertificationInitializationBeginningRequest
    extends TencentWillCertificationSignableRequest {

  static final String TYPE_FROM_APP = "App";
  static final String TYPE_FROM_BROWSER = "browser";


  /** 随机数：32位随机串（字母+数字组成的随机数） */
  private final String nonce;
  /** h5/geth5faceid 接口返回的唯一标识 */
  private final String faceId;
  /**
   * H5 人脸核身完成后回调的第三方 URL，需要第三方提供完整 URL 且做 URL Encode 完整 URL Encode 示例： 原 URL
   * 为`https://cloud.tencent.com` Encode 后为`https%3a%2f%2fcloud.tencent.com`
   */
  private final String url;
  /** 是否显示结果页面 参数值为“1”：直接跳转到 url 回调地址 null 或其他值：跳转提供的结果页面 */
  private final String resultType;
  /** 用户 ID，用户的唯一标识（不要带有特殊字符） */
  private final String userId;

  /** 页面主题颜色 */
  private final String theme;
  /** browser：表示在浏览器启动刷脸 App：表示在 App 里启动刷脸， 默认值为 App */
  private final String from;
  /** 跳转模式， 参数值为“1”时，刷脸页面使用 replace 方式跳转，不在浏览器 history 中留下记录； 不传或其他值则正常跳转 */
  private final String redirectType;

  private final String appId;
  private final String orderNo;
  private final String ticket;

  private TencentWillCertificationInitializationBeginningRequest(
      TencentCloudCertificationInitializationBeginningRequestBuilder builder) {
    super(builder);
    this.nonce = builder.nonce;
    this.faceId = builder.faceId;
    this.url = builder.url;
    this.resultType = builder.resultType;
    this.userId = builder.userId;
    this.theme = builder.theme;
    this.from = builder.from;
    this.redirectType = builder.redirectType;
    this.appId = builder.appId;
    this.orderNo = builder.orderNo;
    this.ticket = builder.ticket;
  }

  public String getNonce() {
    return nonce;
  }

  public String getFaceId() {
    return faceId;
  }

  public String getUrl() {
    return url;
  }

  public String getResultType() {
    return resultType;
  }

  public String getUserId() {
    return userId;
  }

  public String getTheme() {
    return theme;
  }

  public String getFrom() {
    return from;
  }

  public String getRedirectType() {
    return redirectType;
  }

  public String getAppId() {
    return appId;
  }

  public String getOrderNo() {
    return orderNo;
  }


  String obtainTicket() {
    return ticket;
  }

  static TencentCloudCertificationInitializationBeginningRequestBuilder createBuilder() {
    return (new TencentCloudCertificationInitializationBeginningRequestBuilder());
  }

  static class TencentCloudCertificationInitializationBeginningRequestBuilder
      extends TencentCloudCertificationSignableRequestBuilder<
          TencentWillCertificationInitializationBeginningRequest,
          TencentCloudCertificationInitializationBeginningRequestBuilder> {

    private String nonce = (UUID.randomUUID().toString().replace("-", ""));
    private String faceId;
    private String url;
    private String resultType = "1";
    private String userId;
    private String theme;
    private String from = TYPE_FROM_APP;
    private String redirectType = "1";
    private String appId;
    private String orderNo;
    private String ticket;

    @Override
    TencentWillCertificationInitializationBeginningRequest build() {
      return (new TencentWillCertificationInitializationBeginningRequest(this));
    }

    @Override
    protected TencentCloudCertificationInitializationBeginningRequestBuilder self() {
      return this;
    }

    TencentCloudCertificationInitializationBeginningRequestBuilder setNonce(String nonce) {
      ArgumentUtil.throwIfEmptyArgument(nonce, "nonce");
      this.nonce = nonce;
      return this;
    }

    TencentCloudCertificationInitializationBeginningRequestBuilder setFaceId(String faceId) {
      ArgumentUtil.throwIfEmptyArgument(faceId, "faceId");
      this.faceId = faceId;
      return this;
    }

    TencentCloudCertificationInitializationBeginningRequestBuilder setUrl(String url) {
      ArgumentUtil.throwIfEmptyArgument(url, "url");
      this.url = url;
      return this;
    }

    TencentCloudCertificationInitializationBeginningRequestBuilder setResultType(
        String resultType) {
      this.resultType = resultType;
      return this;
    }

    TencentCloudCertificationInitializationBeginningRequestBuilder setUserId(String userId) {
      ArgumentUtil.throwIfEmptyArgument(userId, "userId");
      this.userId = userId;
      return this;
    }
    TencentCloudCertificationInitializationBeginningRequestBuilder setTheme(String theme) {
      //可以为空
      this.theme = theme;
      return this;
    }

    TencentCloudCertificationInitializationBeginningRequestBuilder setFrom(String from) {
      ArgumentUtil.throwIfEmptyArgument(from, "from");
      this.from = from;
      return this;
    }

    TencentCloudCertificationInitializationBeginningRequestBuilder setRedirectType(
        String redirectType) {
      this.redirectType = redirectType;
      return this;
    }

    TencentCloudCertificationInitializationBeginningRequestBuilder setAppId(String appId) {
      ArgumentUtil.throwIfEmptyArgument(appId, "appId");
      this.appId = appId;
      return this;
    }

    TencentCloudCertificationInitializationBeginningRequestBuilder setOrderNo(String orderNo) {
      ArgumentUtil.throwIfEmptyArgument(orderNo, "orderNo");
      this.orderNo = orderNo;
      return this;
    }

    TencentCloudCertificationInitializationBeginningRequestBuilder setTicket(String ticket) {
      ArgumentUtil.throwIfEmptyArgument(ticket, "ticket");
      this.ticket = ticket;
      return this;
    }
  }
}
