package com.timevale.faceauth.controller.poc;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/8/19 10:25
 */
@Data
public class PocWillQuestion {


    @ApiModelProperty(value = "播报问题", required = true)
    private String question;

    @ApiModelProperty(value = "选择意愿认证模式\n" +
            "0：问答模式（默认值）\n" +
            "1：点头模式", required = true)
    @JSONField(serialize = false, deserialize = false)
    private String answerMode;

    @ApiModelProperty(value = "问答", required = true)
    private String answer;
}
