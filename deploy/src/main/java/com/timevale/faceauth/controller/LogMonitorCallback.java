package com.timevale.faceauth.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.timevale.faceauth.service.apispace.service.com.timevale.apispace.domain.monitor.alert.AlertEngine;
import com.timevale.faceauth.service.apispace.service.com.timevale.apispace.domain.monitor.alert.AlertSupport;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMethod;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
@Slf4j
@Api(tags = "回调")
@ExternalService
@RestMapping(path = "/faceauth")
public class LogMonitorCallback {

  @Autowired private AlertSupport alertSupport;
  @Autowired private AlertEngine alertEngine;

  /** 供应商监控触发告警后回调地址 */
  @ApiOperation(value = "供应商监控触发告警后回调地址", httpMethod = "POST")
  @RestMapping(
      path = "/callback/monitor",
      method = {RequestMethod.POST})
  public void monitorCallback(String content) {
    log.info("monitorCallback : {}", content);

    if (StringUtils.isBlank(content)) return;

    JSONObject jsonObject = JSON.parseObject(content);
    String condition = jsonObject.getString("condition");
    JSONObject annotations = jsonObject.getJSONObject("annotations");

    if (null == annotations || StringUtils.isBlank(condition)) {
      return;
    }

    String providerId = annotations.getString("providerId");
    if (StringUtils.isBlank(providerId)) {
      return;
    }

    String alertId = alertSupport.deduceAlertId(providerId, condition);
    if (StringUtils.isNotBlank(alertId)) {
      // 执行告警处理器
      alertEngine.start(providerId, alertId);
    }
  }
}
