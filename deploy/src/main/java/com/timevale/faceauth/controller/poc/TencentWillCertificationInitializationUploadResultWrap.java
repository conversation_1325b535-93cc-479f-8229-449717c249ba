package com.timevale.faceauth.controller.poc;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/18 18
 */
class TencentWillCertificationInitializationUploadResultWrap {

  private final String input;
  private final TencentWillCertificationInitializationUploadResult result;

  TencentWillCertificationInitializationUploadResultWrap(
          String input, TencentWillCertificationInitializationUploadResult result) {
    this.input = input;
    this.result = result;
  }

  public String getInput() {
    return input;
  }

  public TencentWillCertificationInitializationUploadResult getResult() {
    return result;
  }
}
