package com.timevale.faceauth.deploy;

import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.concurrent.Callable;
import java.util.concurrent.Future;

public class ContextAwarePoolExecutor extends ThreadPoolTaskExecutor {

    private static final long serialVersionUID = 667815067287186086L;

    @Override
    public void execute(Runnable task) {
        super.execute(new ContextAwareRunable(task, ThreadContextContainer.newInstance()));
    }

    @Override
    public <T> Future<T> submit(Callable<T> task) {
        return super.submit(new ContextAwareCallable<T>(task, ThreadContextContainer.newInstance()));
    }

    @Override
    public ListenableFuture<?> submitListenable(Runnable task) {
        return super.submitListenable(
                new ContextAwareRunable(task, ThreadContextContainer.newInstance()));
    }

    @Override
    public <T> ListenableFuture<T> submitListenable(Callable<T> task) {
        return super.submitListenable(
                new ContextAwareCallable<T>(task, ThreadContextContainer.newInstance()));
    }
}
