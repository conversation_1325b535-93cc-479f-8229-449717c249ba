package com.timevale.faceauth.deploy;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.io.Serializable;
import java.util.Map;

@Slf4j
public class ThreadContextContainer implements Serializable {

    private static final long serialVersionUID = -6809291915300091330L;

    private RequestAttributes requestAttributes;
    private Map<String, String> contextMapOfMDC;

    public RequestAttributes getRequestAttributes() {
        return requestAttributes;
    }

    public Map<String, String> getContextMapOfMDC() {
        return contextMapOfMDC;
    }

    public void setRequestAttributes(RequestAttributes requestAttributes) {
        this.requestAttributes = requestAttributes;
    }

    public void setContextMapOfMDC(Map<String, String> contextMapOfMDC) {
        this.contextMapOfMDC = contextMapOfMDC;
    }

    /** set infos what we need */
    public static ThreadContextContainer newInstance() {
        ThreadContextContainer container = new ThreadContextContainer();
        try {
            container.setRequestAttributes(RequestContextHolder.currentRequestAttributes());
        } catch (Exception e) {
            log.warn("thread set ThreadContextContainer failed, may be no request");
        }
        container.setContextMapOfMDC(MDC.getCopyOfContextMap());
        return container;
    }
}
