package com.timevale.faceauth.deploy;

import org.slf4j.MDC;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.util.Map;

public class ContextAwareRunable implements Runnable {

        /** the original task */
        private Runnable task;

        /** for storing infos what we need */
        private ThreadContextContainer threadContextContainer;

        public ContextAwareRunable(
                Runnable task, ThreadContextContainer threadContextContainer) {
            this.task = task;
            this.threadContextContainer = threadContextContainer;
        }

        @Override
        public void run() {
            // set infos
            if (threadContextContainer != null) {
                Map<String, String> contextMapOfMDC = threadContextContainer.getContextMapOfMDC();
                if (contextMapOfMDC != null) {
                    MDC.setContextMap(contextMapOfMDC);
                }
                RequestAttributes requestAttributes = threadContextContainer.getRequestAttributes();
                if (requestAttributes != null) {
                    RequestContextHolder.setRequestAttributes(requestAttributes);
                }
            }

            try {
                // execute the original task
                task.run();
            } finally {
                // clear infos after task completed
                RequestContextHolder.resetRequestAttributes();
                try {
                    MDC.clear();
                } finally {
                }
            }
        }
    }