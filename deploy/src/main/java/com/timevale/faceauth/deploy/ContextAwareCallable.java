package com.timevale.faceauth.deploy;

import org.slf4j.MDC;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.util.Map;
import java.util.concurrent.Callable;

public class ContextAwareCallable<T> implements Callable<T> {

        /** the original task */
        private Callable<T> task;

        /** for storing infos what we need */
        private ThreadContextContainer threadContextContainer;

        public ContextAwareCallable(
                Callable<T> task, ThreadContextContainer threadContextContainer) {
            this.task = task;
            this.threadContextContainer = threadContextContainer;
        }

        @Override
        public T call() throws Exception {
            // set infos
            if (threadContextContainer != null) {
                RequestAttributes requestAttributes = threadContextContainer.getRequestAttributes();
                if (requestAttributes != null) {
                    RequestContextHolder.setRequestAttributes(requestAttributes);
                }
                Map<String, String> contextMapOfMDC = threadContextContainer.getContextMapOfMDC();
                if (contextMapOfMDC != null) {
                    MDC.setContextMap(contextMapOfMDC);
                }
            }

            try {
                // execute the original task
                return task.call();
            } finally {
                // clear infos after task completed
                RequestContextHolder.resetRequestAttributes();
                try {
                    MDC.clear();
                } finally {
                }
            }
        }
    }