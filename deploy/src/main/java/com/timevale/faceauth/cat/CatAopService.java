//package com.timevale.faceauth.cat;
//
//import esign.cat.integration.CatAnnotation;
//import esign.cat.integration.CatLogUtil;
//import esign.cat.integration.env.CatInitialization;
//import lombok.extern.slf4j.Slf4j;
//import org.aspectj.lang.ProceedingJoinPoint;
//import org.aspectj.lang.annotation.Around;
//import org.aspectj.lang.annotation.Aspect;
//import org.aspectj.lang.annotation.Pointcut;
//import org.springframework.core.annotation.Order;
//import org.springframework.stereotype.Component;
//
///** <AUTHOR> modified by Master T */
////@Aspect
////@Component
//@Order(0)
//@Slf4j
//public class CatAopService extends CatInitialization {
//  @Pointcut(
//          value =
//                  " execution(* com.timevale.open.platform.service..*.*(..))"
//                          + "|| execution(* com.timevale.footstone.antforest.common.rpc.api..*.*(..))"
//                          + "|| execution(* com.timevale.filesystem.common.service.api..*.*(..))"
//                          + "|| execution(* com.timevale.facedetectalgo.service..*.*(..))"
//  )
//  protected void remoteServicePointcut() {
//  }
//
//  @Pointcut("execution(* com.timevale.faceauth..*.*(..))")
//  protected void facePC() {
//  }
//
//  ;
//
//  /**
//   * joinPoint for service layer
//   */
//  @Pointcut(
//          "@within(org.springframework.stereotype.Service)"
//                  + "&& execution(* com.timevale.faceauth..*.*(..))")
//  protected void servicePointcut() {
//  }
//
//  /**
//   * joinPoint for component layer
//   */
//  @Pointcut(
//          "@within(org.springframework.stereotype.Component)"
//                  + "&& !@within(esign.cat.integration.CatAnnotation)"
//                  + "&& execution(* com.timevale.faceauth..*.*(..))")
//  protected void componentPointcut() {
//  }
//
//  @Pointcut(
//          "@within(com.timevale.mandarin.common.annotation.RestService)")
//  protected void restPointCut() {
//  }
//
//  /**
//   * 自定义的切面
//   */
//  @Pointcut(
//          "@within(esign.cat.integration.CatAnnotation)||@annotation(esign.cat.integration.CatAnnotation)")
//  protected void customizePointcut() {
//  }
//
//  @Around("servicePointcut()")
//  public Object serviceAround(ProceedingJoinPoint pjp) throws Throwable {
//    return CatLogUtil.processor(pjp, "Service");
//  }
//
//  @Around("componentPointcut()")
//  public Object componentAround(ProceedingJoinPoint pjp) throws Throwable {
//    return CatLogUtil.processor(pjp, "Component");
//  }
//
//  @Around("restPointCut() && facePC()")
//  public Object restServiceAround(ProceedingJoinPoint pjp) throws Throwable {
//    return CatLogUtil.processor(pjp, "RestService");
//  }
//
//  @Around("remoteServicePointcut()")
//  public Object remoteServiceAround(ProceedingJoinPoint pjp) throws Throwable {
//    return CatLogUtil.processor(pjp, "Call");
//  }
//
//  @Around("customizePointcut() && facePC()")
//  public Object customizeAround(ProceedingJoinPoint pjp) throws Throwable {
//    CatAnnotation annotation = pjp.getTarget().getClass().getAnnotation(CatAnnotation.class);
//    String name = annotation.value();
//    return CatLogUtil.processor(pjp, name);
//  }
//}
