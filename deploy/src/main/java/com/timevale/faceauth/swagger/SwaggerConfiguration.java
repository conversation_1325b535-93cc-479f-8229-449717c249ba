package com.timevale.faceauth.swagger;

import com.google.common.base.Predicate;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.DefaultServletHandlerConfigurer;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;
import springfox.documentation.RequestHandler;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * https://blog.csdn.net/sinat_27639721/article/details/81218551
 * https://github.com/springfox/springfox/issues/1080
 */
@Configuration
@EnableSwagger2
public class SwaggerConfiguration extends WebMvcConfigurerAdapter {

  private final String ROOT = "/faceauth";
  private final String SWAGGER_SCAN_BASE_PACKAGE = "com.timevale.faceauth";

  /**
   * 项目里的静态资源路径指向如下
   *
   * <AUTHOR>
   * @since 2018/12/13 下午1:21
   */
  @Override
  public void addResourceHandlers(ResourceHandlerRegistry registry) {

//    registry
//        .addResourceHandler(ROOT + "/**")
//        .addResourceLocations("classpath:/META-INF/resources/");
  }

  @Override
  public void addViewControllers(ViewControllerRegistry registry) {
    registry.addRedirectViewController(ROOT + "/v2/api-docs", "/v2/api-docs");
    registry.addRedirectViewController(ROOT + "/configuration/ui", "/configuration/ui");
    registry.addRedirectViewController(ROOT + "/configuration/security", "/configuration/security");
    registry.addRedirectViewController(ROOT + "/swagger-resources", "/swagger-resources");
    registry.addRedirectViewController(ROOT + "", ROOT + "/swagger-ui.html");
    registry.addRedirectViewController(ROOT + "/", ROOT + "/swagger-ui.html");
    registry.addRedirectViewController("", ROOT + "/swagger-ui.html");
    registry.addRedirectViewController("/", ROOT + "/swagger-ui.html");
  }

  @Override
  public void configureDefaultServletHandling(DefaultServletHandlerConfigurer configurer) {
    configurer.enable();
  }

  @Bean
  public Docket createRestApi() {

    Predicate<RequestHandler> predicate = (input) -> true;

    Docket docket =
        new Docket(DocumentationType.SWAGGER_2)
            .apiInfo(apiInfo())
            .select()
            .apis(RequestHandlerSelectors.basePackage(SWAGGER_SCAN_BASE_PACKAGE))
            .apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
            .apis(predicate)
            .paths(SwaggerPathSelectors.any())
            .build();

    return docket;
  }

  private ApiInfo apiInfo() {

    return new ApiInfoBuilder()
        .title("faceauth online doc")
        .description("faceauth online doc")
        .termsOfServiceUrl("https://www.tsign.cn/")
        .contact(new Contact("", "", "<EMAIL>"))
        .version("1.0")
        .build();
  }
}
