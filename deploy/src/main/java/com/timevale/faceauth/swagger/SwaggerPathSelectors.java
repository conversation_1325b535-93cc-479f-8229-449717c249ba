package com.timevale.faceauth.swagger;

import com.google.common.base.Predicate;
import com.google.common.base.Predicates;
import org.springframework.util.AntPathMatcher;

/**
 * <AUTHOR>
 * @since 2018/12/13 下午6:08
 */
public class SwaggerPathSelectors {

  private static int count = 0;

  private SwaggerPathSelectors() {
    throw new UnsupportedOperationException();
  }

  /**
   * Any path satisfies this condition
   *
   * @return predicate that is always true
   */
  public static Predicate<String> any() {
    return Predicates.alwaysTrue();
  }

  /**
   * No path satisfies this condition
   *
   * @return predicate that is always false
   */
  public static Predicate<String> none() {
    return Predicates.alwaysFalse();
  }

  /**
   * Predicate that evaluates the supplied regular expression
   *
   * @param pathRegex - regex
   * @return predicate that matches a particular regex
   */
  public static Predicate<String> regex(final String pathRegex) {
    return (input) -> {
      boolean flag = input.matches(pathRegex);
      if (flag) {
        count++;
      }
      return flag;
    };
  }

  /**
   * Predicate that evaluates the supplied ant pattern
   *
   * @param antPattern - ant Pattern
   * @return predicate that matches a particular ant pattern
   */
  public static Predicate<String> ant(final String antPattern) {
    return (input) -> {
      boolean flag = new AntPathMatcher().match(antPattern, input);
      if (flag) {
        count++;
      }
      return flag;
    };
  }

  public static int getCount() {
    return count;
  }
}
