package com.timevale.faceauth.dao;

import com.timevale.faceauth.AbstractTest;
import com.timevale.faceauth.dal.pfs.dao.SdkCustomerDAO;
import com.timevale.faceauth.dal.pfs.dataobject.SdkCustomerDO;
import com.timevale.faceauth.dal.pfs.dataobject.query.SdkCustomerDOQuery;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.annotations.Test;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2024/8/6 18:00
 */

public class SdkCustomerDAOTest extends AbstractTest {

    @Autowired
    private SdkCustomerDAO sdkCustomerDAO;

    @Test

    public void test() {

        SdkCustomerDOQuery query = new SdkCustomerDOQuery();
        query.setChannelCodeList(Lists.newArrayList("c"));
        List<SdkCustomerDO> page = sdkCustomerDAO.page(query);
        Long count = sdkCustomerDAO.count(query);
        System.out.println(page);
        System.out.println(count);

        query.setChannelCodeList(Lists.newArrayList("a"));
        page = sdkCustomerDAO.page(query);
        count = sdkCustomerDAO.count(query);
        System.out.println(page);
        System.out.println(count);
    }
}
