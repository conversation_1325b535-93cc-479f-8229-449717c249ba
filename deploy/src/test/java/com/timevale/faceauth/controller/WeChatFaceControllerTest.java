package com.timevale.faceauth.controller;

import com.timevale.faceauth.AbstractTest;
import com.timevale.faceauth.controller.wechat.face.WeChatFaceController;
import com.timevale.faceauth.service.input.WeChatFaceCheckBizTokenInput;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.Assert;
import org.testng.annotations.Test;


public class WeChatFaceControllerTest extends AbstractTest {

  @Autowired private WeChatFaceController faceController;

  @Test
  public void checkBizToken() {

    WeChatFaceCheckBizTokenInput tokenRequest1 = new WeChatFaceCheckBizTokenInput();
    tokenRequest1.setBizToken("CF806EC8-13FD-4BDD-9CB6-A182882CC503");
    Assert.assertFalse(faceController.checkBizToken(tokenRequest1).getData().isCompleted());

    WeChatFaceCheckBizTokenInput tokenRequest2 = new WeChatFaceCheckBizTokenInput();
    tokenRequest1.setBizToken("035935AF-D8F3-4AAD-9CCF-B4A2AAD28436");
    Assert.assertTrue(faceController.checkBizToken(tokenRequest1).getData().isCompleted());
  }
}
