package com.timevale.faceauth.mock;

import com.google.common.collect.Lists;
import com.timevale.faceauth.dal.pfs.dataobject.FaceUserResourceControlRecordDO;
import com.timevale.faceauth.dal.pfs.dataobject.query.FaceUserResourceControlRecordQuery;
import com.timevale.faceauth.service.domain.controll.FaceResourceControlStatusEnum;
import com.timevale.faceauth.service.impl.api.FaceUserResourceControlServiceImpl;
import com.timevale.faceauth.service.inner.FaceUserResourceControlInnerService;
import com.timevale.faceauth.service.input.control.ControlRecordInput;
import com.timevale.faceauth.service.input.control.ControlRecordPageInput;
import com.timevale.faceauth.service.input.control.ControlRecordQueryDetailInput;
import com.timevale.faceauth.service.input.control.ControlRecordSaveInput;
import com.timevale.faceauth.service.input.control.SwitchStatusControlRecordInput;
import com.timevale.faceauth.service.result.BaseFaceAuthResult;
import com.timevale.faceauth.service.result.SupportResult;
import com.timevale.faceauth.service.result.control.ControlRecordPageResult;
import com.timevale.faceauth.service.result.control.ControlRecordResult;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.Date;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.testng.Assert.assertNotNull;

@RunWith(MockitoJUnitRunner.class)
public class FaceUserResourceControlServiceImplTest   {

    @InjectMocks
    private FaceUserResourceControlServiceImpl faceUserResourceControlService;

    @Mock
    private FaceUserResourceControlInnerService faceUserResourceControlInnerService;

    @BeforeClass
    public void setUp() {
        // 旧版 Mockito 宽松模式设置方式
        Mockito.reset(); // 清除之前的所有mock

        // 初始化所有@Mock注解
        MockitoAnnotations.initMocks(this);

        // 设置宽松验证（适用于旧版）
        Mockito.validateMockitoUsage();
    }

    @Test
    public void testBatchSaveControlRecord() {
        ControlRecordSaveInput input = new ControlRecordSaveInput();
        input.setAppIds(Lists.newArrayList("1"));
        input.setControlType("1");
        input.setStatus("1");
        input.setName("1");
        input.setIdNo("1");
        input.setIdType("1");
        input.setMobileNo("13123919602");
        input.setRemark("1");

        ControlRecordInput.ProofDocumentDTO proofDocumentDTO = getProofDocumentDTO();

        input.setProofDocumentList(Lists.newArrayList(proofDocumentDTO));
        input.setOperator("xx");
        input.setCreateTime(new Date());
        input.setModifyTime(new Date());



        // 设置 input 的字段值

        SupportResult<BaseFaceAuthResult> result = faceUserResourceControlService.batchSaveControlRecord(input);

        assertNotNull(result);
    }

    private static ControlRecordInput.ProofDocumentDTO getProofDocumentDTO() {
        ControlRecordInput.ProofDocumentDTO proofDocumentDTO = new ControlRecordInput.ProofDocumentDTO();
        proofDocumentDTO.setFileKey("x");
        return proofDocumentDTO;
    }

    @Test
    public void testDetailControlRecord() {
        ControlRecordQueryDetailInput input = new ControlRecordQueryDetailInput();
        input.setRecordId("record123");

        FaceUserResourceControlRecordDO recordDO = new FaceUserResourceControlRecordDO();
        // 设置 recordDO 的字段值

        when(faceUserResourceControlInnerService.selectOneByRecordId("record123")).thenReturn(recordDO);

        SupportResult<ControlRecordResult> result = faceUserResourceControlService.detailControlRecord(input);

        assertNotNull(result);
        assertNotNull(result.getData());
        verify(faceUserResourceControlInnerService, times(1)).selectOneByRecordId("record123");
    }

    @Test
    public void testModifyControlRecord() {
        ControlRecordInput input = new ControlRecordInput();
        input.setRecordId("record123");
        input.setAppId("appId123");
        input.setMobileNo("13123919602");

        input.setControlType("1");
        input.setStatus("1");
        input.setName("1");
        input.setIdNo("1");
        input.setIdType("1");
        input.setMobileNo("13123919602");
        input.setRemark("1");
        // 设置其他字段
        ControlRecordInput.ProofDocumentDTO proofDocumentDTO = getProofDocumentDTO();

        input.setProofDocumentList(Lists.newArrayList(proofDocumentDTO));
        input.setOperator("xx");
        doNothing().when(faceUserResourceControlInnerService).modifyControlRecord(any(FaceUserResourceControlRecordDO.class));

        SupportResult<BaseFaceAuthResult> result = faceUserResourceControlService.modifyControlRecord(input);

        assertNotNull(result);
        verify(faceUserResourceControlInnerService, times(1)).modifyControlRecord(any(FaceUserResourceControlRecordDO.class));
    }

    @Test
    public void testPageControlRecord() {
        ControlRecordPageInput input = new ControlRecordPageInput();
        // 设置 input 的字段值
        input.setPageNum(1);
        input.setPageSize(1);

        FaceUserResourceControlRecordQuery query = new FaceUserResourceControlRecordQuery();
        query.setPageNum(1);
        query.setPageSize(1);
        // 设置 query 的字段值

        ControlRecordPageResult pageResult = new ControlRecordPageResult();
        // 设置 pageResult 的字段值

        when(faceUserResourceControlInnerService.pageControlRecord(query)).thenReturn(pageResult);

        SupportResult<ControlRecordPageResult> result = faceUserResourceControlService.pageControlRecord(input);

        assertNotNull(result);
        assertNotNull(result.getData());
        verify(faceUserResourceControlInnerService, times(1)).pageControlRecord(query);
    }

    @Test
    public void testSwitchStatusControlRecord() {
        SwitchStatusControlRecordInput input = new SwitchStatusControlRecordInput();
        input.setStatus(FaceResourceControlStatusEnum.DISENABLE.name());
        input.setRecordId("1");

        // 设置 input 的字段值

        doNothing().when(faceUserResourceControlInnerService).switchStatusControlRecord(input);

        SupportResult<BaseFaceAuthResult> result = faceUserResourceControlService.switchStatusControlRecord(input);

        assertNotNull(result);
        verify(faceUserResourceControlInnerService, times(1)).switchStatusControlRecord(input);
    }
}
