package com.timevale.faceauth.mock;

import com.timevale.faceauth.dal.pfs.dao.FaceUserResourceControlRecordDAO;
import com.timevale.faceauth.dal.pfs.dataobject.FaceUserResourceControlRecordDO;
import com.timevale.faceauth.dal.pfs.face.support.FaceDO;
import com.timevale.faceauth.service.domain.controll.FaceResourceControlStatusEnum;
import com.timevale.faceauth.service.domain.controll.FaceUserResourceControlCheckDTO;
import com.timevale.faceauth.service.domain.repository.FaceInfo;
import com.timevale.faceauth.service.domain.repository.FaceRepository;
import com.timevale.faceauth.service.inner.impl.control.FaceUserResourceControlInnerServiceImpl;
import com.timevale.faceauth.service.input.control.ControlRecordSaveInput;
import com.timevale.faceauth.service.input.control.FaceUserResourceControlConsumerInput;
import com.timevale.faceauth.service.input.control.SwitchStatusControlRecordInput;
import com.timevale.faceauth.service.result.control.ControlFaceDataPrivilegeResult;
import com.timevale.framework.sands.Sahara;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.util.Collections;
import java.util.Date;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.when;
import static org.testng.AssertJUnit.assertNotNull;

@RunWith(MockitoJUnitRunner.class)
public class FaceUserResourceControlInnerServiceImpl2Test {

    @InjectMocks
    private FaceUserResourceControlInnerServiceImpl faceUserResourceControlInnerService;

    @Mock
    private FaceUserResourceControlRecordDAO controlRecordDAO;

    @Mock
    private FaceRepository faceRepository;

    @Mock
    private Sahara sahara;

    @BeforeMethod
    public void setUp() {
        // 旧版 Mockito 宽松模式设置方式
        Mockito.reset(); // 清除之前的所有mock

        // 初始化所有@Mock注解
        MockitoAnnotations.initMocks(this);

        // 设置宽松验证（适用于旧版）
        Mockito.validateMockitoUsage();
    }

    @Test
    public void testBatchSaveControlRecord_success() {
        // Arrange
        ControlRecordSaveInput input = new ControlRecordSaveInput();
        input.setAppIds(Collections.singletonList("testAppId"));
        input.setName("testName");
        input.setIdNo("123456");
        input.setIdType("ID_CARD");

        String recordId = "cr123456";
        when(sahara.getHexSand()).thenReturn("123456");

        FaceUserResourceControlRecordDO expectedRecord = new FaceUserResourceControlRecordDO();
        expectedRecord.setAppId("testAppId");
        expectedRecord.setName("testName");
        expectedRecord.setIdNo("123456");
        expectedRecord.setIdType("ID_CARD");
        expectedRecord.setRecordId(recordId);
        expectedRecord.setStatus("REVOKE_AGREE");
        expectedRecord.setControlType("APPID");

        // Mock insert behavior
//        Mockito.doNothing().when(controlRecordDAO).insert(expectedRecord);

        // Act
        faceUserResourceControlInnerService.batchSaveControlRecord(input);
        Assert.assertNotNull(expectedRecord);
        // Assert
    }

//    @Test
//    public void testCheckUserResourceControl_noRevokeRecord_returnsEmptyResult() {
//        // Arrange
//        FaceUserResourceControlCheckDTO input = new FaceUserResourceControlCheckDTO();
//        input.setFaceId("face123");
//
//        FaceInfo faceInfo = new FaceInfo();
//        faceInfo.setAppId("testAppId");
//        faceInfo.setName("testName");
//        faceInfo.setIdNo("123456");
//        faceInfo.setIdType("ID_CARD");
//        Mockito.when(faceRepository.getFaceInfoByFaceId("face123")).thenReturn(faceInfo);
//
//        Mockito.when(controlRecordDAO.selectOne(Mockito.any(FaceUserResourceControlRecordDO.class))).thenReturn(null);
//
//        // Act
//        ControlFaceDataPrivilegeResult result = faceUserResourceControlInnerService.checkUserResourceControl(input);
//
//        // Assert
//        Assert.assertNull(result.getRefusePhotoAndVideoRecordId());
//    }

    @Test
    public void testRepeatAgree_withExistingRevokeRecord_createsNewRecord() {
        // Arrange
        FaceUserResourceControlConsumerInput input = new FaceUserResourceControlConsumerInput();
        input.setAppId("testAppId");
        input.setName("testName");
        input.setIdNo("123456");
        input.setIdType("ID_CARD");

        FaceUserResourceControlRecordDO revokeRecord = new FaceUserResourceControlRecordDO();
        revokeRecord.setRecordId("cr1");
        revokeRecord.setCreateTime(new Date(System.currentTimeMillis() - 86400000)); // yesterday

        when(controlRecordDAO.selectOne(any(FaceUserResourceControlRecordDO.class)))
                .thenReturn(revokeRecord)
                .thenReturn(null); // for REPEAT_AGREE query

        when(sahara.getHexSand()).thenReturn("789012");


        // Act
        faceUserResourceControlInnerService.repeatAgree(input);

        // Assert
        Assert.assertNotNull(input);
    }
    @Test
    public void testSwitchStatusControlRecord_invalidStatus_throwsException() {
        // Arrange
        SwitchStatusControlRecordInput input = new SwitchStatusControlRecordInput();
        input.setRecordId("invalidRecordId");
        input.setStatus(FaceResourceControlStatusEnum.DISENABLE.name());

        when(controlRecordDAO.selectOneByRecordId("invalidRecordId")).thenReturn(new FaceUserResourceControlRecordDO());

        // Act
        faceUserResourceControlInnerService.switchStatusControlRecord(input);
    }


    @Test
    public void testCheckUserResourceControl_FullChain() {
        // 1. 构造输入参数
        FaceUserResourceControlCheckDTO input = new FaceUserResourceControlCheckDTO();
        input.setFaceId("testFaceId");

        // 2. 构造 faceInfo
        FaceDO faceDO = new FaceDO();
        faceDO.appId = ("testAppId");
        faceDO.name = ("张三");
        faceDO.idNo = ("110101199003072316");
        faceDO.idType = ("ID_CARD");
        Date faceCreateTime = new Date(System.currentTimeMillis() - 86400000); // 昨天
        faceDO.createTime = (faceCreateTime);


        when(faceRepository.getFaceInfoByFaceId("testFaceId")).thenReturn(FaceInfo.valueOf(faceDO));

        // 3. 构造 REVOKE_AGREE 记录
        FaceUserResourceControlRecordDO revokeRecord = new FaceUserResourceControlRecordDO();
        revokeRecord.setRecordId("record1");
        revokeRecord.setStatus(FaceResourceControlStatusEnum.REVOKE_AGREE.name());
        revokeRecord.setCreateTime(new Date());

        when(controlRecordDAO.selectOne(any(FaceUserResourceControlRecordDO.class))).thenReturn(revokeRecord);

        // 4. 构造 REPEAT_AGREE 记录
        FaceUserResourceControlRecordDO repeatRecord = new FaceUserResourceControlRecordDO();
        repeatRecord.setRecordId("record2");
        repeatRecord.setStatus(FaceResourceControlStatusEnum.REPEAT_AGREE.name());
        Date repeatAgreeTime = new Date(System.currentTimeMillis() - 3600000); // 1小时前
        repeatRecord.setCreateTime(repeatAgreeTime);

        when(controlRecordDAO.selectOne(argThat((FaceUserResourceControlRecordDO record) ->
                record.getStatus().equals(FaceResourceControlStatusEnum.REPEAT_AGREE.name()))))
                .thenReturn(repeatRecord);

        // 5. 执行方法
        ControlFaceDataPrivilegeResult result = faceUserResourceControlInnerService.checkUserResourceControl(input);

        // 6. 验证结果
        assertNotNull(result);

    }
}
