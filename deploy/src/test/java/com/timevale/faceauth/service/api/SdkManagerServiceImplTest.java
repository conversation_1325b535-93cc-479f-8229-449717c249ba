package com.timevale.faceauth.service.api;

import com.alibaba.fastjson.JSON;
import com.timevale.faceauth.AbstractTest;
import com.timevale.faceauth.service.enums.SdkStatusEnum;
import com.timevale.faceauth.service.impl.api.SdkManagerServiceImpl;
import com.timevale.faceauth.service.input.sdk.BatchSaveCustomerChannelInput;
import com.timevale.faceauth.service.input.sdk.GetManualConfigInput;
import com.timevale.faceauth.service.input.sdk.GetSdkCustomerDetailInput;
import com.timevale.faceauth.service.input.sdk.QuerySdkCustomerListInput;
import com.timevale.faceauth.service.input.sdk.QuerySdkVersionListInput;
import com.timevale.faceauth.service.input.sdk.SaveSdkVersionInput;
import com.timevale.faceauth.service.input.sdk.UpdateBundleIdIdInput;
import com.timevale.faceauth.service.input.sdk.UpdateSdkVersionStatusInput;
import com.timevale.faceauth.service.input.sdk.VerifyAndQueryChannelSdkInput;
import com.timevale.faceauth.service.result.BaseFaceAuthResult;
import com.timevale.faceauth.service.result.SupportResult;
import com.timevale.faceauth.service.result.sdk.BatchSaveCustomerChannelResult;
import com.timevale.faceauth.service.result.sdk.GetManualConfigResult;
import com.timevale.faceauth.service.result.sdk.GetSdkCustomerDetailResult;
import com.timevale.faceauth.service.result.sdk.QuerySdkCustomerListResult;
import com.timevale.faceauth.service.result.sdk.QuerySdkVersionListResult;
import com.timevale.faceauth.service.result.sdk.SaveSdkVersionResult;
import com.timevale.faceauth.service.result.sdk.UpdateSdkVersionStatusResult;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.Optional;

/**
 * <AUTHOR>
 * @DATE 2024/8/6 20:34
 */
@Slf4j
public class SdkManagerServiceImplTest extends AbstractTest {
    @Autowired
    private SdkManagerServiceImpl sdkManagerService;

    String orgName = "esigntest毒牙的公司";
    String gid = "0ae39d8a576a4566b8a8fd0e8731b49d";
    String sdkVersion = "0ae39d8a576a4566b8a8fd0e8731b49d";


    @Test
    public void testQuerySdkCustomerList() {
        SupportResult<QuerySdkCustomerListResult> result = sdkManagerService.querySdkCustomerList(new QuerySdkCustomerListInput());
        log.info("querySdkCustomerList  result = {}", JSON.toJSONString(result));
        Assert.assertTrue(result.ifSuccess());
    }


    @Test
    public void testQuerySdkVersionList() {
        SupportResult<QuerySdkVersionListResult> result = sdkManagerService.querySdkVersionList(new QuerySdkVersionListInput());
        log.info("querySdkVersionList  result = {}", JSON.toJSONString(result));
        Assert.assertTrue(result.ifSuccess());
    }


    @Test
    public void testSaveSdkVersion() {
        //
        SaveSdkVersionInput saveSdkVersionInput = new SaveSdkVersionInput();
        saveSdkVersionInput.setSdkVersion(sdkVersion);
        saveSdkVersionInput.setStatus(SdkStatusEnum.ENABLE.name());
        saveSdkVersionInput.setOperator(sdkVersion);
        SupportResult<SaveSdkVersionResult> saveSdkVersionResultSupportResult = sdkManagerService.saveSdkVersion(saveSdkVersionInput);
        log.info("saveSdkVersion  result = {}", JSON.toJSONString(saveSdkVersionResultSupportResult));


        UpdateSdkVersionStatusInput statusInput = new UpdateSdkVersionStatusInput();
        statusInput.setSdkVersionId(saveSdkVersionResultSupportResult.getData().getId());
        statusInput.setStatus(SdkStatusEnum.DISENABLE.name());
        statusInput.setOperator(sdkVersion);
        SupportResult<UpdateSdkVersionStatusResult> result = sdkManagerService.updateSdkVersionStatus(statusInput);
        log.info("updateSdkVersionStatus  result = {}", JSON.toJSONString(saveSdkVersionResultSupportResult));
        Assert.assertTrue(result.ifSuccess());
    }


    @Test
    public void testBatchSaveCustomerChannel() {
        GetManualConfigInput configInput = new GetManualConfigInput();
        SupportResult<GetManualConfigResult> config = sdkManagerService.getManualConfig(configInput);
        log.info("getManualConfig  result = {}", JSON.toJSONString(config));

        //
        String bundleId = System.currentTimeMillis() + "";

        //
        BatchSaveCustomerChannelInput input = new BatchSaveCustomerChannelInput();
        input.setOrgGid(gid);
        input.setOrgName(orgName);
        input.setOperator(orgName);
        BatchSaveCustomerChannelInput.BatchSaveCustomerChannelItem item = new BatchSaveCustomerChannelInput.BatchSaveCustomerChannelItem();
        item.setChannelCode(config.getData().getChannelList().get(0).getCode());
        item.setChannelLicense(orgName);
        item.setBundleId(bundleId);
        input.setCustomerChannelList(Lists.newArrayList(item));
        SupportResult<BatchSaveCustomerChannelResult> result = sdkManagerService.batchSaveCustomerChannel(input);
        log.info("batchSaveCustomerChannel  result = {}", JSON.toJSONString(result));
        Assert.assertTrue(result.ifSuccess());
        final String sdkCustomerId = result.getData().getSdkCustomerId();

        //
        GetSdkCustomerDetailInput detailInput = new GetSdkCustomerDetailInput();
        detailInput.setOrgGid(gid);
        detailInput.setSdkCustomerId(sdkCustomerId);
        SupportResult<GetSdkCustomerDetailResult> sdkCustomerDetail = sdkManagerService.getSdkCustomerDetail(detailInput);
        log.info("getSdkCustomerDetail  result = {}", JSON.toJSONString(sdkCustomerDetail));

        // 2025/5/12  更新
        item.setCustomerChannelId(sdkCustomerDetail.getData().getCustomerInfoItems().get(0).getCustomerChannelId());
        item.setChannelLicense(orgName + bundleId);
        SupportResult<BatchSaveCustomerChannelResult> updateResult = sdkManagerService.batchSaveCustomerChannel(input);
        //
        VerifyAndQueryChannelSdkInput verifyAndQueryChannelSdkInput = new VerifyAndQueryChannelSdkInput();
        verifyAndQueryChannelSdkInput.setOrgGid(gid);
        verifyAndQueryChannelSdkInput.setBundleId(bundleId);
        Optional<GetManualConfigResult.ConfigItem> sdkOptional = config.getData().getSdkVersionList().stream().filter(e -> StringUtils.equals(e.getStatus(), SdkStatusEnum.ENABLE.name())).findAny();
        verifyAndQueryChannelSdkInput.setSdkVersion(sdkOptional.isPresent() ? sdkOptional.get().getCode() : "1.0.0");
        SupportResult<String> supportResult = sdkManagerService.verifyAndQueryChannelList(verifyAndQueryChannelSdkInput);
        log.info("verifyAndQueryChannelList  result = {}", JSON.toJSONString(supportResult));
        Assert.assertTrue(supportResult.ifSuccess());

        //
        final UpdateBundleIdIdInput updateBundleIdIdInput = new UpdateBundleIdIdInput();
        updateBundleIdIdInput.setSdkCustomerId(sdkCustomerId);
        updateBundleIdIdInput.setBundleId(bundleId + "1");
        final SupportResult<BaseFaceAuthResult> updateBundleResult = sdkManagerService.updateBundleIdId(updateBundleIdIdInput);
        Assert.assertTrue(updateBundleResult.ifSuccess());
    }




}
