package com.timevale.faceauth.service.domain.provider.audiovideodual.esign;

import com.timevale.faceauth.AbstractTest;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpServletRequest;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * <AUTHOR>
 * @since 2024/9/29 14:34
 */
public class EsignVideoDualFaceServiceTest  extends AbstractTest {
    @Autowired
    private EsignVideoDualFaceService service;

    @Test
    public void detectFaceAuthorizationResultOnReturn(){
        String faceId = "71f3cea8c56b4c4ca4465b459d58678f";
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        ProviderFaceAuthorizationResult result = service.detectFaceAuthorizationResultOnReturn(faceId, mockHttpServletRequest);
        Assert.assertNotNull(result);
        Assert.assertEquals(true, result.isCompleted());
    }
}
