package com.timevale.faceauth.service.api;

import com.alibaba.fastjson.JSON;
import com.timevale.faceauth.AbstractTest;
import com.timevale.faceauth.service.domain.provider.tiktok.face.TiktokFaceService;
import com.timevale.faceauth.service.domain.provider.tiktok.face.domain.TiktokFaceAuthorizationResult;
import com.timevale.faceauth.service.domain.provider.tiktok.face.entity.response.TiktokGetTicketResultResponse;
import com.timevale.faceauth.service.domain.provider.tiktok.face.service.impl.TiktokOpenApiClientHandler;
import com.timevale.faceauth.service.domain.provider.tiktok.face.util.TiktokIdentityAuthUtil;
import com.timevale.faceauth.service.enums.FaceAuthModeEnum;
import com.timevale.faceauth.service.input.FaceAuthInput;
import com.timevale.faceauth.service.input.FaceAuthResInput;
import com.timevale.faceauth.service.input.HandleFaceAuthorizationReturnInput;
import com.timevale.faceauth.service.input.WakeupFaceInput;
import com.timevale.faceauth.service.result.FaceAuthResult;
import com.timevale.faceauth.service.result.QueryFaceAuthResResult;
import com.timevale.faceauth.service.result.SupportResult;
import com.timevale.faceauth.service.result.WakeupFaceResult;
import lombok.SneakyThrows;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE 2024/5/16 17:12
 */
public class FaceAuthServiceV3TiktokTest extends AbstractTest {


    @Autowired
    private TiktokFaceService tiktokFaceService;
    @Autowired
    private FaceAuthServiceV3 faceAuthServiceV3;
    @Autowired
    private TiktokOpenApiClientHandler tiktokOpenApiClientHandler;
    static String faceAuthCode = "e-dy-2ecd022fd208005e";
    static String aid = "1128";

    static String open_id = "_000vdMLj19CUbip3kn7SlLBRDYd5ubQ_2L-";

    @Test
    public void testTiktokStartNewFaceAuth() {
        String json = "{\n" +
                "    \"version\": \"1.0.0\",\n" +
                "    \"dnsAppId\": \"7876627435\",\n" +
                "    \"faceAuthMode\": \"FACE_TIKTOK_MINI\",\n" +
                "    \"name\": \"许华建\",\n" +
                "    \"idNo\": \"342622198905262396\",\n" +
                "    \"certType\": \"IDENTITY_CARD\",\n" +
                "    \"photoType\": \"HIGH_DEFINITION\",\n" +
                "    \"from\": \"BROWSER\",\n" +
                "    \"bizType\": \"WILL_AUTH\",\n" +
                "    \"appId\": \"7876627435\",\n" +
                "    \"bizId\": \"f4a9a8af8dcf43e2906483751c6d8327\"\n" +
                "}";
        SupportResult<FaceAuthResult> result = faceAuthServiceV3.startNewFaceAuth(JSON.parseObject(json, FaceAuthInput.class));

        // 获取faceToken
        String url = result.getData().getFaceValue();
        String faceToken = StringUtils.substringBetween(url, "faceToken=", "&");
        if (faceToken == null) {
            faceToken = StringUtils.substringAfter(url, "faceToken=");
        }

        WakeupFaceInput resourceInput = new WakeupFaceInput();
        resourceInput.setFaceAuthModeValue(FaceAuthModeEnum.FACE_TIKTOK_MINI.getMode());
        resourceInput.setFaceAuthCode(faceToken);
        resourceInput.setQueryContext(new HashMap() {{
            put(WakeupFaceInput.QueryContextConstant.tiktokSourceChannel, "TIKTOK");
            put(WakeupFaceInput.QueryContextConstant.tiktokMiniAuthCode, "test");
        }});
        SupportResult<WakeupFaceResult> faceResource = faceAuthServiceV3.wakeupFace(resourceInput);

        Assert.assertEquals(result.ifSuccess(), true);

    }


    @Test
    public void testQueryNewFaceAuthResult() {

        FaceAuthResInput authResInput = new FaceAuthResInput();
        authResInput.setFaceAuthId("a06efc74d39348f4b0c2e53164dc7aff");
        SupportResult<QueryFaceAuthResResult> result = faceAuthServiceV3.queryNewFaceAuthResult(authResInput);
        Assert.assertTrue(result.ifSuccess());

    }


    @Test
    public void testTiktokGetFaceResource() {
        WakeupFaceInput request = new WakeupFaceInput();
        request.setFaceAuthModeValue(FaceAuthModeEnum.FACE_TIKTOK_MINI.getMode());
        request.setFaceAuthCode(faceAuthCode);

        Map<String, String> queryContext = new HashMap<>();
        queryContext.put("miniAuthCode", "miniAuthCode");
        queryContext.put("sourceChannel", "TIKTOK");
        request.setQueryContext(queryContext);
        SupportResult<WakeupFaceResult> result = faceAuthServiceV3.wakeupFace(request);
        Assert.assertNotNull(result);
    }


    @Test
    public void testQueryResult() {
        HandleFaceAuthorizationReturnInput request = new HandleFaceAuthorizationReturnInput();
        request.setFaceAuthModeValue(FaceAuthModeEnum.FACE_TIKTOK_MINI.getMode());
        request.setFaceAuthCode(faceAuthCode);
        SupportResult result = faceAuthServiceV3.handleFaceAuthorizationReturn(request);
        Assert.assertFalse(result.ifSuccess(), "");
    }


    @SneakyThrows
    @Test
    public void testDyIdentityAuth() {
        // //平台公钥
        String pubKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA00J/vo2DVFWBf4WzQYSbC1Q9DK9r1MKrpN/Ju6T1zRQh8Un5TvooZLNsz9Aj06GBv5S/1xzZg0Em0iWL687teavlf6mGPvy9xvjRqH/a6Y2fBdCn7pV6aOxmsqbuGhuzp/TFk4IvXhoML7adXnpyH9DIvkLfpAfMpwdxZgs64lYGcdGMdC52FdnOf+2jZe7ZY1Oi2frDT/M9AA5MfZWF/xBH9Z06MSsA6A0Ma2ECfox5yfJp8ne/4aLNNou/1szINYSjOWSwku1nYcRHwa0y8Nv4wlPD7vdUPFYv5skdGlP4zBAfZY39kgIsE4Q3ftWjn50lzczeH1g+HKt8zfVU8wIDAQAB";
        //应用私钥
        String secretKey = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCfzEIBH2ycRpYjzcnBkQAw/kz8xBBkjPuId//50Rj70ebscpW8TkvKiLHOBuyZuiZamE3Z8AwisoD8/KPeDL8CnIjb4VlklGg7N2M7aOWq/+QAVmGAc+J9iXTyBFHcRzd75pFoMf44lZOxuSSYcSmCNZQVV/muJ3mDyIxPgIdm/vXmVhdr9xVP+tSEhs0+VrIyegfrREaBlINLUweHIG/7b7u45mWlKj4H6DVACwXcB3bcD9FZMdEZ9tZp8R1xOEYGb3Wvvlh45JHwCcvkcPyN/2IEFrIHeExht3+CiBISfLHMwq0b1TKu6BkYqEozpsAZm94OM2RAkrYrJQ6oDQCBAgMBAAECggEAdXRiU4bwgxGTC/AXyvI0jM5uch/+2JmTzOQ/PzVFQtsu/D7g9o8qQE0nPAN05y2fQVnPZ1B86wf/rXRZUj5WK6o5Ij1KQpae4l+8zY81uPHreaAEfLExGgrt7XHlZZ7nFmgMGuUcMiUncStfzbjnxLBzDxkiCbM0Kvw+vbUAUWAOZRgTJVn6simlp0XfMsnSuircRyD6rNYInKSxaGXXC8+8sf0FQqVPDsbXL1PbnZtLMjGT9DkEQDDaPRl6Cb1VLE2SXKtsUgn1bXs3xpjHIgEUT1UumPTAzUpbgdkQvKy7UfDyBMk+9EZMIX6YWpDPBvIJjERcOC+bQlhQi32tkQKBgQDTpdwrsG06e4DX8lRhPhKXO23dMte79UrI2g1FsIQbsxWUlGlvFRpPTQ7+NlZ837ITOhsvbaOrKW2QPU5Yl1OAqsfCeUXWk4mOH3SR/LdWtTK7kKfwOqPAjGlDaHLxKV1oNw1pe8YPjYcHZjWwpm+R2/KPFRBt7Wu9Sk62ZHbd1QKBgQDBSNVTxpCxtjLT59zAUNkiY6j+Ev2aqCyZAYfASpXkIR35qdE80iYraMSjSTpfjrVlF20iwL4br+AX3A91Vf+1yJB/yLarhWshiS+SKvBUQufWReQT/0njtORpAeo3i8nIuRLw+iMGbK7SbMb+SQUyDtNAn2ihpGpo2P7YrqYx/QKBgGWcc5/mDArP0IPcq/YjvvksZwqTpk9u7ICRb13N6R8DU5EJz7StBPydml0h6VP6cZwZgYO7t6AtO5TMWhjeThCv7UfIwWbLXncFUwnjsoHGbJzwTjBfds6ymrbqJoTxvw6Rn7bM3cIJyJIc2Bn/TgFQ/5sCGeks21T9n9yhTxAhAoGBAJB/hdR6t0PUZY1cYwF3r8zw2q8PvX64yZaduUI9wG9rfsBef2nL5fNAAPndzgEQbHYmHFYF6FpEkvDjeL8Myv248qZFhKMYPG425FSPO1qTJkVfvNJiYXgVjUkfwF1EaVdhXVLAfhzYdZ4k6mPCbVruEGrYgd2OUGsQBv4xG1h1AoGBAJfeX0BOCmyHSw4GRT59EngYFYtV7sIsfjJOpk8urM6CIQUbiFXof5ur/ndXcb5+PPjNFR3P/lukDTaY1zdPlzrUb62yNI9avHy8f397lQJe26U7zNI1HpPlsK98vThnLrhHGtP5LRE+HduZLsVcromB66ZmQ3JInzZCAzI3dqxn";

        String pubKeyFormat = String.format(pubKey);
        String secretKeyFormat = String.format(secretKey);
        //------------------------第一步生成对称密钥和向量--------------------
//        System.out.println("--------------公钥加密--------------------");
        byte[] key = TiktokIdentityAuthUtil.randomKeyOrIv(32);
        byte[] iv = TiktokIdentityAuthUtil.randomKeyOrIv(16);

        byte[] keyEncrypt = TiktokIdentityAuthUtil.encryptPublicKey(Base64.getEncoder().encode(key), pubKeyFormat);
        byte[] ivEncrypt = TiktokIdentityAuthUtil.encryptPublicKey(Base64.getEncoder().encode(iv), pubKeyFormat);
        //----------------------第二步RSA加解密对称密钥key和初始向量iv---------------------------
        //-------------------------------第三步加解密敏感数据------------------------------------
        String name = TiktokIdentityAuthUtil.encryptCBC("许华建", key, iv);
        String idcard = TiktokIdentityAuthUtil.encryptCBC("342622198905262396", key, iv);

        //------------------------------------私钥解密------------------------------------------
//        System.out.println("------------------私钥解密--------------------");
        //1.RSA解密key、iv
        String keyRSAE = "Sw3oMTVEE5ke5ERFgDfPJfpNEcGDkzjcPNmEoNL38L5lM4rQnqCLJTtFv3bH5rJ+lREjQovMYz1cpZlJRHG0fA0jzTRXY3cfks5Kb+hjl4kup6Z1/VWnmvb4DtGrBNzNljc9As4dJ45XMAKNPOg33fatexnMS34IJGyhtjympbTu3UheasyZh3k5nRu0xbUZvqxD4TpUS3VzXtn1A2n7IZ+o5A5ffEJCsdBzbDfHOcmykIQSDebC8T7zF5RaBi7HcyHoK/ROX+umj15VJmZvTuxKonBEmdNLgwJS2Mva0JZ5KaQ9WB9xyOBvj0BNKz5F7Ht8Dgtj6pFaLZ5lj/4+/w==";
        String ivRSAE = "jx6EcJLZn1CndJy4OQMnW5Tm7wXDaGi2DfxXw2DbpT+qQgVv6fTkU5FPJdxPT7UyxrvB+sOwzLYigSnLbCl2/6FhPy9eojGMzUs5YTYOze51TzNw+4hIvxop/H7TjgJrWiL8PdFvom1I0CYcDatFwu6/oKzUMvnVedL/h1Olx31W3rE2/N/F4XxYIhoThHligh4wBdKNe3yboIJJzbg3wo4oSYZMaacjgi9Nr5EAh76xABhTbWSdMBvhbrloFs6j5kcCEHwwYkdtbgx7hTsETn9TQ0EGRwoBQWzlBgp9GGaDLZNlXJoy6WURtKlaApJlcVB5r6tbAOW9axxX13EZ7Q==";
        String keyDecrypt = TiktokIdentityAuthUtil.decryptByPrivateKey(Base64.getDecoder().decode(keyRSAE), secretKeyFormat);
        String ivDecrypt = TiktokIdentityAuthUtil.decryptByPrivateKey(Base64.getDecoder().decode(ivRSAE), secretKeyFormat);
        String userVideo = TiktokIdentityAuthUtil.decryptCBC("z6cGyp5EcDL92OyxizdEjcIq0woXCxt26Wa9gE4SkeSje1AGLOvYUUWZkhi/Ev56/w7Jxh/6RhZNgfl6AT0Mj70+9un3IxhO7LvTiq7R4XIR3jln8SJRShfoOqv9hPzrQ6QHyKQMOEyS6zzVKxb+Aa617wGhnaWvZzG5znBEn5I2GqPp0vmfAkkHpLic0Q59RSfWiaiulk1NSyjEk/OKLpagQ/ZPajTfLmEEsLeMDHcHRV0FIvkSYExC5iau/aQXtneMnUMuRZ9NzQzY6WJ15X4Vd5IH6HoafrmvbPYnuS6eFaz45dmpTLop1Q1RVxFwknEiwpDttBLMjudqRkd6tyAeVTOLBM3NyarUy1roT+akPGyOVAkuLPGVoIMNc/pDp6HSk6dFWtHDAvVGhrr4l7PumFnKinTSOwGfcTxb6in0oXJkfuF7h45IMlC6E1dFiMtF4jNRyMYxvyPcBzycrDKPxzR94sytyHILte9bWvcgkdMciOM6RnEz//jeUofTyblthcblcBcE9eh8vJWXlGwGWtAA0FPxsN52+Qs/4I/+sE+B/xvWsOGQJ+8BJ33pVz1Jl/XWZPyDHFAgW4YiTEvEd29EJo7EK3+Rq25ji92zJyAwsb7+r5/kDHc/Gj57lE3hzC/e6ZgVr7iltmlhObBWYcy8ntjWHGGnXWc5kD6mky2OALg/LMQuoVwjjaAUYBp04Ys1EzSZKVchN8GXX7iob9UNvAEBujPbGptf+7udHhBLTexWEqH/dBQE7l53G81wuCbohM/mM4f4TwzH63BNyuJUdIGlvKlzb5PFUPxW5gpEq5iG5cpwYMcF9olu", Base64.getDecoder().decode(keyDecrypt), Base64.getDecoder().decode(ivDecrypt));
        String userImage = TiktokIdentityAuthUtil.decryptCBC("z6cGyp5EcDL92OyxizdEjcIq0woXCxt26Wa9gE4SkeSje1AGLOvYUUWZkhi/Ev56/w7Jxh/6RhZNgfl6AT0Mj1vbEtYBbiLEKywg0KeMwFaHcAFo+AXi1wtP8D/vt6RqE43FL4keu/9vLWoCJITgITk3DRl+RxOoKz/Q6D/n3A2ex4pgyAxg3Rar6yMvu2RC2AGfe/wtlYvTp/EbSCvyYDMlzexxzsHRCmGr1rDbNJfTnj40dzMsMkGWop61tbWDQfGYOmQwIGj0BnpLM3UMoYzLTC9YFW2VpVs2nslV0ZuOf7GloGe9oaEtq4NJPVX05xFFdB+fSnJbjhnv2iqWrubUHjCGQGOP4pBwcbgnSA+H3NG6kmJF4W2GpPjKbaJrdQkGEK79caO2BuF0wSRxkOdWSkHKLdI7RLnObjTH2bjHqBNhENmdikNDy+TKKm1iwEQC4kMoYF6PxapA0VzJ9TdPCwIawa5gvTXErksX0Bffdx2ATMQ7ottgUXB0h8gsM8Sos/4VcBkSId6viP5Tmo1GkTikgOEpkZtLyXuEFoKbNS176Vhk2W/9kAlzxKA3NbPHO7nvvr8PpDCxJdBiTFfu9CwwV0lMuaE/NAQ4i7XnL4/3M9JJxvEhplyddEYdqXCyoYbCcKiK7AYjyhRhHOD+HW4zxkb6vkI6h5s38SGVc5BQAshskG3owTNogggnCRSYLgAE+esIErpgM2Q0UTxWmI9FK+Dd1bWDAv6428lCuLOmlAb/Kk7NvwG6cDCarAZKHhHTuACgZff4FX8kpKldgFNT9Rz4yaiWzRGNNGlB+yww99xd49dIS6IJP2vV", Base64.getDecoder().decode(keyDecrypt), Base64.getDecoder().decode(ivDecrypt));
        Assert.assertNotNull(userImage);
    }

//    @Test 目前只能模拟和生产能使用
//    public void testGetTicket() {
//        String ticket = tiktokOpenApiClientHandler.getTicket(open_id, aid, "许华建", "342622198905262396");
//        TiktokGetTicketResultResponse ticketResult = tiktokOpenApiClientHandler.getTicketResult(ticket, aid);
//        Assert.assertNotNull(ticket);
//
//        Boolean xx = tiktokOpenApiClientHandler.fee(ticket, aid);
//        Assert.assertNotNull(xx);
//    }


    @Test
    public void testGetOpenId() {
        String xx = tiktokOpenApiClientHandler.getOpenId(open_id);
        Assert.assertNotNull(xx);
    }


    @Test
    public void testAnalyzeErrMsgErrorCode() {

        TiktokGetTicketResultResponse response = new TiktokGetTicketResultResponse();
        response.setErr_no(0);
        response.setErr_tips("sd");
        TiktokFaceAuthorizationResult.createBuilder("x");

        //-- analyzeErrMsgErrorCode
        String prErrorCode = "1003114";
        tiktokFaceService.analyzeErrMsgErrorCode(null);

        String errMsg = "startFacialRecognitionVerify:fail " + prErrorCode + " 保持人物姿态端正无遮挡  屏幕内无过亮、过暗区域";
        Pair<Integer, String> pair = tiktokFaceService.analyzeErrMsgErrorCode(errMsg);
        Assert.assertEquals(pair.getLeft().toString(), prErrorCode);

        //-- getOutTiktokFaceResultStatus
        HashMap<String, String> extendedMap = new HashMap<>();
        Assert.assertNull(tiktokFaceService.getOutTiktokFaceResultStatus(extendedMap));

        extendedMap.put("1", "1");
        Assert.assertNull(tiktokFaceService.getOutTiktokFaceResultStatus(extendedMap));

        extendedMap.put(HandleFaceAuthorizationReturnInput.ExtendedMapConstant.errNo, "21102");
        Assert.assertNotNull(tiktokFaceService.getOutTiktokFaceResultStatus(extendedMap));

        extendedMap.put(HandleFaceAuthorizationReturnInput.ExtendedMapConstant.errMsg, errMsg);
        Assert.assertNotNull(tiktokFaceService.getOutTiktokFaceResultStatus(extendedMap));
        //--
        tiktokFaceService.extractedSimilarity(null);
    }

    @Test
    public void testStatic() {
        tiktokOpenApiClientHandler.getBase64ImageByUri("");
        tiktokOpenApiClientHandler.getBase64ImageByUri("test://");

        TiktokGetTicketResultResponse.Data data = new TiktokGetTicketResultResponse.Data();
        TiktokGetTicketResultResponse ticketResultResponse = new TiktokGetTicketResultResponse();
        ticketResultResponse.setFlow_data(data);
        tiktokOpenApiClientHandler.logTicketResult(ticketResultResponse);
        data.setUser_image("xx");
        tiktokOpenApiClientHandler.logTicketResult(ticketResultResponse);
        data.setUser_video("xx");
        tiktokOpenApiClientHandler.logTicketResult(ticketResultResponse);
        Assert.assertNotNull(data);

    }


}
