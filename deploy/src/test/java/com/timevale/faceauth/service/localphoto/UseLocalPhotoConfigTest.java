package com.timevale.faceauth.service.localphoto;

import com.timevale.faceauth.AbstractTest;
import com.timevale.framework.puppeteer.enums.PropertyChangeType;
import com.timevale.framework.puppeteer.model.ConfigChange;
import com.timevale.framework.puppeteer.model.ConfigChangeEvent;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/7/29 19:36
 */
public class UseLocalPhotoConfigTest extends AbstractTest {




    @Test
    public void loadConfig(){
        UseLocalPhotoConfig useLocalPhotoConfig = UseLocalPhotoConfig.getInstance();
        String originConfig  = "[{\"provider\":\"byteDance\",\"localPhotoOriginEnable\":false,\"useOriginEnable\":false},{\"provider\":\"tencentCloud\",\"localPhotoOriginEnable\":true,\"useOriginEnable\":true},{\"provider\":\"weChatFace\",\"localPhotoOriginEnable\":true,\"useOriginEnable\":true},{\"provider\":\"antBlockChain\",\"localPhotoOriginEnable\":true,\"useOriginEnable\":false},{\"provider\":\"faceWithAlipay\",\"localPhotoOriginEnable\":true,\"useOriginEnable\":false},{\"provider\":\"liveness\",\"localPhotoOriginEnable\":false,\"useOriginEnable\":false},{\"provider\":\"tiktok\",\"localPhotoOriginEnable\":true,\"useOriginEnable\":false}]";
        useLocalPhotoConfig.onInitialize();
        useLocalPhotoConfig.initConfig(originConfig);
        List<String> useOriginProviders = useLocalPhotoConfig.getUseOriginProviders();
        Assert.assertNotNull(useOriginProviders);

        PhotoOriginConfigBean centerConfig = UseLocalPhotoConfig.getConfig("byteDance1");
        Assert.assertNull(centerConfig);
        centerConfig = UseLocalPhotoConfig.getConfig("byteDance");
        Assert.assertNotNull(centerConfig);
        Assert.assertEquals(centerConfig.isLocalPhotoOriginEnable(), Boolean.FALSE.booleanValue());

        Assert.assertEquals(useLocalPhotoConfig.isValidAge("110115201312034194"), false);
        Assert.assertEquals(useLocalPhotoConfig.isValidAge("110116195405125990"), false);
        Assert.assertEquals(useLocalPhotoConfig.isValidAge("110101199312316257"), true);


        Map<String, ConfigChange> m_changes = new HashMap<>();
        m_changes.put("local.photo.origin.age.max", new ConfigChange("face_local_photo","local.photo.origin.age.max", "65","70",PropertyChangeType.MODIFIED));

        ConfigChangeEvent changeEvent = new ConfigChangeEvent("face_local_photo",m_changes);
        useLocalPhotoConfig.onPropertyChanged(changeEvent);
        Assert.assertEquals(useLocalPhotoConfig.getOriginAgeMax().intValue(),70);
    }

}
