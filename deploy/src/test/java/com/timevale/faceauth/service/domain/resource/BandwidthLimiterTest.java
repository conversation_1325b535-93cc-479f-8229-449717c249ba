package com.timevale.faceauth.service.domain.resource;

import com.alibaba.fastjson.JSON;
import com.timevale.faceauth.AbstractTest;
import com.timevale.faceauth.service.domain.resource.http.download.BandwidthLimiter;
import com.timevale.faceauth.service.domain.resource.http.download.GuavaRateLimited;
import com.timevale.faceauth.service.domain.resource.http.download.NoneRateLimited;
import com.timevale.faceauth.service.domain.resource.http.download.StringOutputStream;
import com.timevale.faceauth.service.domain.resource.task.ResourceDownloadMsg;
import com.timevale.faceauth.service.domain.resource.task.ResourceDownloadTaskHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * <AUTHOR>
 * @since 2025/1/8 11:32
 */

public class BandwidthLimiterTest extends AbstractTest {


    @Autowired
    private ProviderTencentCloudH5RemotePhotoSyncResourceLoader photoSyncResourceLoader;
    @Autowired
    private ProviderTencentCloudH5RemoteVideoSyncResourceLoader videoSyncResourceLoader;
    @Autowired
    private ProviderTencentCloudH5RemotePhotoAsyncResourceLoader photoAsyncResourceLoader;

    @Test
    public void rateLimit() throws IOException {
        NoneRateLimited noneRateLimited = new NoneRateLimited();
        Assert.assertEquals(noneRateLimited.name(), BandwidthLimiter.NAME_DEFAULT);
        noneRateLimited.init(1024, BandwidthLimiter.BUFFER_SIZE_DEFAULT);
        String  text  ="23123123";
        InputStream input = new ByteArrayInputStream(text.getBytes("UTF-8"));
        OutputStream output = new StringOutputStream();
        noneRateLimited.read(input, output);
        noneRateLimited.after();


        text  ="23123122312332312312231233231231223123323123122312332312312231233231231223123323123122312332312312231233231231223123323123122312332312312231233231231223123323123122312332312312231233231231223123323123122312332312312231233231231223123323123122312332312312231233";
        input = new ByteArrayInputStream(text.getBytes("UTF-8"));
        output = new StringOutputStream();

        GuavaRateLimited guavaRateLimited = new GuavaRateLimited();
        Assert.assertEquals(guavaRateLimited.name(), BandwidthLimiter.NAME_GUAVA_LIMITED);
        guavaRateLimited.init(1024, 2048);
        guavaRateLimited.read(input, output);
        guavaRateLimited.after();
    }

    @Test
    public void ResourceLoader(){
        ProviderResourceBaseContext context = new ProviderResourceBaseContext();
        context.setFaceId("7fd5e0b2b535412aadfff7b53b3a8e1c");
        String photo = photoSyncResourceLoader.execute(context);
        Assert.assertNull(photo);
        String  video = videoSyncResourceLoader.execute(context);
        Assert.assertNull(video);

        String ResourceDownloadTopic = ResourceSystemHolder.getInstance().getResourceDownloadMqTopic();
        ResourceDownloadTaskHandler resourceDownloadTaskHandler = new ResourceDownloadTaskHandler(ResourceDownloadTopic);

        ResourceDownloadMsg downloadMsg = new ResourceDownloadMsg();
        downloadMsg.setFaceId("7fd5e0b2b535412aadfff7b53b3a8e1c");
        downloadMsg.setProvider("tencentCloud");
        downloadMsg.setResourceType(FaceResourceType.PHOTO.name());
        resourceDownloadTaskHandler.execute(JSON.toJSONString(downloadMsg));
        Assert.assertEquals(downloadMsg.getProvider(), "tencentCloud");


        photoAsyncResourceLoader.execute(context);
    }

}
