package com.timevale.faceauth.service.localphoto;

import com.timevale.faceauth.AbstractTest;
import com.timevale.faceauth.service.domain.processor.FaceRequestContext;
import com.timevale.faceauth.service.domain.processor.support.SimpleFaceRequestContext;
import com.timevale.faceauth.service.domain.provider.support.FaceResourcesInvocationHandler;
import com.timevale.faceauth.service.domain.repository.FaceInfo;
import com.timevale.faceauth.service.domain.repository.FaceOSSResources;
import com.timevale.faceauth.service.domain.repository.UserPhoto;
import com.timevale.faceauth.service.domain.support.SupportFaceAuthorizationResourceResolver;
import com.timevale.faceauth.service.impl.api.DelegateFaceAuthorizationService;
import com.timevale.faceauth.service.input.UserPhotoQueryInput;
import com.timevale.faceauth.service.result.UserPhotoQueryResult;
import com.timevale.faceauth.service.utils.UUIDUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * <AUTHOR>
 * @since 2024/7/29 20:31
 */
public class FaceResourcesInvocationHandlerTest  extends AbstractTest {


    @Autowired
    private FaceResourcesInvocationHandler faceResourcesInvocationHandler;
    @Autowired
    private DelegateFaceAuthorizationService authorizationService;
    @Autowired
    private SupportFaceAuthorizationResourceResolver supportFaceAuthResourceResolver;

    @Test
    public void saveUserPhoto(){
        FaceInfo faceInfo = FaceInfo
                .createBuilder()
                .setFaceId(UUIDUtil.generateUUID())
                .setPhoto("$xcxcxcxcxcxc")
                .setProvider("byteDance")
                .setName("测试")
                .setIdNo("test12121212")
                .build();
        FaceOSSResources resources = new FaceOSSResources();
        resources.setPhoto("$NEWxcxcxcxcxcxc");
        faceResourcesInvocationHandler.saveUserPhoto(faceInfo, resources);

        faceInfo = FaceInfo
                .createBuilder()
                .setFaceId(UUIDUtil.generateUUID())
                .setPhoto("$xcxcxcxcxcxc")
                .setProvider("TestByteDance")
                .setName("测试")
                .setIdNo("test12121212")
                .build();
        resources = new FaceOSSResources();
        resources.setPhoto("$NEWxcxcxcxcxcxc");
        faceResourcesInvocationHandler.saveUserPhoto(faceInfo, resources);
        UserPhotoQueryInput queryInput =  new UserPhotoQueryInput();
        queryInput.setName(faceInfo.getName());
        queryInput.setIdNo(faceInfo.getIdNo());
        UserPhotoQueryResult queryResult = authorizationService.queryUserPhoto(queryInput).getData();
        Assert.assertEquals(queryResult.getHasUserPhoto().booleanValue(),true);

        FaceRequestContext context = new FaceContext(faceInfo.getProvider(),faceInfo.getName(),faceInfo.getIdNo());
        UserPhoto userPhoto = supportFaceAuthResourceResolver.resolvePhoto(context);
        Assert.assertEquals(userPhoto.getPhoto(), resources.getPhoto());
        authorizationService.clearUserPhoto(queryInput);

        userPhoto = supportFaceAuthResourceResolver.resolvePhoto(context);
        Assert.assertEquals(userPhoto.getPhoto(), null);
        queryResult = authorizationService.queryUserPhoto(queryInput).getData();
        Assert.assertEquals(queryResult.getHasUserPhoto().booleanValue(),false);
    }



    @Test
    public void saveUserPhotoV2(){
        FaceInfo faceInfo = FaceInfo
                .createBuilder()
                .setFaceId(UUIDUtil.generateUUID())
                .setPhoto("$xcxcxcxcxcxc")
                .setProvider("tencentCloud")
                .setName("测试")
                .setIdNo("test12121212")
                .build();
        FaceOSSResources resources = new FaceOSSResources();
        resources.setPhoto("$NEWxcxcxcxcxcxc");
        faceResourcesInvocationHandler.saveUserPhoto(faceInfo, resources);
    }

    @Test
    public void saveUserPhotoExceptionV2(){
        FaceInfo faceInfo = FaceInfo
                .createBuilder()
                .setFaceId(UUIDUtil.generateUUID())
                .setProvider("tencentCloud")
                .setName("测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试")
                .setIdNo("test12121212")
                .build();
        FaceOSSResources resources = new FaceOSSResources();
        resources.setPhoto("$NEWxcxcxcxcxcxc");
        faceResourcesInvocationHandler.saveUserPhoto(faceInfo, resources);
    }



    public class FaceContext implements FaceRequestContext {

        private String provider;
        private String name;
        private String idNo;
        public FaceContext(String provider,String name, String idNo) {
            this.provider = provider;
            this.name = name;
            this.idNo = idNo;
        }

        @Override
        public String getAppId() {
            return "**********";
        }

        @Override
        public String getOid() {
            return "oid";
        }

        @Override
        public String getBizId() {
            return UUIDUtil.generateUUID();
        }

        @Override
        public String getBizCode() {
            return "code";
        }

        @Override
        public String getClientType() {
            return "IOS";
        }

        @Override
        public String getName() {
            return name;
        }

        @Override
        public String getIdNo() {
            return idNo;
        }

        @Override
        public String getIdType() {
            return "null";
        }

        @Override
        public String getProvider() {
            return provider;
        }

        @Override
        public String getPhoto() {
            return null;
        }

        @Override
        public String getPhotoType() {
            return "xxxxxx";
        }

        @Override
        public String getReturnUrl() {
            return null;
        }

        @Override
        public String getCallbackUrl() {
            return null;
        }

        @Override
        public long getTimestamp() {
            return 0;
        }

        @Override
        public String getInput() {
            return null;
        }
    }

}
