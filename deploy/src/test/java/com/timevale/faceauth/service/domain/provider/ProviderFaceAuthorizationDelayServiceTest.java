package com.timevale.faceauth.service.domain.provider;

import com.timevale.faceauth.AbstractTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.testng.annotations.Test;

/**
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/3/24
 */
public class ProviderFaceAuthorizationDelayServiceTest extends AbstractTest {

  @Test
  public void livenessFaceAuthorizationReturned() {
    final String faceId = "c182a28f524a40d5981a75c9104540c2";
    final String provider = "liveness";
    ProviderFaceAuthorizationDelayService returnService =
            ConfigurableProviderServices.getProviderService(provider);
    returnService.faceAuthorizationReturned(faceId, null, (new MockHttpServletResponse()));
  }

  @Test
  public void byteDanceFaceAuthorizationReturned() {
    final String faceId = "2844911337ef42e18e461c62e533d5b7";
    final String provider = "byteDance";
    ProviderFaceAuthorizationDelayService returnService =
            ConfigurableProviderServices.getProviderService(provider);
    returnService.faceAuthorizationReturned(faceId, new MockHttpServletRequest(), (new MockHttpServletResponse()));
  }
}