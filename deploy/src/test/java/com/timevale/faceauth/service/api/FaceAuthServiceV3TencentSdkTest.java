package com.timevale.faceauth.service.api;

import com.timevale.faceauth.AbstractTest;
import com.timevale.faceauth.service.domain.provider.tencent.TencentWebAccessHolder;
import com.timevale.faceauth.service.domain.provider.tencent.domain.TencentWebAppIdVersionEnum;
import com.timevale.faceauth.service.enums.FaceAuthBizTypeEnum;
import com.timevale.faceauth.service.enums.FaceAuthModeEnum;
import com.timevale.faceauth.service.enums.FaceResourceFaceAuthModeEnum;
import com.timevale.faceauth.service.input.FaceAuthInput;
import com.timevale.faceauth.service.input.HandleFaceAuthorizationReturnInput;
import com.timevale.faceauth.service.input.WakeupFaceInput;
import com.timevale.faceauth.service.result.FaceAuthResult;
import com.timevale.faceauth.service.result.HandleFaceAuthorizationReturnResult;
import com.timevale.faceauth.service.result.SupportResult;
import com.timevale.faceauth.service.result.WakeupFaceResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * <AUTHOR>
 * @DATE 2024/8/28 15:05
 */
public class FaceAuthServiceV3TencentSdkTest extends AbstractTest {

    @Autowired
    private FaceAuthServiceV3 faceAuthServiceV3;

    @Test
    public void testStartNewFaceAuthTencentSdk() {

        String json = "{\n" +
                "    \"version\": \"1.0.0\",\n" +
                "    \"dnsAppId\": \"7876627435\",\n" +
                "    \"faceAuthMode\": \"FACE_TIKTOK_MINI\",\n" +
                "    \"name\": \"许华建\",\n" +
                "    \"idNo\": \"342622198905262396\",\n" +
                "    \"certType\": \"IDENTITY_CARD\",\n" +
                "    \"photoType\": \"HIGH_DEFINITION\",\n" +
                "    \"from\": \"BROWSER\",\n" +
                "    \"bizType\": \"WILL_AUTH\",\n" +
                "    \"appId\": \"7876627435\",\n" +
                "    \"bizId\": \"f4a9a8af8dcf43e2906483751c6d8327\"\n" +
                "}";

        FaceAuthInput faceAuthInput = new FaceAuthInput();
        faceAuthInput.setName("许华建");
        faceAuthInput.setIdNo("342622198905262396");
        faceAuthInput.setFaceAuthMode(FaceAuthModeEnum.FACE_TECENT_SDK_BASIC);
        faceAuthInput.setBizType(FaceAuthBizTypeEnum.WILL_AUTH);
        faceAuthInput.setAppId(APP_ID);
        faceAuthInput.setDnsAppId(APP_ID);
        SupportResult<FaceAuthResult> result = faceAuthServiceV3.startNewFaceAuth(faceAuthInput);
        Assert.assertTrue(result.ifSuccess());


        String faceValue = result.getData().getFaceValue();
        WakeupFaceInput wakeupFaceInput = new WakeupFaceInput();
        wakeupFaceInput.setFaceAuthCode(faceValue);
        wakeupFaceInput.setFaceAuthModeValue(FaceResourceFaceAuthModeEnum.APP_FACE_SDK.getMode());
        SupportResult<WakeupFaceResult> supportResult = faceAuthServiceV3.wakeupFace(wakeupFaceInput);
        Assert.assertTrue(supportResult.ifSuccess());


        HandleFaceAuthorizationReturnInput returnInput = new HandleFaceAuthorizationReturnInput();
        returnInput.setFaceAuthModeValue(FaceResourceFaceAuthModeEnum.APP_FACE_SDK.getMode());
        returnInput.setFaceAuthCode(faceValue);
        SupportResult<HandleFaceAuthorizationReturnResult> supportResult2 = faceAuthServiceV3.handleFaceAuthorizationReturn(returnInput);
        Assert.assertTrue(supportResult2.ifSuccess());
    }

    @Test
    public void testStaticTencentWebAppIdVersionEnum() {
        TencentWebAppIdVersionEnum.getByCode(APP_ID);
        TencentWebAccessHolder accessHolder = TencentWebAppIdVersionEnum.getByVersion(APP_ID).getAccessHolder();
        Assert.assertNotNull(accessHolder);
    }
}
