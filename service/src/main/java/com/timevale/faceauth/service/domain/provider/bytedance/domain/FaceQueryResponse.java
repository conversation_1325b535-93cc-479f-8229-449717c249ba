package com.timevale.faceauth.service.domain.provider.bytedance.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.volcengine.service.visual.model.response.VisualBaseResponse;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/2/21
 */
@Data
public class FaceQueryResponse extends VisualBaseResponse {


    @JSONField(name = "data")
    private VolcengineQueryData data;

    @Data
    public static class VolcengineQueryData {

        /**
         * 是否核验通过
         */
        @JSONField(name = "result")
        private Boolean result;

        /**
         * 客户端采集的人脸图
         */
        @JSONField(name = "images")
        private Image images;

        /**
         * 客户端采集的视频数据
         */
        @JSONField(name = "video")
        private String video;

        /**
         * 认证的分数和阈值
         */
        @JSONField(name = "source_comp_details")
        private SourceCompDetail sourceCompDetails;

        /**
         * 客户在Token接口传入Tos信息时，会返回此字段
         */
        @JSONField(name = "tos_data")
        private TosData tosData;

        /**
         * 子错误说明，可以进一步区分错误原因，部分服务异常情况时无法返回。详细见错误码和计费中的algorithm_base_resp
         */
        @JSONField(name = "verify_algorithm_base_resp")
        private AlgorithmBaseResp verifyAlgorithmBaseResp;

        /**
         * 计费说明，部分服务异常情况时无法返回。详细见：错误码和计费中的req_measure_info
         */
        @JSONField(name = "verify_req_measure_info")
        private String verifyReqMeasureInfo;

    }

    @Data
    public static class Image {

        /**
         * 人脸图的base64
         */
        @JSONField(name = "image_best")
        private String imageBest;

        /**
         * 环境图的base64
         */
        @JSONField(name = "image_env")
        private String imageEnv;

    }

    @Data
    public static class SourceCompDetail {

        /**
         * 有源比对分数
         */
        @JSONField(name = "score")
        private float score;

        /**
         * 有源比对分数阈值
         */
        @JSONField(name = "thresholds")
        private Thresholds thresholds;

    }

    @Data
    public static class Thresholds {

        /**
         * 0.1%置信度阈值
         */
        @JSONField(name = "1e-3")
        private float e3;

        /**
         * 0.01%置信度阈值	此值为判断是否通过的标准
         */
        @JSONField(name = "1e-4")
        private float e4;

        /**
         * 0.001%置信度阈值
         */
        @JSONField(name = "1e-5")
        private float e5;

        /**
         * 0.0001%置信度阈值
         */
        @JSONField(name = "1e-6")
        private float e6;

    }

    @Data
    public static class TosData {

        @JSONField(name = "bucket")
        private String bucket;

        @JSONField(name = "image_env_key")
        private String imageEnvKey;

        @JSONField(name = "image_best_key")
        private String imageBestKey;

        @JSONField(name = "video_key")
        private String videoKey;

        @JSONField(name = "cert_data_key")
        private String certDataKey;

    }

    @Data
    public static class AlgorithmBaseResp {

        /**
         * 子错误码
         */
        @JSONField(name = "status_code")
        private int statusCode;

        /**
         * 子错误描述
         */
        @JSONField(name = "status_message")
        private String statusMessage;

    }

//    @Data
//    public static class ReqMeasureInfo {
//
//        /**
//         * 取值固定为"query_num"
//         */
//        @JSONField(name = "measure_type")
//        private int measureType;
//
//        /**
//         * 取值为0或1。0为不计费，1为计费。
//         */
//        @JSONField(name = "value")
//        private String value;
//
//    }


}
