//package com.timevale.faceauth.service.domain.controll;
//
//import com.timevale.mandarin.common.result.ToString;
//import lombok.Data;
//
//import java.util.Date;
//
///**
// * <AUTHOR>
// * @DATE 2025/7/4 16:18
// */
//@Data
//public class FaceUserResourceControlCheckResultDTO extends ToString {
//    /**
//     * true 拒绝.不返回照片视频
//     * false 同意，返回照片视频
//     */
//    private boolean refuse;
//
//    private String refuseRecordId;
//
//    private Date refuseRecordTime;
//
//}
