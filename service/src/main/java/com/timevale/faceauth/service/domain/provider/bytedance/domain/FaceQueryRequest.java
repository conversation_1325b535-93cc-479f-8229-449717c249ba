package com.timevale.faceauth.service.domain.provider.bytedance.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/2/21
 */
@Data
public class FaceQueryRequest {

    /**
     * 此处请填写cert_verify_query
     */
    @JSONField(name = "req_key")
    private String req_key = "cert_verify_query";

    /**
     * 通过Token接口获取的byted_token
     */
    @JSONField(name = "byted_token")
    private String byted_token;

    /**
     * 默认为false，填写true时Query接口将不会返回图片与视频
     *
     */
    @JSONField(name = "omit_data")
    private boolean omit_data = false;

    /**
     * 默认为false，填写true时Query接口不会返回图片
     * 该参数优先级低于omit_data参数
     */
    @JSONField(name = "omit_image_data")
    private boolean omit_image_data = false;

    /**
     * 默认为false，填写true时Query接口不会返回视频
     * 该参数优先级低于omit_data参数
     */
    @JSONField(name = "omit_video_data")
    private boolean omit_video_data = false;

}
