package com.timevale.faceauth.service.domain.provider;

import com.timevale.faceauth.service.apispace.facade.com.timevale.apispace.enums.MarkStatusEnum;
import com.timevale.infoauth.service.enums.ProvResult;

/**
 * 供应商返回信息
 *
 * <AUTHOR>
 * @since 2024/6/24 15:50
 */
public interface ProviderOut {

    /**
     * 供应商标识
     * @return
     */
    String getProvider();
    String getFullName();

    /** 供应商请求串号 */
    String getProvOrderNo();

    /** 适配错误码 */
    ProvResult getProvResult();

    /** 供应商响应字符串 */
    String getProvResultString();

    /** 第三方服务提供商的原始错误码 */
     String getProvErrorCode();

    /** 第三方服务商原始错误信息 */
     String getProvErrorMessage();

}
