package com.timevale.faceauth.service.domain.provider.tiktok.face.domain;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * 「签名算法」https://developer.open-douyin.com/docs/resource/zh-CN/mini-app/develop/server/signature-algorithm
 * <p>
 * * 在鉴权认证机制中，需要 4 个密钥，分别为：
 * * 平台公钥：
 * * 每个「小程序」对应的平台公钥是不一样的，平台公钥由「开放平台」负责生成，并告知「小程序」开发者。
 * * 平台私钥：
 * * 每个「小程序」对应的平台私钥是不一样的，平台私钥由「开放平台」负责生成和保存。
 * * 应用公钥：
 * * 「小程序」的应用公钥由开发者生成并上传到「开放平台」，可支持更换。
 * * 应用私钥：
 * * 「小程序」的应用私钥由开发者生成并保存，不能对外提供，可支持更换。
 *
 * <AUTHOR>
 * @DATE 2024/5/17 10:12
 */
@Data
public class TiktokAppSecretDomain extends ToString {

    /**
     * 抖音开放平台应用ID
     */
    private String tiktokAppId = "tt395e4fe3a66f241b01";

    /**
     *
     */
    private String tiktokAppSecret = "cc0510409980ed485fd517dfc84c61940d5cada4";

    /**
     * 平台公钥  每个「小程序」对应的平台公钥是不一样的，平台公钥由「开放平台」负责生成，并告知「小程序」开发者。
     */
    private String platformPublishKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA00J/vo2DVFWBf4WzQYSb" +
            "C1Q9DK9r1MKrpN/Ju6T1zRQh8Un5TvooZLNsz9Aj06GBv5S/1xzZg0Em0iWL687t" +
            "eavlf6mGPvy9xvjRqH/a6Y2fBdCn7pV6aOxmsqbuGhuzp/TFk4IvXhoML7adXnpy" +
            "H9DIvkLfpAfMpwdxZgs64lYGcdGMdC52FdnOf+2jZe7ZY1Oi2frDT/M9AA5MfZWF" +
            "/xBH9Z06MSsA6A0Ma2ECfox5yfJp8ne/4aLNNou/1szINYSjOWSwku1nYcRHwa0y" +
            "8Nv4wlPD7vdUPFYv5skdGlP4zBAfZY39kgIsE4Q3ftWjn50lzczeH1g+HKt8zfVU" +
            "8wIDAQAB";

    /**
     * 应用私钥  「小程序」的应用私钥由开发者生成并保存，不能对外提供，可支持更换。  注意：在抖音控制台只能看到应用公钥
     *
     */
    private String appPrivateKey =
            "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCfzEIBH2ycRpYj" +
                    "zcnBkQAw/kz8xBBkjPuId//50Rj70ebscpW8TkvKiLHOBuyZuiZamE3Z8AwisoD8" +
                    "/KPeDL8CnIjb4VlklGg7N2M7aOWq/+QAVmGAc+J9iXTyBFHcRzd75pFoMf44lZOx" +
                    "uSSYcSmCNZQVV/muJ3mDyIxPgIdm/vXmVhdr9xVP+tSEhs0+VrIyegfrREaBlINL" +
                    "UweHIG/7b7u45mWlKj4H6DVACwXcB3bcD9FZMdEZ9tZp8R1xOEYGb3Wvvlh45JHw" +
                    "CcvkcPyN/2IEFrIHeExht3+CiBISfLHMwq0b1TKu6BkYqEozpsAZm94OM2RAkrYr" +
                    "JQ6oDQCBAgMBAAECggEAdXRiU4bwgxGTC/AXyvI0jM5uch/+2JmTzOQ/PzVFQtsu" +
                    "/D7g9o8qQE0nPAN05y2fQVnPZ1B86wf/rXRZUj5WK6o5Ij1KQpae4l+8zY81uPHr" +
                    "eaAEfLExGgrt7XHlZZ7nFmgMGuUcMiUncStfzbjnxLBzDxkiCbM0Kvw+vbUAUWAO" +
                    "ZRgTJVn6simlp0XfMsnSuircRyD6rNYInKSxaGXXC8+8sf0FQqVPDsbXL1PbnZtL" +
                    "MjGT9DkEQDDaPRl6Cb1VLE2SXKtsUgn1bXs3xpjHIgEUT1UumPTAzUpbgdkQvKy7" +
                    "UfDyBMk+9EZMIX6YWpDPBvIJjERcOC+bQlhQi32tkQKBgQDTpdwrsG06e4DX8lRh" +
                    "PhKXO23dMte79UrI2g1FsIQbsxWUlGlvFRpPTQ7+NlZ837ITOhsvbaOrKW2QPU5Y" +
                    "l1OAqsfCeUXWk4mOH3SR/LdWtTK7kKfwOqPAjGlDaHLxKV1oNw1pe8YPjYcHZjWw" +
                    "pm+R2/KPFRBt7Wu9Sk62ZHbd1QKBgQDBSNVTxpCxtjLT59zAUNkiY6j+Ev2aqCyZ" +
                    "AYfASpXkIR35qdE80iYraMSjSTpfjrVlF20iwL4br+AX3A91Vf+1yJB/yLarhWsh" +
                    "iS+SKvBUQufWReQT/0njtORpAeo3i8nIuRLw+iMGbK7SbMb+SQUyDtNAn2ihpGpo" +
                    "2P7YrqYx/QKBgGWcc5/mDArP0IPcq/YjvvksZwqTpk9u7ICRb13N6R8DU5EJz7St" +
                    "BPydml0h6VP6cZwZgYO7t6AtO5TMWhjeThCv7UfIwWbLXncFUwnjsoHGbJzwTjBf" +
                    "ds6ymrbqJoTxvw6Rn7bM3cIJyJIc2Bn/TgFQ/5sCGeks21T9n9yhTxAhAoGBAJB/" +
                    "hdR6t0PUZY1cYwF3r8zw2q8PvX64yZaduUI9wG9rfsBef2nL5fNAAPndzgEQbHYm" +
                    "HFYF6FpEkvDjeL8Myv248qZFhKMYPG425FSPO1qTJkVfvNJiYXgVjUkfwF1EaVdh" +
                    "XVLAfhzYdZ4k6mPCbVruEGrYgd2OUGsQBv4xG1h1AoGBAJfeX0BOCmyHSw4GRT59" +
                    "EngYFYtV7sIsfjJOpk8urM6CIQUbiFXof5ur/ndXcb5+PPjNFR3P/lukDTaY1zdP" +
                    "lzrUb62yNI9avHy8f397lQJe26U7zNI1HpPlsK98vThnLrhHGtP5LRE+HduZLsVc" +
                    "romB66ZmQ3JInzZCAzI3dqxn";



}
