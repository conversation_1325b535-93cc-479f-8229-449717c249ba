package com.timevale.faceauth.service.domain.provider.tencentsdks.entity.dto;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 *
 * ios
 * {
 * "faceAuthCode": "7549dcc689364ea6b0bb94a1a6e2550d",
 * "extendedMap": {
 * "originalReturnData": "{\"code\":12001,\"desc\":\"传入的licence不可用\",\"reason\":\"bundle id 和 licence 不匹配\",\"domain\":\"WBFaceErrorDomainInputParams\"}"
 * },
 * "faceAuthMode": "APP_FACE_SDK",
 * "pathDic": {}
 * }
 *
 *安卓
 * {"extendedMap":{"originalReturnData":"WbFaceError{domain='WBFaceErrorDomainParams', code='11001', desc='传入keyLicence不可用', reason='传入keyLicence不可用(3004)'}"},"faceAuthMode":"APP_FACE_SDK","faceAuthCode":"1c0ef491414d47f08b50ccc92e16c933"}
 * <AUTHOR>
 * @DATE 2024/8/29 16:19
 */
@Data
public class SdkOriginalReturnDataDTO extends ToString {
    private String domain;
    private Integer code;
    private String desc;
    private String reason;
}
