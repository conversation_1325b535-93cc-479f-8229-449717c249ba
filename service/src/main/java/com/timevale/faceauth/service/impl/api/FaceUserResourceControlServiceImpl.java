package com.timevale.faceauth.service.impl.api;

import com.timevale.faceauth.dal.pfs.dataobject.FaceUserResourceControlRecordDO;
import com.timevale.faceauth.dal.pfs.dataobject.query.FaceUserResourceControlRecordQuery;
import com.timevale.faceauth.service.api.FaceUserResourceControlService;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.inner.FaceUserResourceControlInnerService;
import com.timevale.faceauth.service.inner.impl.control.FaceUserResourceControlInnerServiceImpl;
import com.timevale.faceauth.service.inner.impl.control.convert.FaceUserResourceControlConvert;
import com.timevale.faceauth.service.input.control.ControlRecordInput;
import com.timevale.faceauth.service.input.control.ControlRecordPageInput;
import com.timevale.faceauth.service.input.control.ControlRecordQueryDetailInput;
import com.timevale.faceauth.service.input.control.ControlRecordSaveInput;
import com.timevale.faceauth.service.input.control.SwitchStatusControlRecordInput;
import com.timevale.faceauth.service.result.BaseFaceAuthResult;
import com.timevale.faceauth.service.result.SupportResult;
import com.timevale.faceauth.service.result.control.ControlRecordPageResult;
import com.timevale.faceauth.service.result.control.ControlRecordResult;
import com.timevale.faceauth.service.utils.FaceAuthValidationUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.RestService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR>
 * @DATE 2025/7/2 14:26
 */
@Slf4j
@RestService
@Api(tags = "刷脸资源控制管理")
public class FaceUserResourceControlServiceImpl implements FaceUserResourceControlService {

    @Autowired
    private FaceUserResourceControlInnerService faceUserResourceControlInnerService;

    // Times for invoke provider api
    @Value("${face.resource.control.ProofDocument.max.size:6}")
    private int proofDocumentMaxSize = 6;



    /**
     * 批量保存控制记录
     *
     * @param input 控制记录保存请求参数
     * @return 操作结果封装对象，包含基础人脸认证结果
     */
    @Override
    public SupportResult<BaseFaceAuthResult> batchSaveControlRecord(ControlRecordSaveInput input) {
        FaceAuthValidationUtils.notNull(input, FaceException.valueOf(FaceStatusCode.NULL_ARGUMENT, "input不能为空"));
        FaceAuthValidationUtils.validateBean(input);
        FaceAuthValidationUtils.notEmpty(input.getProofDocumentList(), FaceException.valueOf(FaceStatusCode.NULL_ARGUMENT, "证明材料不能为空"));
        FaceAuthValidationUtils.assertTrue(input.getProofDocumentList().size() <= proofDocumentMaxSize, FaceException.valueOf(FaceStatusCode.NULL_ARGUMENT, "证明材料举证数量超出限制，请减少后在保存"));

        faceUserResourceControlInnerService.batchSaveControlRecord(input);
        return SupportResult.success(null);
    }

    /**
     * 查询控制记录详情
     *
     * @param input 控制记录详情查询参数
     * @return 操作结果封装对象，包含控制记录详细信息
     */
    @Override
    public SupportResult<ControlRecordResult> detailControlRecord(ControlRecordQueryDetailInput input) {
        FaceAuthValidationUtils.notNull(input, FaceException.valueOf(FaceStatusCode.NULL_ARGUMENT, "input不能为空"));
        FaceAuthValidationUtils.validateBean(input);
        FaceUserResourceControlRecordDO recordDO = faceUserResourceControlInnerService.selectOneByRecordId(input.getRecordId());
        FaceAuthValidationUtils.notNull(recordDO, FaceException.valueOf(FaceStatusCode.NULL_ARGUMENT, "未查询到记录"));

        ControlRecordResult result = FaceUserResourceControlConvert.convert(recordDO, true);
        return SupportResult.success(result);
    }

    /**.
     * 修改控制记录
     *
     * @param input 控制记录修改请求参数
     * @return 操作结果封装对象，包含更新后的基础人脸认证结果
     */
    @Override
    public SupportResult<BaseFaceAuthResult> modifyControlRecord(ControlRecordInput input) {

        FaceAuthValidationUtils.notNull(input, FaceException.valueOf(FaceStatusCode.NULL_ARGUMENT, "input不能为空"));
        FaceAuthValidationUtils.validateBean(input);

        FaceAuthValidationUtils.notEmpty(input.getProofDocumentList(), FaceException.valueOf(FaceStatusCode.NULL_ARGUMENT, "证明材料不能为空"));
        FaceAuthValidationUtils.assertTrue(input.getProofDocumentList().size() <= proofDocumentMaxSize, FaceException.valueOf(FaceStatusCode.NULL_ARGUMENT, "证明材料举证数量超出限制，请减少后在保存"));

        FaceAuthValidationUtils.notBlank(input.getRecordId(), FaceException.valueOf(FaceStatusCode.NULL_ARGUMENT, "recordId 不能为空"));
        FaceAuthValidationUtils.notBlank(input.getAppId(), FaceException.valueOf(FaceStatusCode.NULL_ARGUMENT, "appId 不能为空"));

        FaceUserResourceControlRecordDO recordDO = JsonUtils.obj2pojo(input, FaceUserResourceControlRecordDO.class);
        String documents = FaceUserResourceControlInnerServiceImpl.parseProofDocuments(input);
        if (StringUtils.isNotBlank(documents)) {
            recordDO.setProofDocuments(documents);
        }
        faceUserResourceControlInnerService.modifyControlRecord(recordDO);

        return SupportResult.success(null);
    }

    /**
     * 分页查询控制记录
     *
     * @param input 分页查询请求参数
     * @return 操作结果封装对象，包含分页返回数据及控制记录列表
     */
    @Override
    public SupportResult<ControlRecordPageResult> pageControlRecord(ControlRecordPageInput input) {

        FaceAuthValidationUtils.notNull(input, FaceException.valueOf(FaceStatusCode.NULL_ARGUMENT, "input不能为空"));
        FaceAuthValidationUtils.validateBean(input);

         FaceUserResourceControlRecordQuery query = JsonUtils.obj2pojo(input, FaceUserResourceControlRecordQuery.class);

        ControlRecordPageResult re = faceUserResourceControlInnerService.pageControlRecord(query);

        return SupportResult.success(re);
    }

    @Override
    public SupportResult<BaseFaceAuthResult> switchStatusControlRecord(SwitchStatusControlRecordInput input) {
        FaceAuthValidationUtils.notNull(input, FaceException.valueOf(FaceStatusCode.NULL_ARGUMENT, "input不能为空"));
        FaceAuthValidationUtils.validateBean(input);


        faceUserResourceControlInnerService.switchStatusControlRecord(input);
        return SupportResult.success(null);
    }

}
