package com.timevale.faceauth.service.inner.impl.sdk;

import com.alibaba.fastjson.JSON;
import com.timevale.faceauth.dal.pfs.dao.SdkCustomerChannelMappingDAO;
import com.timevale.faceauth.dal.pfs.dao.SdkCustomerDAO;
import com.timevale.faceauth.dal.pfs.dao.SdkCustomerVersionMappingDAO;
import com.timevale.faceauth.dal.pfs.dao.SdkVersionDAO;
import com.timevale.faceauth.dal.pfs.dataobject.SdkCustomerChannelMappingDO;
import com.timevale.faceauth.dal.pfs.dataobject.SdkCustomerDO;
import com.timevale.faceauth.dal.pfs.dataobject.SdkCustomerVersionMappingDO;
import com.timevale.faceauth.dal.pfs.dataobject.SdkVersionDO;
import com.timevale.faceauth.dal.pfs.dataobject.query.SdkCustomerDOQuery;
import com.timevale.faceauth.service.constant.SystemConfig;
import com.timevale.faceauth.service.convertor.SdkCustomerConvert;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.sdk.SdkChannelDomain;
import com.timevale.faceauth.service.enums.SdkStatusEnum;
import com.timevale.faceauth.service.inner.SdkManagerInnerService;
import com.timevale.faceauth.service.inner.impl.sdk.config.SdkCustomerConfigDomain;
import com.timevale.faceauth.service.input.sdk.BatchSaveCustomerChannelInput;
import com.timevale.faceauth.service.input.sdk.GetManualConfigInput;
import com.timevale.faceauth.service.input.sdk.GetSdkCustomerDetailInput;
import com.timevale.faceauth.service.input.sdk.QuerySdkCustomerListInput;
import com.timevale.faceauth.service.input.sdk.QuerySdkVersionListInput;
import com.timevale.faceauth.service.input.sdk.SaveSdkVersionInput;
import com.timevale.faceauth.service.input.sdk.UpdateBundleIdIdInput;
import com.timevale.faceauth.service.input.sdk.UpdateSdkVersionStatusInput;
import com.timevale.faceauth.service.result.sdk.GetManualConfigResult;
import com.timevale.faceauth.service.result.sdk.GetSdkCustomerDetailResult;
import com.timevale.faceauth.service.result.sdk.QuerySdkCustomerListResult;
import com.timevale.faceauth.service.result.sdk.QuerySdkVersionListResult;
import com.timevale.faceauth.service.result.sdk.VerifyAndQueryChannelSdkResult;
import com.timevale.faceauth.service.utils.FaceAuthValidationUtils;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import esign.utils.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2024/8/6 15:45
 */
@Service
@Slf4j
public class SdkManagerInnerServiceImpl implements SdkManagerInnerService {


    @Resource
    private SdkVersionDAO sdkVersionDAO;
    @Resource
    private SdkCustomerDAO sdkCustomerDAO;
    @Resource
    private SdkCustomerVersionMappingDAO sdkCustomerVersionMappingDAO;
    @Resource
    private SdkCustomerChannelMappingDAO sdkCustomerChannelMappingDAO;

    @Autowired
    private SystemConfig systemConfig;


    /**
     * 查询全量的SDK列表
     *
     * @return
     */
    @Override
    public QuerySdkVersionListResult querySdkVersionList(QuerySdkVersionListInput input) {
        String md5 = MD5Util.md5(JSON.toJSONString(input));

        List<SdkVersionDO> sdkVersionDOS = sdkVersionDAO.query(input.getPageNum(), input.getPageSize());
        Long count = sdkVersionDAO.countALl();
        QuerySdkVersionListResult result = SdkCustomerConvert.convert(count, sdkVersionDOS);
        return result;
//        //进行缓存
//        return CacheUtil.get(SDK_VERSION_CACHE_KEY + md5, () -> {
//
//        });
    }

    @Override
    public String saveSdkVersion(SaveSdkVersionInput input) {
        SdkVersionDO sdkVersionDO = new SdkVersionDO();
        sdkVersionDO.setSdkVersionId(SdkCustomerConvert.getSdkVersionId());
        sdkVersionDO.setSdkVersion(input.getSdkVersion());
        sdkVersionDO.setStatus(SdkStatusEnum.ENABLE.name());
        sdkVersionDO.setOperator("rpc");
        int insert = sdkVersionDAO.insert(sdkVersionDO);
        return sdkVersionDO.getSdkVersionId();
    }

    @Override
    public List<SdkChannelDomain> queryAllSdkChannel() {
        List<SdkCustomerConfigDomain.ConfigItem> sdkChannels = systemConfig.getSdkCustomerConfigDomain().getSdkChannels();
        return Optional.ofNullable(sdkChannels).orElse(Collections.emptyList())
                .stream().map(e -> {
                    SdkChannelDomain channelDomain = new SdkChannelDomain();
                    channelDomain.setChannelCode(e.getCode());
                    channelDomain.setChannelDesc(e.getName());
                    channelDomain.setStatus(e.getStatus());
                    channelDomain.setAppId(e.getAppId());
                    return channelDomain;
                }).collect(Collectors.toList());
    }

    @Override
    public void updateSdkVersionStatus(UpdateSdkVersionStatusInput input) {
        int i = sdkVersionDAO.updateStatus(input.getSdkVersionId(), input.getStatus(), input.getOperator());
        FaceAuthValidationUtils.assertTrue(i == 1, FaceException.valueOf(FaceStatusCode.NULL_ARGUMENT, "更新失败"));
    }


    @Override
    public QuerySdkCustomerListResult querySdkCustomerList(QuerySdkCustomerListInput input) {

        SdkCustomerDOQuery query = SdkCustomerConvert.convert(input);

        Long count = sdkCustomerDAO.count(query);

        List<SdkCustomerDO> list = Optional.ofNullable(sdkCustomerDAO.page(query)).orElse(Collections.emptyList());
        List<String> sdkCustomerIds = list.stream().map(SdkCustomerDO::getSdkCustomerId).distinct().collect(Collectors.toList());
        List<SdkCustomerChannelMappingDO> channelMappingDOS = listChannelBySdkCustomerIds(sdkCustomerIds);
        List<SdkCustomerVersionMappingDO> versionMappingDOS = listVersionBySdkCustomerIds(sdkCustomerIds);

        QuerySdkCustomerListResult result = SdkCustomerConvert.convertQuerySdkCustomerListResult(list, channelMappingDOS, versionMappingDOS, getChannelMap());
        result.setCount(Optional.ofNullable(count).orElse(0L));
        return result;
    }


    private List<SdkCustomerChannelMappingDO> listChannelBySdkCustomerIds(List<String> sdkCustomerIds) {
        if (CollectionUtils.isEmpty(sdkCustomerIds)) {
            return Lists.newArrayList();
        }
        // 一个sdkCustomerId 对应的应该只有一种包名  每种版本数据只有一条
        return Optional.ofNullable(sdkCustomerChannelMappingDAO.listChannelBySdkCustomerIds(sdkCustomerIds)).orElse(Lists.newArrayList());
    }

    private List<SdkCustomerVersionMappingDO> listVersionBySdkCustomerIds(List<String> sdkCustomerIds) {
        if (CollectionUtils.isEmpty(sdkCustomerIds)) {
            return Lists.newArrayList();
        }
        return Optional.ofNullable(sdkCustomerVersionMappingDAO.listVersionBySdkCustomerIds(sdkCustomerIds)).orElse(Lists.newArrayList());
    }


    @Override
    @Transactional
    public String batchSaveCustomerChannel(BatchSaveCustomerChannelInput input) {
        if (CollectionUtils.isEmpty(input.getCustomerChannelList()) || StringUtils.isBlank(input.getOrgGid())) {
            return null;
        }

        List<BatchSaveCustomerChannelInput.BatchSaveCustomerChannelItem> channelList = input.getCustomerChannelList();
        SdkCustomerDO sdkCustomerDO = this.getSdkCustomerDO(input);
        String sdkCustomerId = sdkCustomerDO.getSdkCustomerId();
        List<SdkCustomerChannelMappingDO> channelMappingDOS = listChannelBySdkCustomerIds(Lists.newArrayList(sdkCustomerId));
        Map<String, SdkCustomerChannelMappingDO> bundleIdMap = channelMappingDOS.stream().collect(Collectors.toMap(SdkCustomerChannelMappingDO::getBundleId, Function.identity(), (o, n) -> o));
        Map<String, SdkCustomerChannelMappingDO> channelIdMap = channelMappingDOS.stream().collect(Collectors.toMap(SdkCustomerChannelMappingDO::getMappingId, Function.identity(), (o, n) -> o));

        // 批量保存客户渠道信息
        for (BatchSaveCustomerChannelInput.BatchSaveCustomerChannelItem batchSaveCustomerChannelItem : channelList) {
            String customerChannelId = batchSaveCustomerChannelItem.getCustomerChannelId();
            String channelCode = batchSaveCustomerChannelItem.getChannelCode();
            String bundleId = batchSaveCustomerChannelItem.getBundleId();

            String status = StringUtils.isAllBlank(batchSaveCustomerChannelItem.getChannelLicense(),batchSaveCustomerChannelItem.getHarmonyOsLicense()) ? SdkStatusEnum.DISENABLE.name() : SdkStatusEnum.ENABLE.name();
            if (StringUtils.isBlank(customerChannelId)) {
                //新增
                FaceAuthValidationUtils.assertTrue(Objects.isNull(bundleIdMap.get(bundleId)), FaceException.valueOf(FaceStatusCode.ILLEGAL_ARGUMENT, bundleId + "重复添加"));
                SdkCustomerChannelMappingDO channelMappingDO = new SdkCustomerChannelMappingDO();
                channelMappingDO.setMappingId(SdkCustomerConvert.getSdkCustomerChannelId());
                channelMappingDO.setSdkCustomerId(sdkCustomerId);
                channelMappingDO.setChannelCode(channelCode);
                channelMappingDO.setChannelLicense(batchSaveCustomerChannelItem.getChannelLicense());
                // 2025/5/7
                channelMappingDO.setHarmonyOsLicense(batchSaveCustomerChannelItem.getHarmonyOsLicense());
                channelMappingDO.setStatus(status);
                channelMappingDO.setOperator(input.getOperator());
                //不允许编辑 bundleId
                channelMappingDO.setBundleId(batchSaveCustomerChannelItem.getBundleId());
                sdkCustomerChannelMappingDAO.insert(channelMappingDO);
            } else {
                SdkCustomerChannelMappingDO channelMappingDO = channelIdMap.get(customerChannelId);
                //修改
                FaceAuthValidationUtils.notNull(channelMappingDO, FaceException.valueOf(FaceStatusCode.ILLEGAL_ARGUMENT, customerChannelId + " 错误的channelId"));
                FaceAuthValidationUtils.assertTrue(StringUtils.equals(channelMappingDO.getChannelCode(), channelCode), FaceException.valueOf(FaceStatusCode.ILLEGAL_ARGUMENT, channelCode + "错误的channelCode"));
                sdkCustomerChannelMappingDAO.updateChannelLicense(channelMappingDO.getId(), batchSaveCustomerChannelItem.getChannelLicense(),
                        // 2025/5/7
                        batchSaveCustomerChannelItem.getHarmonyOsLicense(),
                        input.getOperator(), status);
            }
        }

        return sdkCustomerId;
    }

    private SdkCustomerDO getSdkCustomerDO(BatchSaveCustomerChannelInput input) {
        String orgGid = input.getOrgGid();
        String bundleId = input.findFirstBundleId();
        final Pair<SdkCustomerDO, List<SdkCustomerChannelMappingDO>> pair = this.selectByGidAndBundleId(orgGid, bundleId);
        SdkCustomerDO sdkCustomerDO = pair.getLeft();
        if (sdkCustomerDO == null) {
            sdkCustomerDO = saveCustomer(input.getOperator(), input.getOrgGid(), input.getOrgName());
        } else {
            SdkCustomerDO update = new SdkCustomerDO();
            update.setId(sdkCustomerDO.getId());
            update.setOperator(input.getOperator());
            sdkCustomerDAO.updateOperator(update);
        }
        return sdkCustomerDO;
    }

    /**
     * 保存客户信息
     *
     * @return
     */
    private SdkCustomerDO saveCustomer(String operator, String orgGid, String orgName) {
        String sdkCustomerId = SdkCustomerConvert.getSdkCustomerId();
        SdkCustomerDO sdkCustomerDO = new SdkCustomerDO();
        sdkCustomerDO.setOperator(operator);
        sdkCustomerDO.setOrgGid(orgGid);
        sdkCustomerDO.setStatus(SdkStatusEnum.ENABLE.name());
        sdkCustomerDO.setSdkCustomerId(sdkCustomerId);
        sdkCustomerDO.setOrgName(orgName);
        sdkCustomerDAO.insert(sdkCustomerDO);
        return sdkCustomerDO;
    }


    @Override
    public GetSdkCustomerDetailResult getSdkCustomerDetail(GetSdkCustomerDetailInput input) {
        GetSdkCustomerDetailResult result = new GetSdkCustomerDetailResult();
        SdkCustomerDO sdkCustomerDO = sdkCustomerDAO.selectByCustomerId(input.getSdkCustomerId());
        if (Objects.isNull(sdkCustomerDO)) {
            return result;
        }
        List<SdkCustomerChannelMappingDO> mappingDOS = listChannelBySdkCustomerIds(Lists.newArrayList(sdkCustomerDO.getSdkCustomerId()));
        return SdkCustomerConvert.convertGetSdkCustomerDetailResult(sdkCustomerDO, mappingDOS, getChannelMap());
    }

    public Map<String, String> getChannelMap() {
        return queryAllSdkChannel().stream().collect(Collectors.toMap(SdkChannelDomain::getChannelCode, SdkChannelDomain::getChannelDesc, (o, m) -> o));
    }


    @Override
    public GetManualConfigResult getManualConfig(GetManualConfigInput input) {
        List<SdkChannelDomain> sdkChannelDomains = queryAllSdkChannel();

        List<SdkVersionDO> sdkVersionDOS = sdkVersionDAO.listAll();
        return SdkCustomerConvert.convertSdkCustomerConvert(sdkChannelDomains, sdkVersionDOS);
    }

    /**
     * 根据组织全局标识符（GID）和套餐标识符（bundleId）选择客户信息和渠道映射信息
     * 此方法首先从数据库中获取符合GID条件的客户信息，然后获取这些客户的渠道映射信息，
     * 并根据bundleId进行过滤，最后返回与bundleId匹配的客户信息和渠道映射信息
     *
     * @param orgGid   组织的全局标识符，用于查询客户信息
     * @param bundleId 套餐标识符，用于过滤渠道映射信息
     * @return 返回一个Pair对象，包含客户信息和与指定bundleId匹配的渠道映射信息列表
     */
    private Pair<SdkCustomerDO, List<SdkCustomerChannelMappingDO>> selectByGidAndBundleId(String orgGid, String bundleId) {
        // 根据组织GID查询客户信息
        final List<SdkCustomerDO> customerDOS = sdkCustomerDAO.listByGid(orgGid);
        // 如果没有找到任何客户信息，则返回空Pair对象
        if (CollectionUtils.isEmpty(customerDOS)) {
            return Pair.of(null, Collections.emptyList());
        }

        // 提取所有客户的SDK客户ID，并去重，用于后续查询渠道映射信息
        final List<String> sdkIds = customerDOS.stream().map(SdkCustomerDO::getSdkCustomerId).distinct().collect(Collectors.toList());
        // 将客户信息列表转换为Map，以便通过SDK客户ID快速查询客户信息实体
        final Map<String, SdkCustomerDO> mapSkdIdEntity = customerDOS.stream().collect(Collectors.toMap(SdkCustomerDO::getSdkCustomerId, Function.identity(), (o, n) -> o));

        // 根据SDK客户ID列表查询渠道映射信息
        List<SdkCustomerChannelMappingDO> channelMappingDOS = this.listChannelBySdkCustomerIds(sdkIds);
        List<SdkCustomerChannelMappingDO> filterList = channelMappingDOS.stream()
                .filter(e -> StringUtils.equals(e.getBundleId(), bundleId)).collect(Collectors.toList());

        // 从过滤后的渠道映射信息列表中获取一个SDK客户ID，如果列表为空则返回null
        final String sdkId = filterList.stream().map(SdkCustomerChannelMappingDO::getSdkCustomerId).findAny().orElse(null);
        // 返回与获取到的SDK客户ID对应的客户信息实体和过滤后的渠道映射信息列表
        return Pair.of(mapSkdIdEntity.get(sdkId), filterList);
    }


    @Override
    public Pair<VerifyAndQueryChannelSdkResult, String> verifyAndQueryChannelList(String orgGid, String sdkVersion, String bundleId) {
        this.verify(orgGid, sdkVersion);

        VerifyAndQueryChannelSdkResult result = new VerifyAndQueryChannelSdkResult();
        result.setUrlDomain(systemConfig.getSdkCustomerConfigDomain().getUrlDomain());


        final Pair<SdkCustomerDO, List<SdkCustomerChannelMappingDO>> pair = this.selectByGidAndBundleId(orgGid, bundleId);

        if (Objects.isNull(pair.getLeft())) {
            return Pair.of(result, null);
        }

        final SdkCustomerDO customerDO = pair.getLeft();

        // 查询所有SDK渠道并创建渠道代码到应用ID的映射
        Map<String, String> codeToApIdMap = queryAllSdkChannel().stream().collect(Collectors.toMap(SdkChannelDomain::getChannelCode, SdkChannelDomain::getAppId, (o, m) -> o));

        // 找配置的渠道信息
        List<SdkCustomerChannelMappingDO> channelMappingDOS = pair.getRight();

        // 将过滤后的渠道信息转换为AppSdkEntity对象列表
        List<VerifyAndQueryChannelSdkResult.AppSdkEntity> sdkEntities = channelMappingDOS.stream()
                .filter(SdkCustomerChannelMappingDO::hasLicense)
                .filter(e -> StringUtils.equals(e.getBundleId(), bundleId))
                .map(item -> {
                    VerifyAndQueryChannelSdkResult.AppSdkEntity appSdkEntity = new VerifyAndQueryChannelSdkResult.AppSdkEntity();
                    appSdkEntity.setSdkChannel(item.getChannelCode());
                    appSdkEntity.setAppSdkLicense(item.getChannelLicense());
                    appSdkEntity.setWbAppId(codeToApIdMap.get(item.getChannelCode()));
                    // 2025/5/7
                    appSdkEntity.setHarmonyOsLicense(item.getHarmonyOsLicense());
                    return appSdkEntity;
                }).collect(Collectors.toList());
        result.setInlayAppSdkLicenseList(sdkEntities);

        // 返回结果对象和sdkCustomerId的Pair对象
        return Pair.of(result, customerDO.getSdkCustomerId());
    }

    private void verify(String orgGid, String sdkVersion) {
        SdkVersionDO sdkVersionDO = sdkVersionDAO.selectByVersion(sdkVersion);
        if (Objects.isNull(sdkVersionDO)) {
            sdkVersionDO = new SdkVersionDO();
            sdkVersionDO.setSdkVersionId(SdkCustomerConvert.getSdkVersionId());
            sdkVersionDO.setSdkVersion(sdkVersion);
            sdkVersionDO.setStatus(SdkStatusEnum.ENABLE.name());
            sdkVersionDO.setOperator("admin");
            int insert = sdkVersionDAO.insert(sdkVersionDO);
            log.info("发现新版本SDK  orgGid={} sdkVersion={} , 插入数据库结果={}", orgGid, sdkVersion, insert);
        } else {
            //后续如果客户风控识别危险可支持到GID维度不可用
            FaceAuthValidationUtils.assertEqual(sdkVersionDO.getStatus(), SdkStatusEnum.ENABLE.name(), FaceException.valueOf(FaceStatusCode.APP_SDK_NOT_SUPPORT_VERSION));
        }
    }

    @Override
    public void asyncSdkPoint(String orgGid, String sdkVersion, String sdkCustomerId) {
        try {
            if (StringUtils.isAnyBlank(sdkCustomerId)) {
                return;
            }

            SdkVersionDO sdkVersionDO = sdkVersionDAO.selectByVersion(sdkVersion);
            if (Objects.isNull(sdkVersionDO)) {
                return;
            }
            //更新SDK数据
            sdkVersionDO.setLatestActivateTime(new Date());
            int count = sdkVersionDAO.updateLatestActivateTime(sdkVersionDO);
            log.info("orgGid:{}, sdkVersion:{} 更新版本最近一次时间  更新行数{}", orgGid, sdkVersion, count);

            SdkCustomerDO sdkCustomerDO = sdkCustomerDAO.selectByCustomerId(sdkCustomerId);
            if (Objects.isNull(sdkCustomerDO)) {
                return;
            }

            SdkCustomerVersionMappingDO mappingDO = sdkCustomerVersionMappingDAO.selectByCIdAndVersion(sdkVersion, sdkCustomerDO.getSdkCustomerId());
            if (Objects.nonNull(mappingDO)) {
                count = sdkCustomerVersionMappingDAO.updateLatestActivateTime(mappingDO.getMappingId(), new Date());
                log.info("orgGid:{}, sdkVersion:{}  {} 更新客户版本最近一次时间 更新行数{}", orgGid, sdkVersion, mappingDO.getMappingId(), count);
            } else {

                SdkCustomerVersionMappingDO insertMapping = new SdkCustomerVersionMappingDO();
                insertMapping.setSdkVersion(sdkVersion);
                insertMapping.setLatestActivateTime(new Date());
                insertMapping.setStatus(SdkStatusEnum.ENABLE.name());
                insertMapping.setSdkCustomerId(sdkCustomerDO.getSdkCustomerId());
                insertMapping.setOperator("admin");
                insertMapping.setMappingId(SdkCustomerConvert.getSdkCustomerVersionId());
                sdkCustomerVersionMappingDAO.insert(insertMapping);
            }
        } catch (Exception e) {
            log.warn("asyncSdkPoint  记录SDK激活操作记录失败 error orgGid:{}, sdkVersion:{}", orgGid, sdkVersion, e);
        }

    }

    /**
     * 更新Bundle ID
     * 此方法用于更新特定SDK客户ID的Bundle ID首先，它检查给定的SDK客户ID是否存在然后，它确保新的Bundle ID不会与同一组织组内的其他SDK客户ID重复
     * 如果存在重复，它将抛出异常否则，它将更新Bundle ID
     *
     * @param input 包含要更新的SDK客户ID和新的Bundle ID的输入对象
     */
    @Override
    public void updateBundleIdId(UpdateBundleIdIdInput input) {
        // 根据客户ID查询SDK客户信息
        SdkCustomerDO sdkCustomerDO = sdkCustomerDAO.selectByCustomerId(input.getSdkCustomerId());
        // 验证客户ID是否存在
        FaceAuthValidationUtils.notNull(sdkCustomerDO, FaceException.valueOf(FaceStatusCode.NULL_ARGUMENT, "sdkCustomerId 未找到数据"));

        // 查询同一组织组内的所有客户
        List<SdkCustomerDO> customerDOS = sdkCustomerDAO.listByGid(sdkCustomerDO.getOrgGid());
        // 提取GID下所有SDK客户ID
        final List<String> sdkIds = customerDOS.stream().map(SdkCustomerDO::getSdkCustomerId).distinct().collect(Collectors.toList());

        // 根据SDK客户ID列表查询渠道映射信息
        List<SdkCustomerChannelMappingDO> channelMappingDOS = this.listChannelBySdkCustomerIds(sdkIds);
        // 过滤出与新Bundle ID相同但SDK客户ID不同的映射记录
        final List<SdkCustomerChannelMappingDO> equalMappingList = channelMappingDOS.stream()
                .filter(e -> StringUtils.equals(input.getBundleId(), e.getBundleId()))
                .filter(e -> !StringUtils.equals(input.getSdkCustomerId(), e.getSdkCustomerId()))
                .collect(Collectors.toList());

        // 验证是否有重复的Bundle ID
        FaceAuthValidationUtils.assertTrue(CollectionUtils.isEmpty(equalMappingList), FaceException.valueOf(FaceStatusCode.NULL_ARGUMENT, "bundleId 存在重复数据"));
        // 更新Bundle ID
        sdkCustomerChannelMappingDAO.updateBundleId(input.getSdkCustomerId(), input.getBundleId());
    }
}
