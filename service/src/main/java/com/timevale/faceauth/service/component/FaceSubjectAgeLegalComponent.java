package com.timevale.faceauth.service.component;

import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.exception.FaceAgeLegalAuthException;
import com.timevale.faceauth.service.utils.CardUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @DATE 2025/6/26 10:40
 */
@Component
public class FaceSubjectAgeLegalComponent {
    @Autowired
    private ConfigurableProperties configurableProperties;

    public void checkAge(String idno, String certType) {
        // 2025/6/23 低于十四岁不允许刷脸
        Pair<Boolean, Integer> personAge = CardUtils.parserPersonAge(idno);
        if (personAge.getLeft() && personAge.getRight() < configurableProperties.getFaceSubjectAgeNotLegal()) {
            throw new FaceAgeLegalAuthException(FaceStatusCode.FACE_SUBJECT_AGE_NOT_LEGAL);
        }
    }


}
