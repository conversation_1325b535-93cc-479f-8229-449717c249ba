package com.timevale.faceauth.service.inner.impl.afterface.impl;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.provider.ConfigurableProviderServices;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationDelayService;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationResult;
import com.timevale.faceauth.service.domain.repository.ProviderFaceInfo;
import com.timevale.faceauth.service.domain.repository.ProviderFaceRepository;
import com.timevale.faceauth.service.enums.FaceResourceFaceAuthModeEnum;
import com.timevale.faceauth.service.exception.ProviderException;
import com.timevale.faceauth.service.inner.impl.afterface.ProviderFaceResourceService;
import com.timevale.faceauth.service.input.HandleFaceAuthorizationReturnInput;
import com.timevale.faceauth.service.input.WakeupFaceInput;
import com.timevale.faceauth.service.result.HandleFaceAuthorizationReturnResult;
import com.timevale.faceauth.service.result.WakeupFaceResult;
import com.timevale.faceauth.service.utils.FaceAuthValidationUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @DATE 2024/8/13 14:23
 */
@Service
public class AppSdkProviderFaceResourceServiceImpl implements ProviderFaceResourceService {
    @Autowired
    private ProviderFaceRepository providerFaceRepository;

    @Override
    public WakeupFaceResult wakeupFace(WakeupFaceInput input) {
        ProviderFaceInfo providerFaceDO = providerFaceRepository.getByFaceId(input.getFaceAuthCode());
        FaceAuthValidationUtils.assertFalse(providerFaceDO.isDone(), FaceException.valueOf(FaceStatusCode.FACE_PROVIDER_COMPLETED));

        ProviderFaceAuthorizationDelayService service = ConfigurableProviderServices.getProviderService(providerFaceDO.getProvider());
        WakeupFaceResult wakeupFaceResult = service.handleWakeupFace(providerFaceDO.getFaceId(), input);
        return wakeupFaceResult;
    }



    @Override
    public HandleFaceAuthorizationReturnResult handleFaceAuthorizationReturn(HandleFaceAuthorizationReturnInput request) {

        //找faceId
        ProviderFaceInfo providerFaceDO = providerFaceRepository.getByFaceId(request.getFaceAuthCode());
        FaceAuthValidationUtils.notNull(providerFaceDO, FaceException.valueOf(FaceStatusCode.ILLEGAL_ARGUMENT, "人脸识别记录不存在"));
        String faceId = providerFaceDO.getFaceId();

        //do 处理逻辑
        ProviderFaceAuthorizationDelayService service =
                ConfigurableProviderServices.getProviderService(providerFaceDO.getProvider());
        ProviderFaceAuthorizationResult result = service.handleFaceAuthorizationResponse(faceId, request);

        HandleFaceAuthorizationReturnResult response = new HandleFaceAuthorizationReturnResult().setCompleted(result.isCompleted()).setPassed(result.isSuccess());

        if (Objects.nonNull(result.getError())) {
            //1.返回天谷错误码
            ProviderException error = result.getError();
            response.setErrCode(String.valueOf(error.getCode()));
            response.setMsg(error.getMsg());
        }
        return response;
    }


    @Override
    public FaceResourceFaceAuthModeEnum mode() {
        return FaceResourceFaceAuthModeEnum.APP_FACE_SDK;
    }
}
