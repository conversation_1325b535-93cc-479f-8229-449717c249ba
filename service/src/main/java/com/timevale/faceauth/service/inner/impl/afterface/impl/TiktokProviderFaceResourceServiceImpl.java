package com.timevale.faceauth.service.inner.impl.afterface.impl;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.provider.ConfigurableProviderService;
import com.timevale.faceauth.service.domain.provider.ConfigurableProviderServices;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationDelayService;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationResult;
import com.timevale.faceauth.service.domain.provider.tiktok.face.TiktokFaceService;
import com.timevale.faceauth.service.domain.provider.tiktok.face.domain.TiktokFaceResultStatusDomain;
import com.timevale.faceauth.service.domain.provider.tiktok.face.entity.request.TiktokProviderFaceInput;
import com.timevale.faceauth.service.domain.provider.tiktok.face.util.TiktokConfig;
import com.timevale.faceauth.service.domain.repository.ProviderFaceInfo;
import com.timevale.faceauth.service.domain.repository.ProviderFaceRepository;
import com.timevale.faceauth.service.enums.FaceAuthModeEnum;
import com.timevale.faceauth.service.enums.FaceResourceFaceAuthModeEnum;
import com.timevale.faceauth.service.exception.ProviderException;
import com.timevale.faceauth.service.inner.impl.afterface.ProviderFaceResourceService;
import com.timevale.faceauth.service.input.HandleFaceAuthorizationReturnInput;
import com.timevale.faceauth.service.input.WakeupFaceInput;
import com.timevale.faceauth.service.result.HandleFaceAuthorizationReturnResult;
import com.timevale.faceauth.service.result.WakeupFaceResult;
import com.timevale.faceauth.service.utils.FaceAuthValidationUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @DATE 2024/5/14 17:07
 */
@Service
public class TiktokProviderFaceResourceServiceImpl implements ProviderFaceResourceService {

    @Autowired
    private TiktokFaceService tiktokFaceService;

    @Autowired
    private ProviderFaceRepository providerFaceRepository;
    @Autowired
    private TiktokConfig tiktokConfig;

    @Override
    public WakeupFaceResult wakeupFace(WakeupFaceInput input) {


        Map<String, String> queryContext = Optional.ofNullable(input.getQueryContext()).orElseGet(HashMap::new);
        String miniAuthCode = queryContext.get(WakeupFaceInput.QueryContextConstant.tiktokMiniAuthCode);
        String sourceChannel = queryContext.get(WakeupFaceInput.QueryContextConstant.tiktokSourceChannel);
        FaceAuthValidationUtils.notBlank(miniAuthCode, FaceException.valueOf(FaceStatusCode.ILLEGAL_ARGUMENT, "扩展参数queryContext.miniAuthCode不能为空"));

        //获取抖音AID
        String aid = Optional.ofNullable(tiktokConfig.getTiktokAppCommonDomain().getTiktokSourceChannel()).orElseGet(HashMap::new).get(sourceChannel);
        FaceAuthValidationUtils.notBlank(aid, FaceException.valueOf(FaceStatusCode.ILLEGAL_ARGUMENT, "sourceChannel = " + sourceChannel + "未定义的渠道类型"));

        //查询人脸识别记录
        ProviderFaceInfo providerFaceDO = providerFaceRepository.getByProviderOrder(getProvider(input.getFaceAuthModeValue()), input.getFaceAuthCode());
        FaceAuthValidationUtils.notNull(providerFaceDO, FaceException.valueOf(FaceStatusCode.ILLEGAL_ARGUMENT, "人脸识别记录不存在"));
        //  fixme : 数据库内流程已经完结 ,则报错返回
        FaceAuthValidationUtils.assertFalse(providerFaceDO.isDone(), FaceException.valueOf(FaceStatusCode.FACE_PROVIDER_COMPLETED));

        //请求供应商上下文faceInput
        TiktokProviderFaceInput providerFaceInput = JsonUtils.json2pojo(providerFaceDO.getFaceInput(), TiktokProviderFaceInput.class);
        String openId = tiktokFaceService.getOpenId(miniAuthCode);
        //补充aid,openId 进去
        providerFaceInput.setAid(aid).setOpenId(openId);

        //设置人脸识别记录的姓名
        WakeupFaceResult response = new WakeupFaceResult().setIdentityName(providerFaceInput.getName()).setFaceAuthMode(FaceAuthModeEnum.FACE_TIKTOK_MINI.name());
        //判断是否已经获取到抖音ticket
        if (StringUtils.isNotBlank(providerFaceDO.getThirdpardId())) {
            //  fixme ticket 绑定openId 是否一致  防止有人重复刷
            FaceAuthValidationUtils.assertStringEqualIgnoreCase(openId, providerFaceInput.getOpenId(), FaceException.valueOf(FaceStatusCode.FACE_REPEATED_AND_NOT_ONE_ERROR));

            //  fixme :  有ticket重复获取，可能已经是刷过脸之后，需要在走一次供应商  如果完结就快速报错返回
            HandleFaceAuthorizationReturnResult returnResponse = handleFaceAuthorizationReturn(new HandleFaceAuthorizationReturnInput().setFaceAuthCode(input.getFaceAuthCode()).setFaceAuthModeValue(input.getFaceAuthModeValue()));
            FaceAuthValidationUtils.assertFalse(returnResponse.isCompleted(), FaceException.valueOf(FaceStatusCode.FACE_PROVIDER_COMPLETED));
            return response.setTicket(providerFaceDO.getThirdpardId());
        }

        FaceAuthValidationUtils.notBlank(openId, FaceException.valueOf(FaceStatusCode.PROVIDER_AUTH_FAIL, "抖音授权信息已经过期，无法获取抖音账号信息"));

        //调用抖音接口
        String ticket = tiktokFaceService.getTicket(openId, aid, providerFaceInput.getName(), providerFaceInput.getIdNo());
        //更新抖音票据到三方ID上面
        providerFaceRepository.updateProviderFaceThirdpartId(ticket, JsonUtils.obj2json(providerFaceInput), providerFaceDO.getFaceId());

        return response.setTicket(ticket);
    }

    /**
     * 目前只支持抖音刷脸
     *
     * @param faceAuthModeValue
     * @return
     * @see com.timevale.faceauth.service.impl.support.SimpleFaceRequestResolver#PROVIDER_MAPPER
     */
    private String getProvider(Integer faceAuthModeValue) {
        if (Objects.equals(FaceAuthModeEnum.FACE_TIKTOK_MINI.getMode(), faceAuthModeValue)) {
            return ConfigurableProviderService.PROVIDER_FACE_TIKTOK_MINI;
        }
        // unsupported, and throws
        throw FaceException.valueOf(FaceStatusCode.SERVICE_UNSUPPORTED);
    }


    @Override
    public HandleFaceAuthorizationReturnResult handleFaceAuthorizationReturn(HandleFaceAuthorizationReturnInput request) {
        String provider = getProvider(request.getFaceAuthModeValue());
        //找faceId
        ProviderFaceInfo providerFaceDO = providerFaceRepository.getByProviderOrder(provider, request.getFaceAuthCode());
        FaceAuthValidationUtils.notNull(providerFaceDO, FaceException.valueOf(FaceStatusCode.ILLEGAL_ARGUMENT, "人脸识别记录不存在"));
        String faceId = providerFaceDO.getFaceId();

        //do 处理逻辑
        ProviderFaceAuthorizationDelayService service =
                ConfigurableProviderServices.getProviderService(provider);
        ProviderFaceAuthorizationResult result = service.handleFaceAuthorizationResponse(faceId, request);

        HandleFaceAuthorizationReturnResult response = new HandleFaceAuthorizationReturnResult().setCompleted(result.isCompleted()).setPassed(result.isSuccess());

        if (Objects.nonNull(result.getError())) {
            //1.返回天谷错误码
            ProviderException error = result.getError();
            response.setErrCode(String.valueOf(error.getCode()));
            response.setMsg(error.getMsg());
            //2.返回抖音交互相关字段
            if (Objects.nonNull(error.getPError())) {
                response.setExtendedMap(handleExtendedMap(error.getPError().errCode()));
            }
        }
        return response;

    }

    private Map<String, Object> handleExtendedMap(String peErrorCode) {
        // peErrorCode 供应商的错误码
        TiktokFaceResultStatusDomain domain = tiktokFaceService.getStatusByCode(Integer.parseInt(peErrorCode), null);
        if (Objects.isNull(domain)) {
            return null;
        }
        Map<String, Object> extendedMap = new HashMap<>();
        extendedMap.put(HandleFaceAuthorizationReturnResult.ExtendedMapConstant.tiktokReturnType, domain.getReturnType());
        extendedMap.put(HandleFaceAuthorizationReturnResult.ExtendedMapConstant.tiktokTipsStatus, domain.getTipsStatus());

        return extendedMap;
    }

    @Override
    public FaceResourceFaceAuthModeEnum mode() {
        return FaceResourceFaceAuthModeEnum.FACE_TIKTOK_MINI;
    }
}
