package com.timevale.faceauth.service.localphoto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.timevale.faceauth.service.core.utils.SpringUtils;
import com.timevale.faceauth.service.utils.CardUtils;
import com.timevale.framework.puppeteer.model.ConfigChange;
import com.timevale.framework.puppeteer.model.ConfigChangeEvent;
import com.timevale.framework.puppeteer.spring.annotation.PuppeteerConfigChangeListener;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 本地照片缓存定义可配置化的属性
 *
 * <AUTHOR>
 * @copyright 2024
 * @date 2024/6/27
 */
@Slf4j
@Component
public class UseLocalPhotoConfig {
  public static final String SPLIT  = ",";
  public static final String APPEND  = "_";
  private static final Map<String, PhotoOriginConfigBean> configBeanMap = new HashMap<>();
  private static final List<String> useOriginProviders  = new ArrayList<>();
  private static final Set<String> useOriginIgnoreAppIds  = new HashSet<>();

  //#是否读取老的缓存库
  @Getter
  @Value("${local.photo.origin.old.read}")
  private boolean originOldRead;

  //#是否写入老的缓存库
  @Getter
  @Value("${local.photo.origin.old.write}")
  private boolean originOldWrite;

  @Getter
  @Value("${local.cache.new}")
  private boolean localCacheNew;

  //#缓存最大有效时间（天）
  @Getter
  @Value("${local.photo.origin.expired.day}")
  private Integer originExpiredDay;

  //#缓存允许最小年龄
  @Getter
  @Value("${local.photo.origin.age.min}")
  private Integer originAgeMin;

  //#缓存允许最大年龄
  @Getter
  @Value("${local.photo.origin.age.max}")
  private Integer originAgeMax;

  // 不使用刷脸缓存客户appId
  @Value("${local.photo.origin.config.ignore.appId:}")
  private void initUseOriginIgnoreAppIds(String val){
    useOriginIgnoreAppIds.clear();
    if(StringUtils.isBlank(val)){
      return;
    }
    String[] array = val.split(SPLIT);
    for(String arr : array){
      useOriginIgnoreAppIds.add(arr);
    }
  }

  // 本地照片数据源供应商列表
  @Value("${local.photo.origin.config.useOriginProvider}")
  private void initUseOriginProviders(String val){
    useOriginProviders.clear();
    if(StringUtils.isBlank(val)){
      return;
    }
    String[] array = val.split(SPLIT);
    for(String arr : array){
      useOriginProviders.add(arr);
    }
  }

  @Value("${local.photo.origin.config}")
  protected void initConfig(String val){
    configBeanMap.clear();

    if (StringUtils.isBlank(val)) {
      return;
    }
    List<PhotoOriginConfigBean> jsonArray =
            JSON.parseObject(val, new TypeReference<List<PhotoOriginConfigBean>>() {});
    for (PhotoOriginConfigBean configDTO : jsonArray) {
      configBeanMap.put(configDTO.getProvider(), configDTO);
    }
  }

  public List<String> getUseOriginProviders() {
    return useOriginProviders;
  }

  public boolean containsIgnoreAppId(String appId) {
    if(com.timevale.mandarin.base.util.StringUtils.isBlank(appId)){
       return false;
    }
    return useOriginIgnoreAppIds.contains(appId);
  }

  public boolean isValidAge(String idNo){
    //用户年纪限制
    Pair<Boolean, Integer> agePair = CardUtils.parserPersonAge(idNo);
    if(agePair.getLeft()
            && (this.getOriginAgeMax() < agePair.getRight()
            || this.getOriginAgeMin() > agePair.getRight())){
      return false;
    }
    return true;
  }



  public static final PhotoOriginConfigBean getConfig(String provider) {
    if(StringUtils.isBlank(provider)){
       return null;
    }
    return configBeanMap.get(provider);
  }


  private Map<String, Field> properties;

  @PostConstruct
  protected void onInitialize() {
    Field[] fields = getClass().getDeclaredFields();
    properties = (new HashMap<>(2 * fields.length));
    String propertyKeyPatternKeyName = "key";
    Pattern propertyKeyPattern =
        Pattern.compile(
            "^(\\$\\{)?(?<" + propertyKeyPatternKeyName + ">[0-9a-zA-Z-_.]+)(:\\S*)?}?$");
    for (Field field : fields) {
      String key = deducePropertyKey(propertyKeyPatternKeyName, propertyKeyPattern, field);
      if (null == key) {
        continue;
      }
      ensureAccessible(field);
      properties.put(key, field);
    }
  }

  private String deducePropertyKey(String patternKeyName, Pattern keyPattern, Field field) {
    Value valueAnn = field.getAnnotation(Value.class);
    if (null == valueAnn) {
      return field.getName();
    }

    try {
      Matcher matcher = keyPattern.matcher(valueAnn.value());
      if (matcher.find()) {
        return matcher.group(patternKeyName);
      }
      return field.getName();
    } catch (Exception cause) {
      log.warn("Field deduce property key on '" + valueAnn.value() + "'", cause);
      return null;
    }
  }

  private void ensureAccessible(Field field) {
    if (!field.isAccessible()) {
      field.setAccessible(true);
    }
  }

  @PuppeteerConfigChangeListener("face_local_photo")
  void onPropertyChanged(ConfigChangeEvent changeEvent) {

    ConfigChange change = changeEvent.getChange("local.photo.origin.config");
    if (change != null) {
      initConfig(change.getNewValue());
    }

    change = changeEvent.getChange("local.photo.origin.config.ignore.appId");
    if (change != null) {
      initUseOriginIgnoreAppIds(change.getNewValue());
    }
    change = changeEvent.getChange("local.photo.origin.config.useOriginProvider");
    if (change != null) {
      initUseOriginProviders(change.getNewValue());
    }

    for (String key : changeEvent.changedKeys()) {
      Field property = properties.get(key);
      if (null == property) {
        continue;
      }
      PropertyChanged propertyChanged = PropertyChanged.valueOf(changeEvent.getChange(key));
      propertyChanged.changeProperty(this, property);
    }
  }

  private static volatile UseLocalPhotoConfig DEFAULT_PRO;

  public static UseLocalPhotoConfig getInstance() {
    if(DEFAULT_PRO == null){
      synchronized (UseLocalPhotoConfig.class){
        if (DEFAULT_PRO == null) {
          DEFAULT_PRO = SpringUtils.getBean(UseLocalPhotoConfig.class);
        }
      }
    }

    return DEFAULT_PRO;
  }

  private static final class PropertyChanged {

    private static final Function<String, Byte> CONVERTER_BYTE_FUNCTION = Byte::valueOf;
    private static final Function<String, Short> CONVERTER_SHORT_FUNCTION = Short::valueOf;
    private static final Function<String, Integer> CONVERTER_INTEGER_FUNCTION = Integer::valueOf;
    private static final Function<String, Long> CONVERTER_LONG_FUNCTION = Long::valueOf;
    private static final Function<String, Float> CONVERTER_FLOAT_FUNCTION = Float::valueOf;
    private static final Function<String, Double> CONVERTER_DOUBLE_FUNCTION = Double::valueOf;
    private static final Function<String, String> CONVERTER_STRING_FUNCTION = (s) -> s;
    private static final Function<String, Boolean> CONVERTER_BOOLEAN_FUNCTION = Boolean::valueOf;

    private static final Map<Class<?>, Function<String, ?>> CONVERTER_FUNCTIONS;

    static {
      CONVERTER_FUNCTIONS = (new HashMap<>(16));
      CONVERTER_FUNCTIONS.put(byte.class, CONVERTER_BYTE_FUNCTION);
      CONVERTER_FUNCTIONS.put(short.class, CONVERTER_SHORT_FUNCTION);
      CONVERTER_FUNCTIONS.put(int.class, CONVERTER_INTEGER_FUNCTION);
      CONVERTER_FUNCTIONS.put(long.class, CONVERTER_LONG_FUNCTION);
      CONVERTER_FUNCTIONS.put(float.class, CONVERTER_FLOAT_FUNCTION);
      CONVERTER_FUNCTIONS.put(double.class, CONVERTER_DOUBLE_FUNCTION);
      CONVERTER_FUNCTIONS.put(boolean.class, CONVERTER_BOOLEAN_FUNCTION);
      CONVERTER_FUNCTIONS.put(Byte.class, CONVERTER_BYTE_FUNCTION);
      CONVERTER_FUNCTIONS.put(Short.class, CONVERTER_SHORT_FUNCTION);
      CONVERTER_FUNCTIONS.put(Integer.class, CONVERTER_INTEGER_FUNCTION);
      CONVERTER_FUNCTIONS.put(Long.class, CONVERTER_LONG_FUNCTION);
      CONVERTER_FUNCTIONS.put(Float.class, CONVERTER_FLOAT_FUNCTION);
      CONVERTER_FUNCTIONS.put(Double.class, CONVERTER_DOUBLE_FUNCTION);
      CONVERTER_FUNCTIONS.put(String.class, CONVERTER_STRING_FUNCTION);
      CONVERTER_FUNCTIONS.put(Boolean.class, CONVERTER_BOOLEAN_FUNCTION);
    }

    private final ConfigChange change;

    private PropertyChanged(ConfigChange change) {
      this.change = change;
    }

    private void changeProperty(Object instance, Field field) {
      Function<String, ?> converter = CONVERTER_FUNCTIONS.get(field.getType());
      if (null == converter) {
        log.warn("Not found converter function on type '" + field.getType() + "' .");
        return;
      }
      Object value;
      try {
        value = converter.apply(change.getNewValue());
      } catch (Exception cause) {
        log.warn(
            "'" + change.getNewValue() + "' could not convert to type '" + field.getType() + "' .",
            cause);
        return;
      }

      try {
        field.set(instance, value);
      } catch (Exception cause) {
        log.warn(
            "Failed to set value on field '" + field.getName() + "' with value '" + value + "' .",
            cause);
      }
    }

    private static PropertyChanged valueOf(ConfigChange change) {
      return (new PropertyChanged(change));
    }
  }
}
