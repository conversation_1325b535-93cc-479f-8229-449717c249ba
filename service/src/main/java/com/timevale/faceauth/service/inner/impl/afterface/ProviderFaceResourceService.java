package com.timevale.faceauth.service.inner.impl.afterface;

import com.timevale.faceauth.service.enums.FaceResourceFaceAuthModeEnum;
import com.timevale.faceauth.service.input.HandleFaceAuthorizationReturnInput;
import com.timevale.faceauth.service.input.WakeupFaceInput;
import com.timevale.faceauth.service.result.HandleFaceAuthorizationReturnResult;
import com.timevale.faceauth.service.result.WakeupFaceResult;

/**
 * <AUTHOR>
 * @DATE 2024/5/13 16:46
 */
public interface ProviderFaceResourceService {


    WakeupFaceResult wakeupFace(WakeupFaceInput input);


    HandleFaceAuthorizationReturnResult handleFaceAuthorizationReturn(HandleFaceAuthorizationReturnInput request);

    FaceResourceFaceAuthModeEnum mode();
}
