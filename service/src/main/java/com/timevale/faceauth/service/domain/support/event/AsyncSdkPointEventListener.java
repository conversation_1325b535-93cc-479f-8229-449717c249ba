package com.timevale.faceauth.service.domain.support.event;

import com.alibaba.fastjson.JSON;
import com.timevale.faceauth.service.domain.event.sdk.AsyncSdkPointEvent;
import com.timevale.faceauth.service.inner.SdkManagerInnerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @DATE 2024/8/29 10:33
 */
@Slf4j
@Component
public class AsyncSdkPointEventListener {
    @Autowired
    private SdkManagerInnerService sdkManagerInnerService;

    @Async
    @EventListener
    public void doHandle(AsyncSdkPointEvent event) {
        log.info("AsyncSdkPointEventListener  doHandle  event = {} ", JSON.toJSONString(event));
        sdkManagerInnerService.asyncSdkPoint(event.getGid(), event.getSdkVersion(), event.getSdkCustomerId());
    }
}
