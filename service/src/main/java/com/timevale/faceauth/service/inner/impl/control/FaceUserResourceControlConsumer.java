package com.timevale.faceauth.service.inner.impl.control;

import com.timevale.faceauth.service.core.utils.SpringUtils;
import com.timevale.faceauth.service.inner.FaceUserResourceControlInnerService;
import com.timevale.faceauth.service.input.control.FaceUserResourceControlConsumerInput;
import com.timevale.faceauth.service.mq.AbstractMessageHandler;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @DATE 2025/7/7 14:27
 */
@Slf4j
public class FaceUserResourceControlConsumer extends AbstractMessageHandler {

    private final FaceUserResourceControlInnerService faceUserResourceControlInnerService;

    public FaceUserResourceControlConsumer(String topic) {
        super(topic);
        faceUserResourceControlInnerService = SpringUtils.getBean(FaceUserResourceControlInnerService.class);
    }


    @Override
    public void execute(String msg) {
        try {
            if (StringUtils.isBlank(msg)) {
                log.info("FaceUserResourceControlConsumer msg empty : {}", JsonUtils.obj2json(msg));
                return;
            }
            FaceUserResourceControlConsumerInput input = JsonUtils.json2pojo(msg, FaceUserResourceControlConsumerInput.class);
            log.info("FaceUserResourceControlConsumer, msg :{}", msg);
            faceUserResourceControlInnerService.repeatAgree(input);
        } catch (Exception e) {
            log.error("topic " + topic + " error, msg:{}, e:{}", msg, e);
        }
    }
}
