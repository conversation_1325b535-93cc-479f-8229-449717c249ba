package com.timevale.faceauth.service.domain.provider.bytedance.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.volcengine.service.visual.model.response.VisualBaseResponse;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/2/21
 */
@Data
public class FaceConfigInitResponse extends VisualBaseResponse {

    @JSONField(name = "data")
    private ConfigInitData data;

    @Data
    public static class ConfigInitData {

        /**
         * 配置ID，可在Token接口或H5端传入，以激活配置
         */
        @JSONField(name = "config_id")
        private String configId;

    }

}
