package com.timevale.faceauth.service.domain.provider.tencent.domain;

import com.timevale.faceauth.service.domain.provider.ConfigurableProviderService;
import com.timevale.faceauth.service.domain.provider.tencent.TencentWebAccessDefaultHolder;
import com.timevale.faceauth.service.domain.provider.tencent.TencentWebAccessHolder;
import com.timevale.faceauth.service.domain.provider.tencent.TencentWebAccessRealTimeHolder;
import com.timevale.faceauth.service.domain.provider.tencent.TencentWebAppIdVersion;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.Getter;

/**
 * 腾讯云刷脸webappid版本控制
 *
 * <AUTHOR>
 * @since 2021-09-08 19:02
 */@Deprecated
public enum TencentWebAppIdVersionEnum implements TencentWebAppIdVersion {


  RECORD_VIDEO(ConfigurableProviderService.PROVIDER_TENCENT_CLOUD,"1.0", "0","录制视频模式" , new TencentWebAccessDefaultHolder()),
  REAL_TIME_ANALYSIS(ConfigurableProviderService.PROVIDER_TENCENT_CLOUD,"2.0", "1","实时检测模式" , new TencentWebAccessRealTimeHolder()),
  SPECIAL(ConfigurableProviderService.PROVIDER_TENCENT_CLOUD,"10.0", "NONE","客户指定版本" , null),
  ;

  //接口版本号
  private String version;
  //运营支持平台code
  private String code;
  //应用场景
  private String scene;
  @Getter
  private String providerName;
  //webAccess 配置
  private TencentWebAccessHolder accessHolder;


  TencentWebAppIdVersionEnum(String providerName,String version, String code, String scene, TencentWebAccessHolder accessHolder) {
    this.providerName = providerName;
    this.version = version;
    this.code = code;
    this.scene = scene;
    this.accessHolder = accessHolder;
  }

  public String getVersion() {
    return version;
  }

  public String getScene() {
    return scene;
  }

  public String getCode() {
    return code;
  }

  public TencentWebAccessHolder getAccessHolder() {
    if(accessHolder == null){
       throw new RuntimeException("Tencent  config accessHolder is null");
    }
    return accessHolder;
  }

  public static TencentWebAppIdVersionEnum getByCode(String code) {
    if (StringUtils.isBlank(code)) {
      return RECORD_VIDEO;
    }
    for (TencentWebAppIdVersionEnum apiVersion : TencentWebAppIdVersionEnum.values()) {
      if (apiVersion.getCode().equals(code)) {
        return apiVersion;
      }
    }

    return RECORD_VIDEO;
  }

  public void setAccessHolder(TencentWebAccessHolder accessHolder) {
    this.accessHolder = accessHolder;
  }

  public static TencentWebAppIdVersionEnum getByVersion(String version) {
    if (StringUtils.isBlank(version)) {
      return RECORD_VIDEO;
    }
    for (TencentWebAppIdVersionEnum apiVersion : TencentWebAppIdVersionEnum.values()) {
      if (apiVersion.getVersion().equals(version)) {
        return apiVersion;
      }
    }

    return RECORD_VIDEO;
  }
}
