package com.timevale.faceauth.service.domain.provider.bytedance;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.provider.bytedance.domain.FaceQueryRequest;
import com.timevale.faceauth.service.domain.resource.FaceResourceType;
import com.timevale.faceauth.service.domain.resource.ProviderQueryApiRequestConverter;
import com.timevale.faceauth.service.domain.resource.ProviderQueryContext;
import com.timevale.mandarin.base.util.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * 供应商查询api 请求参数加工
 * <AUTHOR>
 * @since 2025/12/23 14:35
 */
@Component
public class ByteDanceApiInvocationQueryRequestConverter implements ProviderQueryApiRequestConverter<FaceQueryRequest> {


    @Override
    public FaceQueryRequest work(FaceQueryRequest request, ProviderQueryContext context) {
        Set<FaceResourceType> resourceTypes =  context.getResourceTypes();
        if(CollectionUtils.isEmpty(resourceTypes)){
            throw new FaceException(FaceStatusCode.ERR_INNER, "query context is bad . resourceTypes is null ");
        }
        if(resourceTypes.size() == 1 && resourceTypes.contains(FaceResourceType.BASE)){
            request.setOmit_data(true);
            return request;
        }
        request.setOmit_data(false);
        request.setOmit_image_data(true);
        request.setOmit_video_data(true);
        if(resourceTypes.contains(FaceResourceType.PHOTO)){
            request.setOmit_image_data(false);
        }
        if(resourceTypes.contains(FaceResourceType.VIDEO)){
            request.setOmit_video_data(false);
        }
        return request;
    }
}
