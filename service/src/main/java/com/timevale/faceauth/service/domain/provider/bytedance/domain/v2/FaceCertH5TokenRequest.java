package com.timevale.faceauth.service.domain.provider.bytedance.domain.v2;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE 2025/4/1 17:20
 */
@Data
public class FaceCertH5TokenRequest extends ToString {

    /**
     * 必选，固定值为"cert_h5_token"
     */
    private String req_key = "cert_h5_token";

    /**
     * 必选，H5临时configId，生成方式请参照CertH5ConfigInit
     */
    private String h5_config_id;

    /**
     * 必选，通过STS接口获取的临时token
     */
    private String sts_token;

    /**
     * 在有源比对时必选，身份证姓名
     */
    private String idcard_name;

    /**
     * 在有源比对时必选，身份证号
     */
    private String idcard_no;

    /**
     * 在无源比对时必选，输入图片的base64数组，在无源比对时需要传入1张用户的基准图，有源比对无需传入
     */
    private String ref_image;
}
