package com.timevale.faceauth.service.tencentface.cache;

import com.timevale.framework.tedis.util.TedisUtil;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version $ Id: FaceSwitchCache.java, v0.1 2020年11月18日 15:03 WangYuWu $
 */
public class FaceSwitchCache {

    private static final int CACHE_EXPIRE = 24;
    private static final String PRE = "FaceSwitchCache";
    private static final String KEY_SEPARATOR = "_";

    public static String generateKey(String key1, String key2, String key3) {
        return PRE+key1 + KEY_SEPARATOR + key2 + KEY_SEPARATOR + key3;
    }

    public static void set2Redis(String key, Object value) {
        TedisUtil.set(key, value,CACHE_EXPIRE, TimeUnit.HOURS);
    }

    public static String getFromRedis(String key) {
        return TedisUtil.get(key);
    }

    public static void deleteFromRedis(String key) {
        TedisUtil.delete(key);
    }

}
