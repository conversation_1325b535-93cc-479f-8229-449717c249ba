package com.timevale.faceauth.service.inner;

import com.timevale.faceauth.dal.pfs.dataobject.FaceUserResourceControlRecordDO;
import com.timevale.faceauth.dal.pfs.dataobject.query.FaceUserResourceControlRecordQuery;
import com.timevale.faceauth.service.domain.controll.FaceUserResourceControlCheckDTO;
import com.timevale.faceauth.service.input.control.ControlRecordSaveInput;
import com.timevale.faceauth.service.input.control.FaceUserResourceControlConsumerInput;
import com.timevale.faceauth.service.input.control.SwitchStatusControlRecordInput;
import com.timevale.faceauth.service.result.control.ControlFaceDataPrivilegeResult;
import com.timevale.faceauth.service.result.control.ControlRecordPageResult;

/**
 * <AUTHOR>
 * @DATE 2025/7/2 14:48
 */

/**
 * FaceUserResourceControlInnerService接口定义了face用户资源控制内部服务的方法
 * 它提供了保存控制记录、查询单个记录、修改记录以及分页查询记录的功能
 */
public interface FaceUserResourceControlInnerService {

    /**
     * 批量保存控制记录
     * 此方法接收一个ControlRecordSaveInput对象作为输入，该对象包含了需要保存的控制记录的相关信息
     *
     * @param input 包含控制记录信息的ControlRecordSaveInput对象
     */
    void batchSaveControlRecord(ControlRecordSaveInput input);

    /**
     * 根据记录ID查询单个控制记录
     * 此方法接收一个字符串参数recordId，表示控制记录的唯一标识符，
     * 并返回一个FaceUserResourceControlRecordDO对象，该对象包含了查询到的控制记录的详细信息
     *
     * @param recordId 控制记录的唯一标识符
     * @return 查询到的FaceUserResourceControlRecordDO对象
     */
    FaceUserResourceControlRecordDO selectOneByRecordId(String recordId);

    /**
     * 修改控制记录
     * 此方法接收一个FaceUserResourceControlRecordDO对象作为输入，该对象包含了需要修改的控制记录的详细信息
     *
     * @param input 包含控制记录详细信息的FaceUserResourceControlRecordDO对象
     */
    void modifyControlRecord(FaceUserResourceControlRecordDO input);

    /**
     * 分页查询控制记录
     * 此方法接收一个FaceUserResourceControlRecordQuery对象作为输入，该对象包含了查询条件和分页信息，
     * 并返回一个ControlRecordPageResult对象，该对象包含了查询结果的分页信息和记录列表
     *
     * @param query 包含查询条件和分页信息的FaceUserResourceControlRecordQuery对象
     * @return 查询结果的ControlRecordPageResult对象
     */
    ControlRecordPageResult pageControlRecord(FaceUserResourceControlRecordQuery query);

    /**
     * 检查用户资源控制信息
     * 该方法用于验证用户对特定资源的控制权限
     *
     * @param input 包含用户和资源信息的输入对象，用于检查用户的资源控制权限
     * @return 返回一个包含用户资源控制结果的对象
     */
    ControlFaceDataPrivilegeResult checkUserResourceControl(FaceUserResourceControlCheckDTO input);

    /**
     * 切换控制记录的状态
     * 该方法用于更改给定控制记录的启用或禁用状态
     *
     * @param input 包含需要更改状态的控制记录信息的输入对象
     */
    void switchStatusControlRecord(SwitchStatusControlRecordInput input);

    /**
     * 重复同意操作
     * 该方法用于处理用户资源控制的重复同意操作，可能包括更新数据库或发送通知等
     *
     * @param input 包含用户资源控制消费者信息的输入对象，用于执行重复同意操作
     * @return
     */
    void repeatAgree(FaceUserResourceControlConsumerInput input);
}

