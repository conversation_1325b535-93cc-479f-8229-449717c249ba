package com.timevale.faceauth.service.domain.resource.store;

import com.timevale.faceauth.service.domain.provider.query.ProviderResponse;
import com.timevale.faceauth.service.domain.resource.FaceResourceType;
import com.timevale.faceauth.service.domain.resource.ProviderQueryContext;

/**
 * <AUTHOR>
 */
public interface ResourceStore {

    void done(ProviderQueryContext context, ProviderResponse providerResponse);
    FaceResourceType getResourceType();
}
