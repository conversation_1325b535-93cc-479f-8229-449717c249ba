//package com.timevale.faceauth.service.domain.provider.tiktok.face.domain;
//
//import com.timevale.faceauth.service.core.FaceStatusCode;
//import com.timevale.faceauth.service.exception.ProviderErrorEnum;
//import lombok.Getter;
//
///**
// * <AUTHOR>
// * @DATE 2024/5/20 10:51
// */
//@Getter
//public enum TiktokFaceResultStatus implements ProviderErrorEnum {
//    code_1(-1, false, "系统错误", FaceStatusCode.PROVIDER_FAILED_API),
//    SUCCESS(0, true, "成功", FaceStatusCode.OK),
//    code_1000(1000, false, "参数解析失败，请确定body参数的数据类型", FaceStatusCode.PROVIDER_INVALID_PARAMETER),
//    code_10001(10001, false, "aid参数异常", FaceStatusCode.PROVIDER_INVALID_PARAMETER),
//    // {"err_no":10002,"err_tips":"req.AppId wrong","ticket":""}
//    code_10002(10002, false, "app_id参数异常", FaceStatusCode.PROVIDER_INVALID_PARAMETER),
//    //{"err_no":10003,"err_tips":"req.OpenId wrong","ticket":""}
//    code_10003(10003, false, "open_id参数异常", FaceStatusCode.PROVIDER_INVALID_PARAMETER),
//    //{"err_no":10004,"err_tips":"req.IdentityCode wrong","ticket":""}
//    code_10004(10004, false, "identity_code参数异常", FaceStatusCode.IDENTITY_PHOTO_ID_NO_ERROR),
//    code_10005(10005, false, "identity_name参数异常", FaceStatusCode.IDENTITY_PHOTO_NAME_ERROR),
//    //{"err_no":10006,"err_tips":"req.AccessToken wrong","ticket":""}
//    code_10006(10006, false, "access_token验证失败", FaceStatusCode.PROVIDER_INVALID_PARAMETER),
//    code_10008(10008, false, "未开通实名功能", FaceStatusCode.FACE_UNSUPPORTED_INITIALIZE_TYPE),
//
//    // {"err_no":10009,"err_tips":"ticket not found","state":0,"flow_data":null,"encryption_info":{"key":"","iv":""}}
//    code_10009(10009, false, "ticket参数异常", FaceStatusCode.PROVIDER_ERROR_API_RESPONSE),
//    //{"err_no":10010,"err_tips":"face comparison was not successful","state":0,"flow_data":null,"encryption_info":{"key":"","iv":""}}
//    code_10010(10010, false, "ticket查询结果异常（人脸比对不通过）", FaceStatusCode.LIVENESS_FAIL_RECOGNITION),
//
//
//    //{"err_no":10012,"err_tips":"decrypt error","ticket":""}
//    code_10012(10012, false, "decrypt error", FaceStatusCode.PROVIDER_ERROR_API_RESPONSE),
//    code_10013(10013, false, "encrypt error", FaceStatusCode.PROVIDER_ERROR_API_RESPONSE),
//
//    code_20001(20001, false, "超频访问，同一appid同一身份证号用户每秒请求超过5次", FaceStatusCode.PROVIDER_RISK_LIMIT),
//    code_1002002(1002002, false, "暂不支持未成年人实名认证", FaceStatusCode.PROVIDER_INVALID_CERT_AGE),
//    code_1002003(1002003, false, "不支持未成年人使用此服务", FaceStatusCode.PROVIDER_INVALID_CERT_AGE);
//
//    private int code;
//    private boolean completed;
//    /**
//     * 不是真实返回的err_tips
//     */
//    private String desc;
//    private FaceStatusCode faceCode;
//
//    TiktokFaceResultStatus(int code, boolean completed, String desc, FaceStatusCode faceCode) {
//        this.code = code;
//        this.completed = completed;
//        this.desc = desc;
//        this.faceCode = faceCode;
//    }
//
//
//    public static TiktokFaceResultStatus getStatusByCode(int code) {
//        for (TiktokFaceResultStatus status : TiktokFaceResultStatus.values()) {
//            if (status.code == code) {
//                return status;
//            }
//        }
//        //兜底系统异常
//        return TiktokFaceResultStatus.code_1;
//    }
//
//    @Override
//    public String errCode() {
//        return null;
//    }
//
//    @Override
//    public String errMsg() {
//        return null;
//    }
//}
