package com.timevale.faceauth.service.apispace.service.com.timevale.apispace.domain.collect.store;

import com.timevale.faceauth.service.apispace.service.com.timevale.apispace.request.ApiResponseEnhanceMetric;
import com.timevale.faceauth.service.apispace.service.com.timevale.apispace.request.ProviderMetric;
import com.timevale.faceauth.service.domain.repository.FaceInfo;
import com.timevale.faceauth.service.domain.repository.FaceRepository;
import com.timevale.faceauth.service.mq.MQManager;
import com.timevale.faceauth.service.utils.UUIDUtil;
import com.timevale.mandarin.base.security.MD5Utils;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
@Slf4j
@Component
public class StandardPushStore implements CollectorStore<ApiResponseEnhanceMetric> {


  private static final String UUID_PREFIX  ="auto_";
  private static final String PROVIDER_BIZ_TYPE  = "FACE";
  private static final String PROVIDER_BIZ_TITLE  = "刷脸";

  @Autowired
  private MQManager mqManager;
  @Autowired
  private FaceRepository faceRepository;

  @Override
  public void store(ApiResponseEnhanceMetric data) {

    ProviderMetric metric = ProviderMetric.builder()
            .timestamp(System.currentTimeMillis())
            .userId(getUserMd5(data.getBizId()))
            .action(data.getAction())
            .bizId(data.getBizId())
            .bizType(PROVIDER_BIZ_TYPE)
            .bizTitle(PROVIDER_BIZ_TITLE)
            .providerKey(data.getProviderKey())
            .providerTitle(data.getProviderTitle())
            .markStatus(data.getMarkStatus())
            .costTime(data.getResponseTime())
            .providerCode(data.getProviderCode())
            .providerMsg(data.getProviderMsg())
            .tag(data.getTag())
            .build();
    mqManager.sendProviderCall(metric);
  }


  @Override
  public String name() {
    return "standard";
  }

  private String getUserMd5(String faceId){
    if(StringUtils.isBlank(faceId)){
      return autoUserId();
    }
    FaceInfo faceInfo = faceRepository.getFaceInfoByFaceId(faceId);
    if(faceInfo == null){
      return autoUserId();
    }
    return MD5Utils.md5(faceInfo.getName()+faceInfo.getIdNo());

  }

  private String autoUserId(){
    return UUID_PREFIX+UUIDUtil.generateUUID();
  }
}
