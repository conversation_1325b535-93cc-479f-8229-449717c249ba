package com.timevale.faceauth.service.domain.provider.tencent.domain;

import com.timevale.faceauth.service.domain.provider.tencent.TencentWebAccessHolder;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE 2024/8/26 10:29
 */
@Data
public class TencentWebAccessHolderHookDTO extends ToString implements TencentWebAccessHolder {
    private String appId;
    private String webAppId;
    private String webAppSecret;
    private boolean doRefresh;
    private String tencentCloudCertificationIdAscApi;
}
