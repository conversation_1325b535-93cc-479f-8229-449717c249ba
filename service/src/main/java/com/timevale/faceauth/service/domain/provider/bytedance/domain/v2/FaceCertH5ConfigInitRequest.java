package com.timevale.faceauth.service.domain.provider.bytedance.domain.v2;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.List;

/**
 * https://www.volcengine.com/docs/6973/1209467
 *
 * <AUTHOR>
 * @DATE 2025/4/1 15:42
 */
@Data
public class FaceCertH5ConfigInitRequest extends ToString {

    /**
     * 必选，固定值为"cert_h5_config_init"
     */
    private String req_key = "cert_h5_config_init";

    /**
     * 必选，h5页面相关配置
     */
    private H5Config h5_config;

    /**
     * 必选，活体认证相关配置
     */
    private LivenessConfig liveness_config;

    /**
     * 可选，自定义tos相关配置
     */
    private TOSConfig tos_config;

    /**
     * 可选，回调相关配置
     */
    private CallBackInfo callback_info;

    /**
     * 单位，秒。范围需在[60, 7200]之间；可不传，不传默认值为900秒
     */
    private Integer expire_duration;

    /**
     * H5页面相关配置
     */
    @Data
    public static class H5Config {
        /**
         * 选填，默认"0"，业务接入场景
         * 0 正常流程：OCR上传图片识别 + 输入身份证号和姓名 + 人脸认证。
         * 1 跳过OCR上传图片识别，直接进入输入身份证号和姓名 + 人脸认证。
         * 2 直接进入OCR上传图片识别 + 人脸认证，跳过输入身份证号和姓名。
         * 3  OCR上传图片识别、输入身份证号和姓名 这两个步骤都要跳过，直接进行人脸认证流程。
         * 【注意】适用于业务已存有用户资料的场景。需要业务侧调用服务端接口（参考CertH5Token ），获取bytedToken值后拼接到URL参数上。
         */
        private String type;

        /**
         * 选填，默认"rgba(56, 123, 255, 1)"，自定义主题色
         */
        private String theme_color;

        /**
         * 选填，默认"1"，是否展示认证首页
         */
        private String show_guide;

        /**
         * 选填，默认"1"，是否展示认证结果页
         */
        private String show_result;

        /**
         * 选填，自定义协议名称
         */
        private String protocol_name;

        /**
         * 选填，自定义协议链接
         */
        private String protocol_link;

        /**
         * 优先使用更高效便捷的实时刷脸方案，当设备不支持该方案时的降级处理策略。
         * 选填，默认1
         * 0 当设备不支持实时刷脸能力时，则认定本次认证失败
         * 1 当设备不支持实时刷脸能力时，启用备用认证方案 - 视频录制，该方案能更好的兼容低端设备。
         */
        private String enable_record;

        /**
         * 必填，认证完成后跳转的URL 证完成（成功or失败）后，会将部分参数拼接到redirectUrl上并执行跳转。
         */
        private String redirect_url;

        /**
         * 选填，默认false，是否展示默认协议链接
         */
        private Boolean ignore_homepage_agreement;

        /**
         * 选填，默认false，是否展示h5认证页面底部文案
         */
        private Boolean ignore_bottom_text;

        // Getter and Setter
    }

    /**
     * 活体认证相关配置
     */
    @Data
    public static class LivenessConfig {
        /**
         * 必填，有源or无源认证
         * 0 无源（根据一张基准图进行认证）
         * 1 有源（根据身份证号和姓名进行认证
         */
        private String ref_source;

        /**
         * 必填，端上活体类型
         */
        private String liveness_type;

        /**
         * 选填，默认10，端上活体超时时间
         */
        private Integer liveness_timeout;

        /**
         * 选填，可被下发的动作列表
         */
        private List<String> motion_list;

        /**
         * 选填，固定一定需要下发的动作列表
         */
        private List<String> fixed_motion_list;

        /**
         * 选填，默认2，选中的动作个数 可选范围：[1, 4]
         * (要≤motion_list数量)
         */
        private Integer motion_count;

        /**
         * 选填，默认10，端上活体最大尝试次数 可选范围：[1, 100]
         */
        private Integer max_liveness_trial;

        // Getter and Setter
    }

    /**
     * 自定义TOS相关配置
     */
    @Data
    public static class TOSConfig {
        /**
         * 选填，TOS使用的STS AK
         */
        private String sts_ak;

        /**
         * 选填，TOS使用的STS SK
         */
        private String sts_sk;

        /**
         * 选填，TOS使用的STS Token
         */
        private String sts_token;

        /**
         * 选填，TOS使用的Bucket
         */
        private String bucket;

        /**
         * 选填，TOS使用的Endpoint
         */
        private String endpoint;

        /**
         * 选填，TOS使用的Region
         */
        private String region;

        // Getter and Setter
    }

    /**
     * 回调相关配置
     */
    @Data
    public static class CallBackInfo {
        /**
         * 必选，默认false，如需主动推送结果的回调，请填入true
         */
        private Boolean switch_flag;

        /**
         * 必选，默认false，如需阻塞式回调，请填入true
         */
        private Boolean block;

        /**
         * 必选，默认为空串，且仅当非空串时才尝试回调
         */
        private String url;

        /**
         * 必选，回调接收客户的唯一代号
         */
        private String client_name;

        // Getter and Setter
    }

    // Getter and Setter
}
