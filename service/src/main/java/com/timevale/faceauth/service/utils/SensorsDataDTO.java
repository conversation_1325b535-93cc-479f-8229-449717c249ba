package com.timevale.faceauth.service.utils;

import com.timevale.component.identity.record.constants.OptProvider;
import com.timevale.component.identity.sensors.SensorsEvent;
import com.timevale.faceauth.service.domain.provider.bytedance.domain.v2.BytedanceDowngradeCodeDTO;
import com.timevale.faceauth.service.domain.repository.FaceInfo;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @DATE 2025/4/28 17:42
 */
@Data
@Accessors(chain = true)
public class SensorsDataDTO extends ToString {
    private OptProvider provider;
    private FaceInfo faceInfo;
    private BytedanceDowngradeCodeDTO downgradeCodeDTO;

    private String face_processtype;

    private SensorsEvent sensorsEvent;
}
