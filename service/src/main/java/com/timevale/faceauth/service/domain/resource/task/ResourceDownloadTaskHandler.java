package com.timevale.faceauth.service.domain.resource.task;

import com.timevale.faceauth.service.core.utils.SpringUtils;
import com.timevale.faceauth.service.domain.resource.ProviderRemoteResourceLoader;
import com.timevale.faceauth.service.domain.resource.ProviderResourceBaseContext;
import com.timevale.faceauth.service.domain.resource.ProviderResourceEngine;
import com.timevale.faceauth.service.mq.AbstractMessageHandler;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 发起刷脸MQ消息处理
 */
@Slf4j
public class ResourceDownloadTaskHandler extends AbstractMessageHandler {

  private ProviderResourceEngine resourceEngine;
  public ResourceDownloadTaskHandler(String topic) {
    super(topic);
    resourceEngine = SpringUtils.getBean(ProviderResourceEngine.class);
  }

  @Override
  public void execute(String msg) {
    try {
      if (StringUtils.isBlank(msg)) {
        log.info("ResourceDownloadTaskHandler msg empty : {}", JsonUtils.obj2json(msg));
        return;
      }
      ResourceDownloadMsg downloadMsg =
              JsonUtils.json2pojo(msg, ResourceDownloadMsg.class);
      log.info("ResourceDownloadTaskHandler, msg :{}", msg);
      ProviderRemoteResourceLoader resourceLoader  = resourceEngine.match(downloadMsg);
      if(resourceLoader == null){
        log.error("ResourceDownloadTaskHandler not match ResourceLoader");
        return;
      }
      ProviderResourceBaseContext baseContext = new ProviderResourceBaseContext();
      baseContext.setFaceId(downloadMsg.getFaceId());
      resourceLoader.execute(baseContext);
    } catch (Exception e) {
        log.error("topic "+topic+" error, msg:{}, e:{}", msg, e);
    }
  }

}
