package com.timevale.faceauth.service.inner.impl.afterface.entity;

import com.timevale.mandarin.base.util.MapUtils;
import com.timevale.mandarin.common.result.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @DATE 2024/8/13 15:26
 */
@Slf4j
public abstract class AfterFaceContext extends ToString {

    public Map<String, String> toMap() {
        try {
            return BeanUtils.describe(this);
        } catch (Exception e) {
            log.error("BeanUtils.describe error", e);
            return MapUtils.EMPTY_MAP;
        }
    }
}
