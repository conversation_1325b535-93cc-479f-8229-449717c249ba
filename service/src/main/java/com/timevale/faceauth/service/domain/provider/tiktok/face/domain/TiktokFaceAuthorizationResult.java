package com.timevale.faceauth.service.domain.provider.tiktok.face.domain;

import com.timevale.faceauth.service.domain.provider.ConfigurableProviderService;
import com.timevale.faceauth.service.domain.provider.support.AbstractProviderFaceAuthorizationResult;

/**
 * <AUTHOR>
 * @DATE 2024/5/15 15:02
 */
public class TiktokFaceAuthorizationResult extends AbstractProviderFaceAuthorizationResult {
    private TiktokFaceAuthorizationResult(TiktokFaceAuthorizationResultBuilder builder) {
        super(builder);
    }

    public static TiktokFaceAuthorizationResultBuilder createBuilder() {
        return (new TiktokFaceAuthorizationResultBuilder());
    }

    public static TiktokFaceAuthorizationResultBuilder createBuilder(String provider) {
        return (new TiktokFaceAuthorizationResultBuilder(provider));
    }

    public static class TiktokFaceAuthorizationResultBuilder
            extends ProviderFaceAuthorizationResultBuilder<
            TiktokFaceAuthorizationResult, TiktokFaceAuthorizationResultBuilder> {

        public TiktokFaceAuthorizationResultBuilder() {
            super(ConfigurableProviderService.PROVIDER_FACE_TIKTOK_MINI);
        }

        public TiktokFaceAuthorizationResultBuilder(String provider) {
            super(provider);
        }

        @Override
        public TiktokFaceAuthorizationResult build() {
            if (isSuccess()) {
                this.refreshContext();
            }
            return (new TiktokFaceAuthorizationResult(this));
        }
    }
}
