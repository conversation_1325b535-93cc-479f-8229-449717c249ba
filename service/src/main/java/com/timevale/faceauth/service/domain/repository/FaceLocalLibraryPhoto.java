package com.timevale.faceauth.service.domain.repository;

import com.timevale.faceauth.dal.pfs.face.support.FaceLocalLibraryPhotoDO;
import com.timevale.faceauth.dal.pfs.face.support.FaceLocalLibraryPhotoV2DO;
import com.timevale.faceauth.service.core.support.ArgumentUtil;
import com.timevale.faceauth.service.domain.UserFactor2Token;
import com.timevale.faceauth.service.enums.FaceAuthPhotoTypeEnum;
import lombok.Getter;
import org.springframework.util.StringUtils;

/**
 * 用户照片
 *
 * <AUTHOR>
 * @copyright 2024
 * @date 2024/07/11 17
 */
@Getter
public class FaceLocalLibraryPhoto {

    private final String name;
    private final String idNo;
    private final String provider;
    private final String sourceId;
    private final String sourceType;

    private final String hashFactor2;
    private final String photo;
    private final String photoType;

    private FaceLocalLibraryPhoto(FaceLocalLibraryPhotoBuilder builder) {
        this.idNo = builder.idNo;
        this.name = builder.name;
        this.provider = builder.provider;
        this.hashFactor2 = builder.hashFactor2;
        this.photo = builder.photo;
        this.photoType = builder.photoType;
        this.sourceId = builder.sourceId;
        this.sourceType = builder.sourceType;
    }

    public static FaceLocalLibraryPhotoBuilder createBuilder() {
        return (new FaceLocalLibraryPhotoBuilder());
    }

    static UserPhoto valueOf(FaceLocalLibraryPhotoDO entity) {
        if(entity == null){
            return null;
        }
        return UserPhoto.createBuilder()
                .setCreateTime(entity.createTime)
                .setName(entity.name)
                .setHashFactor2(entity.hashFactor2)
                .setPhoto(entity.photo)
                .setPhotoType(entity.photoType)
                .build();
    }

    FaceLocalLibraryPhotoDO toEntity() {
        FaceLocalLibraryPhotoDO entity = (new FaceLocalLibraryPhotoDO());
        entity.provider = getProvider();
        entity.name = getName();
        entity.hashFactor2 = getHashFactor2();
        entity.photo = getPhoto();
        entity.photoType = getPhotoType();
        entity.sourceId = getSourceId();
        entity.sourceType = getSourceType();
        return entity;
    }

    FaceLocalLibraryPhotoV2DO toEntityV2() {
        FaceLocalLibraryPhotoV2DO entity = (new FaceLocalLibraryPhotoV2DO());
        entity.provider = getProvider();
        entity.name = getName();
        entity.hashFactor2 = getHashFactor2();
        entity.photo = getPhoto();
        entity.photoType = getPhotoType();
        entity.sourceId = getSourceId();
        entity.sourceType = getSourceType();
        return entity;
    }

    public static class FaceLocalLibraryPhotoBuilder {

        private String name;
        private String idNo;
        private String provider;
        private String sourceId;
        private String sourceType;

        private String hashFactor2;
        private String photo;
        private String photoType;

        public FaceLocalLibraryPhoto build() {
            ensureHashingFactor2();
            return (new FaceLocalLibraryPhoto(this));
        }

        private void ensureHashingFactor2() {
            if (StringUtils.isEmpty(hashFactor2)) {
                hashFactor2 = UserFactor2Token.valueOf(idNo, name).getHashingValue();
            }
        }

        public FaceLocalLibraryPhotoBuilder setIdNo(String idNo) {
            ArgumentUtil.throwIfEmptyArgument(idNo, "idNo");
            this.idNo = idNo;
            return this;
        }

        public FaceLocalLibraryPhotoBuilder setName(String name) {
            ArgumentUtil.throwIfEmptyArgument(name, "name");
            this.name = name;
            return this;
        }

        public FaceLocalLibraryPhotoBuilder setProvider(String provider) {
            ArgumentUtil.throwIfEmptyArgument(provider, "provider");
            this.provider = provider;
            return this;
        }

        public FaceLocalLibraryPhotoBuilder setSourceId(String sourceId) {
            this.sourceId = sourceId;
            return this;
        }

        public FaceLocalLibraryPhotoBuilder setSourceType(String sourceType) {
            this.sourceType = sourceType;
            return this;
        }

        private FaceLocalLibraryPhotoBuilder setHashFactor2(String hashFactor2) {
            this.hashFactor2 = hashFactor2;
            return this;
        }

        public FaceLocalLibraryPhotoBuilder setPhoto(String photo) {
            ArgumentUtil.throwIfEmptyArgument(photo, "photo");
            this.photo = photo;
            return this;
        }

        public FaceLocalLibraryPhotoBuilder setPhotoType(boolean isOriginPhoto) {

            this.photoType = isOriginPhoto
                    ? FaceAuthPhotoTypeEnum.HIGH_DEFINITION.name():
                    FaceAuthPhotoTypeEnum.WATER_WAVE.name() ;
            return this;
        }
    }

    public static void main(String[] args) {
        System.out.println(UserFactor2Token.valueOf("34122319840317041X", "常苏州").getHashingValue());
    }
}
