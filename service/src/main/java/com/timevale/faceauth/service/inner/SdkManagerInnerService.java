package com.timevale.faceauth.service.inner;

import com.timevale.faceauth.service.domain.sdk.SdkChannelDomain;
import com.timevale.faceauth.service.input.sdk.BatchSaveCustomerChannelInput;
import com.timevale.faceauth.service.input.sdk.GetManualConfigInput;
import com.timevale.faceauth.service.input.sdk.GetSdkCustomerDetailInput;
import com.timevale.faceauth.service.input.sdk.QuerySdkCustomerListInput;
import com.timevale.faceauth.service.input.sdk.QuerySdkVersionListInput;
import com.timevale.faceauth.service.input.sdk.SaveSdkVersionInput;
import com.timevale.faceauth.service.input.sdk.UpdateBundleIdIdInput;
import com.timevale.faceauth.service.input.sdk.UpdateSdkVersionStatusInput;
import com.timevale.faceauth.service.result.sdk.GetManualConfigResult;
import com.timevale.faceauth.service.result.sdk.GetSdkCustomerDetailResult;
import com.timevale.faceauth.service.result.sdk.QuerySdkCustomerListResult;
import com.timevale.faceauth.service.result.sdk.QuerySdkVersionListResult;
import com.timevale.faceauth.service.result.sdk.VerifyAndQueryChannelSdkResult;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2024/8/6 15:45
 */
public interface SdkManagerInnerService {
    /**
     * 查询全量的SDK列表
     *
     * @return
     */
    QuerySdkVersionListResult querySdkVersionList(QuerySdkVersionListInput input);


    /**
     * 新增版本渠道
     *
     * @param input
     * @return
     */
    String saveSdkVersion(SaveSdkVersionInput input);

    /**
     * 查询全量的SDK渠道列表
     *
     * @return
     */
    List<SdkChannelDomain> queryAllSdkChannel();

    /**
     * 更新SDK的状态 ，敏感接口  停用后直接导致SDK不可用
     *
     * @param input
     */
    void updateSdkVersionStatus(UpdateSdkVersionStatusInput input);

    /**
     * @param input
     * @return
     */
    QuerySdkCustomerListResult querySdkCustomerList(QuerySdkCustomerListInput input);

    /**
     * 批量保存
     *
     * @param input
     * @return
     */
    String batchSaveCustomerChannel(BatchSaveCustomerChannelInput input);

    /**
     * 查询客户详情
     *
     * @param input
     * @return
     */
    GetSdkCustomerDetailResult getSdkCustomerDetail(GetSdkCustomerDetailInput input);

    /**
     * @param input
     * @return
     */
    GetManualConfigResult getManualConfig(GetManualConfigInput input);

    /**
     * @param orgGid
     * @param sdkVersion
     * @param bundleId
     * @return
     */
    Pair<VerifyAndQueryChannelSdkResult, String> verifyAndQueryChannelList(String orgGid, String sdkVersion, String bundleId);

    /**
     * 异步埋点
     *
     * @param orgGid
     * @param sdkVersion
     * @param sdkCustomerId
     */
    void asyncSdkPoint(String orgGid, String sdkVersion, String sdkCustomerId);

    /**
     * @param input
     */
    void updateBundleIdId(UpdateBundleIdIdInput input);
}
