package com.timevale.faceauth.service.domain.resource;

/**
 * @see com.timevale.faceauth.service.domain.FaceResource
 * <AUTHOR>
 * @since 2024/12/23 16:37
 */
public enum FaceResourceType {

    BASE("BASE","基本信息"),
    PHOTO("PHOTO","照片"),
    <PERSON><PERSON><PERSON>("VIDEO","视频"),
    IDCARD_FRONT("IDCARD_FRONT","身份证正面"),
    IDCARD_BACK("IDCARD_BACK","身份证反正面"),

    ;

    private String code;
    private String desc;

    FaceResourceType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public String getCode() {
        return code;
    }



    public static FaceResourceType getByCode(String code) {
        for (FaceResourceType faceResourceType : FaceResourceType.values()) {
            if (faceResourceType.getCode().equals(code)) {
                return faceResourceType;
            }
        }
        return BASE;
    }

}
