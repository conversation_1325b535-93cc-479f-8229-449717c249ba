package com.timevale.faceauth.service.domain.provider.bytedance.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @since 2023/2/21
 */
@Data
public class FaceConfigInitRequest {

    /**
     * 此处请填写cert_config_init
     */
    @JSONField(name = "req_key")
    private String reqKey = "cert_config_init";

    /**
     * 配置套餐名称
     */
    @JSONField(name = "config_name")
    private String configName;

    /**
     * 服务端Token接口参数配置，详细请参考服务端Token接口中的Body请求参数部分
     */
    @JSONField(name = "token_api_config")
    private TokenApiConfig tokenApiConfig;

    /**
     * H5参数配置，详细请参考H5接入文档中的接入参数部分
     */
    @JSONField(name = "h5_config")
    private H5Config h5Config;

    @Data
    public static class TokenApiConfig {

        /**
         * 可被下发的动作列表，仅在动作活体和炫彩活体生效。可选动作：
         * 0：眨眼
         * 1：张嘴
         * 2：点头
         * 3：摇头
         */
        @JSONField(name = "motion_list")
        private ArrayList<String> motionList = new ArrayList<>();

        /**
         * 比对类型	可选类型：
         * 0：无源比对
         * 1：有源比对
         */
        @JSONField(name = "ref_source")
        private String refSource;


        /**
         * 选中的动作个数，仅在动作活体和炫彩活体生效。
         */
        @JSONField(name = "motion_count")
        private String motionCount;

        /**
         * 可被下发的动作列表，仅在动作活体和炫彩活体生效。可选动作：
         * 0：眨眼
         * 1：张嘴
         * 2：点头
         * 3：摇头
         */
        @JSONField(name = "fixed_motion_list")
        private ArrayList<String> fixedMotionList = new ArrayList<>();

    }

    @Data
    public static class H5Config {

        /**
         * 认证完成（成功/失败/中途失败）后返回结果的callback回调页面的URL地址，回调参数结构见下redirectUrl参数对象
         *
         * 注意需要使用encodeURIComponent进行转义
         */
        @JSONField(name = "redirectUrl")
        private String redirectUrl;

        /**
         * 0-无源，type选值为2时必填; 1-有源，type选值为2时必填
         */
        @JSONField(name = "source")
        private String source;

        /**
         * 2
         * 跳过输入身份证号和姓名，直接进行活体认证流程。
         * 需要填写bytedToken、source
         * 【注意：】需要业务侧提前自行调用服务端接口，详见服务端Token接口，上传身份证号和姓名。
         */
        @JSONField(name = "type")
        private String type;

        /**
         * 可选，默认1。0-不展示完成结果页，直接返回redirectUrl。1-默认取值，展示完成结果页
         */
        @JSONField(name = "showResult")
        private String showResult;

        /**
         * 可选活体检测：0：服务端动作活体,1：服务端静默活体。默认0
         */
        @JSONField(name = "demoteType")
        private String demoteType;

    }

}
