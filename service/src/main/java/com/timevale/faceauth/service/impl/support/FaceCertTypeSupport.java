package com.timevale.faceauth.service.impl.support;

import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.enums.FaceAuthModeEnum;
import com.timevale.faceauth.service.enums.FaceCertTypeEnum;

/**
 * <AUTHOR>
 * @since 2024/10/28 14:37
 */
public class FaceCertTypeSupport {

    public static boolean checkModeNotSupport(FaceCertTypeEnum certTypeEnum , 
                                              FaceAuthModeEnum faceAuthMode){
        if(certTypeEnum == null || faceAuthMode == null){
             return false;
        }
        return !ConfigurableProperties.getInstance().ifSupportFaceCertType(certTypeEnum.getOpenCertTypeEnum().name(), faceAuthMode.name());
    }

}
