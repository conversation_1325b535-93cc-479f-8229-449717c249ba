package com.timevale.faceauth.service.domain.provider.tencent;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.resource.FaceResourceType;
import com.timevale.faceauth.service.domain.resource.ProviderQueryApiRequestConverter;
import com.timevale.faceauth.service.domain.resource.ProviderQueryContext;
import com.timevale.mandarin.base.util.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * 供应商查询api 请求参数加工
 * <AUTHOR>
 * @since 2025/12/23 14:35
 */
@Component
public class TencentCloudApiInvocationQueryRequestConverter implements ProviderQueryApiRequestConverter<TencentCloudCertificationQueryRequest> {


    @Override
    public TencentCloudCertificationQueryRequest work(TencentCloudCertificationQueryRequest request, ProviderQueryContext context) {
        Set<FaceResourceType> resourceTypes =  context.getResourceTypes();
        if(CollectionUtils.isEmpty(resourceTypes)){
            throw new FaceException(FaceStatusCode.ERR_INNER, "query context is bad . resourceTypes is null ");
        }
        if(resourceTypes.size() == 1 && resourceTypes.contains(FaceResourceType.BASE)){
            request.setGetFile(String.valueOf(TencentCloudCertificationQueryFileResultType.GET_NONE.getValue()));
            return request;
        }

        if(resourceTypes.contains(FaceResourceType.PHOTO) && resourceTypes.contains(FaceResourceType.VIDEO)){
            request.setGetFile(String.valueOf(TencentCloudCertificationQueryFileResultType.GET_VIDEO_AND_PHOTO.getValue()));
            return request;
        }
        if(resourceTypes.contains(FaceResourceType.PHOTO)){
            request.setGetFile(String.valueOf(TencentCloudCertificationQueryFileResultType.GET_PHOTO_ONLY.getValue()));
        }
        if(resourceTypes.contains(FaceResourceType.VIDEO)){
            request.setGetFile(String.valueOf(TencentCloudCertificationQueryFileResultType.GET_VIDEO_ONLY.getValue()));
            return request;
        }
        return request;
    }
}
