package com.timevale.faceauth.service.domain.provider.tencentsdks;

import com.alibaba.fastjson.JSON;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStartResult;
import com.timevale.faceauth.service.core.support.FaceQueryableExtend;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.domain.FaceAuthorizationPhotoResolver;
import com.timevale.faceauth.service.domain.processor.FaceRequestContext;
import com.timevale.faceauth.service.domain.provider.ConfigurableProviderApplyResultResolver;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationData;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationResult;
import com.timevale.faceauth.service.domain.provider.support.AbstractProviderFaceAuthorizationResult;
import com.timevale.faceauth.service.domain.provider.support.FaceAuthorizationInitializingContext;
import com.timevale.faceauth.service.domain.provider.tencent.TencentCloudCertificationInitializationUploadResultWrap;
import com.timevale.faceauth.service.domain.provider.tencent.TencentCloudCertificationQueryInvocationHandler;
import com.timevale.faceauth.service.domain.provider.tencent.TencentCloudFaceAuthorizationResult;
import com.timevale.faceauth.service.domain.provider.tencent.TencentCloudFaceResultStatus;
import com.timevale.faceauth.service.domain.provider.tencent.TencentCloudSdkCertificationInitializationUploadInvocationHandler;
import com.timevale.faceauth.service.domain.provider.tencent.TencentCloudService;
import com.timevale.faceauth.service.domain.provider.tencent.TencentWebAppIdVersion;
import com.timevale.faceauth.service.domain.provider.tencent.domain.TencentWebAppIdVersionHookDTO;
import com.timevale.faceauth.service.domain.provider.tencent.domain.TencentWebAppIdVersionUtil;
import com.timevale.faceauth.service.domain.provider.tencentsdks.entity.dto.SdkOriginalReturnDataDTO;
import com.timevale.faceauth.service.domain.provider.tencentsdks.entity.request.TencentSdkProviderFaceInput;
import com.timevale.faceauth.service.domain.provider.tencentsdks.entity.request.TencentSdkRequestContext;
import com.timevale.faceauth.service.domain.repository.FaceInfo;
import com.timevale.faceauth.service.domain.repository.FaceRepository;
import com.timevale.faceauth.service.domain.repository.FaceSwitchRepository;
import com.timevale.faceauth.service.domain.repository.ProviderFaceInfo;
import com.timevale.faceauth.service.domain.repository.ProviderFaceRepository;
import com.timevale.faceauth.service.domain.repository.ProviderReturnInfo;
import com.timevale.faceauth.service.domain.repository.UserPhoto;
import com.timevale.faceauth.service.domain.support.DnsResolver;
import com.timevale.faceauth.service.enums.FaceAuthModeEnum;
import com.timevale.faceauth.service.exception.ProviderException;
import com.timevale.faceauth.service.impl.support.FaceAuthModeAdapter;
import com.timevale.faceauth.service.input.HandleFaceAuthorizationReturnInput;
import com.timevale.faceauth.service.input.WakeupFaceInput;
import com.timevale.faceauth.service.result.FaceAuthResult;
import com.timevale.faceauth.service.result.WakeupFaceResult;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @DATE 2024/8/12 17:20
 */
@Service
@Slf4j
public class TencentSdkBasicService extends TencentCloudService implements ConfigurableProviderApplyResultResolver {

    @Autowired
    protected FaceRepository faceRepository;
    @Autowired
    protected ProviderFaceRepository providerFaceRepository;

    @Autowired
    private ConfigurableProperties properties;

    @Autowired
    private TencentCloudSdkCertificationInitializationUploadInvocationHandler uploadInvocationHandler;


    @Autowired
    private TencentCloudCertificationQueryInvocationHandler queryInvocationHandler;
    @Autowired
    private FaceAuthorizationPhotoResolver faceAuthorizationPhotoResolver;

    @Override
    public FaceAuthResult resolveApplyResult(String clientType, FaceStartResult result) {
        // 2024/8/29  mangcao     自定义SDK刷脸返回 不需要返回url
        return resolveApplyResultOnlyFaceValue(result);
    }

    @Autowired
    public TencentSdkBasicService(FaceRepository faceRepository,
                                  ProviderFaceRepository providerFaceRepository,
                                  DnsResolver dnsResolver,
                                  ConfigurableProperties configurableProperties,
                                  FaceSwitchRepository faceSwitchRepository) {
        super(faceRepository, providerFaceRepository, dnsResolver, configurableProperties, faceSwitchRepository);
    }


    @Override
    public String getProviderName() {
        return PROVIDER_FACE_TENCENT_SDK_BASIC;
    }

    @Override
    public String getFullName() {
        return FULL_NAME_PROVIDER_FACE_TENCENT_SDK_BASIC;
    }

    @Override
    protected AbstractProviderFaceAuthorizationResult resolveDoneAuthorizationResult(ProviderReturnInfo providerReturn, FaceQueryableExtend extend, FaceInfo faceInfo, ProviderFaceInfo providerFaceInfo) throws FaceException {
        log.info("[腾讯云SDK] 解析授权结果 resolveDoneAuthorizationResult faceId = {}", faceInfo.getFaceId());
        return super.resolveDoneAuthorizationResult(providerReturn, extend, faceInfo, providerFaceInfo);
    }

    @Override
    protected ProviderFaceAuthorizationResult doQueryAuthorizeResult(FaceQueryableExtend extend, FaceInfo faceInfo, ProviderFaceInfo providerFaceInfo) throws FaceException {
        log.info("[腾讯云SDK] 查询结果 doQueryAuthorizeResult faceId = {}", faceInfo.getFaceId());
        return super.doQueryAuthorizeResult(extend, faceInfo, providerFaceInfo);
    }

    /**
     * 推断供应商回跳的数据结果
     *
     * @param faceId
     * @param request
     */
    @Override
    protected ProviderFaceAuthorizationResult detectFaceAuthorizationResultOnReturn(String faceId, HttpServletRequest request) throws FaceException {
        log.info("[腾讯云SDK] 推断结果 detectFaceAuthorizationResultOnReturn faceId = {}", faceId);
        return super.detectFaceAuthorizationResultOnReturn(faceId, request);
    }

    /**
     * 推断供应商回调的数据结果
     *
     * @param faceId
     * @param dataBuffer
     * @param request
     */
    @Override
    protected ProviderFaceAuthorizationResult detectFaceAuthorizationResultOnCallback(String faceId, byte[] dataBuffer, HttpServletRequest request) throws FaceException {
        log.info("[腾讯云SDK] 推断结果 detectFaceAuthorizationResultOnCallback faceId = {}", faceId);
        return super.detectFaceAuthorizationResultOnCallback(faceId, dataBuffer, request);
    }


    @Override
    protected ProviderFaceAuthorizationData doInitialize(FaceAuthorizationInitializingContext initializingContext) throws FaceException {
        log.info("[腾讯云SDK] #doInitialize  initializingContext ={} ", JSON.toJSONString(initializingContext));
        //第一步生成 orderNo
        String faceId = initializingContext.getFaceId();
        return ProviderFaceAuthorizationData.createBuilder()
                .setThirdpartId(null)
                .setProviderName(getFullName())
                .setProvider(getProviderName())
                .setOrderNo(faceId)
                //定义抖音自己的刷脸失效时间
                .setExpireMinutes(properties.getTencentSdkTaskExpire())
                .setData(faceId)
                .setAuthorizeInput(resolveRequestContent(initializingContext))
                .build();
    }


    private String resolveRequestContent(FaceAuthorizationInitializingContext authInput) {

        try {
            TencentSdkProviderFaceInput input = new TencentSdkProviderFaceInput();
            input.setName(authInput.getRequest().getName());
            input.setIdNo(authInput.getRequest().getIdNo());
            return JsonUtils.obj2json(input);
        } catch (Exception cause) {
            log.warn("Fail to serialization on request .", cause);
            return StringUtils.EMPTY;
        }
    }

    @Override
    protected ProviderFaceAuthorizationResult detectHandleFaceAuthorizationReturned(String faceId, HandleFaceAuthorizationReturnInput request) {
        log.info("[腾讯云SDK] detectHandleFaceAuthorizationReturned faceId: {}, request: {}", faceId, request);
        FaceInfo faceInfo = faceRepository.getFaceInfoByFaceId(faceId);
        ProviderFaceInfo providerFaceInfo = providerFaceRepository.getByFaceId(faceInfo.getFaceId());

        Pair<Boolean, SdkOriginalReturnDataDTO> pair = resolverSdkOriginalReturnData(request.getExtendedMap());
        if (pair.getLeft()) {
            SdkOriginalReturnDataDTO right = pair.getRight();
            TencentCloudFaceResultStatus resultStatus = TencentCloudFaceResultStatus.getStatusByCode(right.getCode());
            resultStatus.setDesc(right.getDesc());
            ProviderException providerEx = new ProviderException(resultStatus.getFaceCode(), resultStatus);
            return TencentCloudFaceAuthorizationResult.createBuilder()
                    .setProvider(getSdkTencentWebAppIdVersion().getProviderName())
                    .setTimestamp(providerFaceInfo.getCreateTime().getTime())
                    .setSuccess(false)
                    .setFee(resultStatus.isFee())
                    .setContent(JSON.toJSONString(right))
                    .setCompleted(resultStatus.isCompleted())
                    .setFaceId(providerFaceInfo.getFaceId())
                    .setError(providerEx)
                    .build();
        }

        TencentWebAppIdVersion appIdVersion = getSdkTencentWebAppIdVersion();
        ProviderFaceAuthorizationResult faceAuthorizationResult = queryInvocationHandler.invoke(faceInfo, providerFaceInfo, appIdVersion);
        return faceAuthorizationResult;
    }

    public Pair<Boolean, SdkOriginalReturnDataDTO> resolverSdkOriginalReturnData(Map<String, String> extendedMap) {
        //防止 npe
        String originalReturnData = Optional.ofNullable(extendedMap).orElse(new HashMap<>())
                .get(HandleFaceAuthorizationReturnInput.ExtendedMapConstant.originalReturnData);
        if (StringUtils.isBlank(originalReturnData)) {
            return Pair.of(false, null);
        }
        try {
            SdkOriginalReturnDataDTO sdkOriginal = JSON.parseObject(originalReturnData, SdkOriginalReturnDataDTO.class);
            if (Objects.isNull(sdkOriginal) || Objects.isNull(sdkOriginal.getCode())) {
                return Pair.of(false, null);
            }
            return Pair.of(true, sdkOriginal);
        } catch (Exception e) {
            log.error("resolverSdkOriginalReturnData error : {} ", extendedMap, e);

        }
        return Pair.of(false, null);
    }

    /**
     * 获取腾讯云SDK版本
     *
     * @return
     */
    protected TencentWebAppIdVersion getSdkTencentWebAppIdVersion() {
        return TencentWebAppIdVersionUtil.getAppVersionBySceneCode(TencentWebAppIdVersionHookDTO.ABILITY_SDK, getProviderName());

    }

    @Override
    protected TencentWebAppIdVersion getProviderVersion(String appId) {
        return getSdkTencentWebAppIdVersion();
    }

    @Override
    protected TencentWebAppIdVersion getProviderVersion(String appId, String version) {
        return getSdkTencentWebAppIdVersion();
    }


    @Override
    public WakeupFaceResult handleWakeupFace(String faceId, WakeupFaceInput input) {
        log.info("[腾讯云SDK] handleWakeupFace faceId: {}, request: {}", faceId, input);
        FaceInfo faceInfo = faceRepository.getFaceInfoByFaceId(faceId);

        TencentWebAppIdVersion appIdVersion = getSdkTencentWebAppIdVersion();

        FaceAuthorizationInitializingContext initializingContext = getInitContext(faceInfo);
        TencentCloudCertificationInitializationUploadResultWrap uploadResultWrap =
                uploadInvocationHandler.invoke(initializingContext, appIdVersion);

        WakeupFaceResult wakeupFaceResult = uploadInvocationHandler.handleWakeupFace(uploadResultWrap.getResult(), faceInfo, appIdVersion);

        FaceAuthModeEnum faceAuthModeEnum = FaceAuthModeAdapter.deduceFaceAuthModeEnum(getProviderName());
        wakeupFaceResult.setFaceAuthMode(faceAuthModeEnum.name());
        faceRepository.faceApiVersion(initializingContext.getFaceId(), appIdVersion.getVersion());

        return wakeupFaceResult;
    }

    public FaceAuthorizationInitializingContext getInitContext(FaceInfo faceInfo) {
        String faceId = faceInfo.getFaceId();
        String callbackUrl = faceInfo.getCallbackUrl();
        String returnUrl = faceInfo.getReturnUrl();
        String photo = faceInfo.getPhoto();
        String photoType = faceInfo.getPhotoType();
        String name = faceInfo.getName();
        String idNo = faceInfo.getIdNo();
        String idType = faceInfo.getIdType();

        FaceRequestContext faceRequestContext = new TencentSdkRequestContext(faceInfo.getAppId(),
                name, idType, idNo, getProviderName(), photo, photoType);
        UserPhoto userPhoto = faceAuthorizationPhotoResolver.resolvePhoto(faceRequestContext);
        TencentCloudService.TencentCloudFaceAuthorizationRequestContext requestContext
                = new TencentCloudService.TencentCloudFaceAuthorizationRequestContext(faceRequestContext, userPhoto);
        FaceAuthorizationInitializingContext context = new FaceAuthorizationInitializingContext(faceId, requestContext, callbackUrl, returnUrl);
        return context;
    }

}
