package com.timevale.faceauth.service.domain.provider.tencent;

import com.alibaba.fastjson.JSON;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.domain.FaceAuthorizationPhotoResolver;
import com.timevale.faceauth.service.domain.provider.support.FaceAuthorizationInitializingContext;
import com.timevale.faceauth.service.domain.provider.support.RestTemplateRequestResolver;
import com.timevale.faceauth.service.domain.repository.FaceInfo;
import com.timevale.faceauth.service.inner.impl.afterface.entity.TencentSdkFaceContext;
import com.timevale.faceauth.service.result.WakeupFaceResult;
import com.timevale.faceauth.service.utils.providerLog.ProviderLogService;
import com.timevale.faceauth.service.utils.providerLog.result.TencentCloudInitResult;
import com.timevale.mandarin.base.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.UUID;

/**
 * <AUTHOR>
 * @DATE 2024/8/15 20:05
 */
@Component
@Slf4j
public class TencentCloudSdkCertificationInitializationUploadInvocationHandler
        extends TencentCloudFaceInvocationErrorHandler
        implements TencentCloudCertificationSignatureResolver<TencentCloudSdkCertificationInitializationUploadRequest> {

    private final ConfigurableProperties properties;
    private final FaceAuthorizationPhotoResolver faceAuthorizationPhotoResolver;
    private final RestTemplateRequestResolver requestResolver;

    @Autowired
    private ProviderLogService providerLogService;

    @Autowired
    public TencentCloudSdkCertificationInitializationUploadInvocationHandler(
            ConfigurableProperties properties,
            FaceAuthorizationPhotoResolver faceAuthorizationPhotoResolver,
            RestTemplateRequestResolver requestResolver) {
        this.properties = properties;
        this.faceAuthorizationPhotoResolver = faceAuthorizationPhotoResolver;
        this.requestResolver = requestResolver;
    }

    @Override
    public String resolveSignature(TencentCloudSdkCertificationInitializationUploadRequest request)
            throws FaceException {
        return TencentCloudUtil.signature(
                request.getWebankAppId(),
                request.getUserId(),
                request.getVersion(),
                request.getTicket(),
                request.getNonce()
        );
    }

    public TencentCloudCertificationInitializationUploadResultWrap invoke(
            FaceAuthorizationInitializingContext initializingContext, TencentWebAppIdVersion appIdVersion)
            throws FaceException {
        TencentCloudSdkCertificationInitializationUploadRequest request =
                prepareRequest(initializingContext, appIdVersion);
        URI uri = requestResolver.resolveURI(appIdVersion.getAccessHolder().getTencentCloudCertificationIdAscApi());
        RequestEntity<TencentCloudSdkCertificationInitializationUploadRequest> requestRequestEntity =
                (new RequestEntity<>(request, HttpMethod.POST, uri));
        TencentCloudInitResult tencentCloudInitResult = new TencentCloudInitResult(initializingContext.getFaceId(), 0);
        ResponseEntity<String> responseEntity =
                requestResolver.resolveResponse(
                        initializingContext.getFaceId(),
                        appIdVersion.getProviderName(),
                        requestRequestEntity,
                        String.class,
                        tencentCloudInitResult,
                        (x, y, z) -> providerLogService.logTencentCloudInit((TencentCloudInitResult) x, y, z),
                        (x, y, z) -> providerLogService.logTencentCloudInitWithException(
                                (TencentCloudInitResult) x, y, z)
                );
        TencentCloudCertificationInitializationUploadResponse response;
        try {
            response =
                    JsonUtils.json2pojo(
                            responseEntity.getBody(),
                            TencentCloudCertificationInitializationUploadResponse.class);
        } catch (Exception cause) {
            log.error(
                    "Data["
                            + responseEntity.getBody()
                            + "] could not instantiate type["
                            + TencentCloudCertificationInitializationUploadResponse.class
                            + "] .",
                    cause);
            throw FaceException.valueOf(FaceStatusCode.PROVIDER_ERROR_API_RESULT_SYNTAX);
        }
        this.handlerError(response);

        TencentCloudCertificationInitializationUploadResult result = response.getResultIfAbsent();
        String inputStr;
        try {
            // inputStr 数据将被落库，此时，request 上下文中的照片数据是以 base64 形式存在的，这个数据的长度不可控，可能导致长度超出 DB 的长度限制，
            // 为了解决这个问题，将照片保存的 key 设置为当前值，后续根据当前的 key 从数据仓库中获得实际的照片的 base64 数据。
            request.setSourcePhotoStr(initializingContext.getPhotoKey());
            inputStr = JsonUtils.obj2json(request);
        } catch (Exception cause) {
            log.error("Error serialize request .", cause);
            throw FaceException.valueOf(FaceStatusCode.PROVIDER_ERROR_API_RESULT_SYNTAX);
        }
        return (new TencentCloudCertificationInitializationUploadResultWrap(inputStr, result));
    }

    private TencentCloudSdkCertificationInitializationUploadRequest prepareRequest(
            FaceAuthorizationInitializingContext initializingContext, TencentWebAppIdVersion appIdVersion)
            throws FaceException {
        String faceCertType = initializingContext.getRequest().getIdType();
        String idType = TencentCloudIdentityTypeEnum.codeOfIdType(faceCertType);
        TencentCloudSdkCertificationInitializationUploadRequest request =
                TencentCloudSdkCertificationInitializationUploadRequest.createBuilder()
                        .setIdNo(initializingContext.getRequest().getIdNo())
                        .setIdType(idType)
                        .setName(initializingContext.getRequest().getName())
                        .setSourcePhotoStr(
                                faceAuthorizationPhotoResolver.resolvePhotoData(initializingContext.getPhotoKey()))
                        .setSourcePhotoType(
                                TencentCloudCertificationInitializationUploadRequest.PHOTO_TYPE_HIGH_DEFINITION)
                        .setUserId(initializingContext.getFaceId())
                        .setOrderNo(initializingContext.getFaceId())
                        .setSignatureResolver(this)
                        .setTicket(
                                TencentCloudUtil.getTicketCache(initializingContext.getFaceId(), appIdVersion))
                        .setWbappid(appIdVersion.getAccessHolder().getWebAppId())
                        .build();
        request.signatureRequest();
        return request;
    }



    public WakeupFaceResult handleWakeupFace(TencentCloudCertificationInitializationUploadResult uploadResult, FaceInfo faceInfo, TencentWebAppIdVersion appIdVersion) {
        String psnName = faceInfo.getName();
        String faceId = faceInfo.getFaceId();

        String txFaceId = uploadResult.getFaceId();

        String nonce = (UUID.randomUUID().toString().replace("-", ""));
        String nonceTicket = TencentCloudUtil.getNonceTicket(faceId, appIdVersion);

        String sign = TencentCloudUtil.signature(appIdVersion.getAccessHolder().getWebAppId(), faceId, nonce, TencentCloudCertificationSignableRequest.VERSION_1_0, nonceTicket);

        WakeupFaceResult wakeupFaceResult = new WakeupFaceResult();
        wakeupFaceResult.setTicket(sign);
        wakeupFaceResult.setIdentityName(psnName);
//        wakeupFaceResult.setDowngrade(false);
        wakeupFaceResult.setIdentityUrl(StringUtils.EMPTY);

        TencentSdkFaceContext faceContext = new TencentSdkFaceContext();
        faceContext.setUserId(faceId);
        faceContext.setOrderNo(faceId);
        faceContext.setNonce(nonce);
        faceContext.setFaceId(txFaceId);
        faceContext.setVersion(TencentCloudCertificationSignableRequest.VERSION_1_0);
        wakeupFaceResult.setFaceContext(faceContext.toMap());
        log.info("TencentCloudCertificationInitializationBeginningInvocationHandler handleWakeupFace : wakeupFaceResult =   {}",  JSON.toJSONString(wakeupFaceResult));
        return wakeupFaceResult;
    }
}
