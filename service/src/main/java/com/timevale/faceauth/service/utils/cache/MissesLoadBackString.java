package com.timevale.faceauth.service.utils.cache;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @DATE 2024/5/13 18:25
 */
public interface MissesLoadBackString {
    /**
     * 获取需要缓存的数据
     *
     * @return
     */
    Object find();


    /**
     * @return 缓存时间
     */
    default Integer getTimeout() {
        return 1;
    }

    /**
     * @return 缓存时间单位
     */
    default TimeUnit getTimeUnit() {
        return TimeUnit.HOURS;
    }
}
