package com.timevale.faceauth.service.utils.exceptions;

import lombok.extern.slf4j.Slf4j;

import java.util.Optional;
import java.util.function.Supplier;

/**
 * 异常处理类 ,可用于简化 try  catch 的使用
 *
 * @author: 芒草<xiongwen>
 * @DATE: 2022/12/6 下午4:02
 */
@Slf4j
public class IgnoreExceptionUtil {


    /**
     * 发生异常默认返回 null 对象
     *
     * @param supplier
     * @param <T>
     * @return
     */
    public static <T> T safeExceptionableWithResultNull(Supplier<T> supplier) {
        try {
            //获取数据
            return supplier.get();
        } catch (Exception e) {
            log.warn("IgnoreExceptionUtil 可忽略异常", e);
        }
        //未获取到数据可以返回空
        return null;
    }

    /**
     * 满足部分需求当返回为空的时候给与默认值  Optional#orElse()
     * <p>
     * 请自觉判断 Optional#isPresent() 是否为空
     * 否则直接 Optional#get() 可能会 NoSuchElementException("No value present")
     *
     * @param supplier
     * @param <T>
     * @return
     */
    public static <T> Optional<T> ofExceptionable(Supplier<T> supplier) {
        try {
            //获取数据
            return Optional.ofNullable(supplier.get());
        } catch (Exception e) {
            log.warn("IgnoreExceptionUtil 可忽略异常", e);
        }
        //未获取到数据可以返回空 ,所以不能使用Optional.empty()
        return Optional.empty();
    }


    /**
     * 直接忽略异常，常用于在写统计日志等数据统计能力
     *
     * @param runnable
     * @return
     */
    public static void ignore(Runnable runnable) {
        try {
            //执行逻辑
            runnable.run();
        } catch (Exception e) {
            log.warn("IgnoreExceptionUtil 可忽略异常", e);
        }
    }


    /**
     * 异常降级，但是打印error错误级别日志
     */
    public static <T> T downgradeExceptionableWithResultNull(Supplier<T> supplier) {
        try {
            //获取数据
            return supplier.get();
        } catch (Exception e) {
            log.error("异常被降级 请注意观察此是否影响客户数据 ", e);
        }
        //未获取到数据可以返回空
        return null;
    }
}
