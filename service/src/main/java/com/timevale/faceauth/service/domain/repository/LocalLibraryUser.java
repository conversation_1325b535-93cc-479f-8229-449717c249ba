package com.timevale.faceauth.service.domain.repository;

import com.timevale.faceauth.service.core.support.ArgumentUtil;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/7/22 10:49
 */
@Data
@Builder
public class LocalLibraryUser {

    private String bizAppId;
    private String provider;
    private String name;
    private String idNo;

    public void  valid(){
        ArgumentUtil.throwIfEmptyArgument(idNo, "idNo");
        ArgumentUtil.throwIfEmptyArgument(name, "name");
    }
}
