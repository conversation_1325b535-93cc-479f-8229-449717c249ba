package com.timevale.faceauth.service.domain.resource.http.download;

import com.google.common.util.concurrent.RateLimiter;
import com.timevale.mandarin.base.exception.BaseBizRuntimeException;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

@Slf4j
public class GuavaRateLimited implements BandwidthLimiter{

    // 默认最大速率为 1 MB/s
    private static final long MB = 1024 * 1024L;
    private static volatile long maxBytesPerSecond = 1 * MB;
    private static RateLimiter rateLimiter ;


    private int buffSize = BUFFER_SIZE_DEFAULT;
    private InputStream input;
    private OutputStream output;

    @Override
    public String name() {
        return NAME_GUAVA_LIMITED;
    }

    @Override
    public void init(long maxRate, int bufferSize) {
        if(bufferSize > 1){
            this.buffSize = bufferSize;
        }

        if(rateLimiter == null){
            maxBytesPerSecond = maxRate;
            synchronized (GuavaRateLimited.class) {
                if(rateLimiter == null){
                    rateLimiter = RateLimiter.create(maxBytesPerSecond);
                }

            }
            return ;
        }

        if(maxRate != maxBytesPerSecond){
            maxBytesPerSecond = maxRate;
            rateLimiter.setRate(maxRate);
        }
    }

    @Override
    public void read(InputStream input, OutputStream output)throws IOException {
            check();

            //TODO faceId 控制次数
            byte[] buffer = new byte[this.buffSize];
            int bytesRead;
            while ((bytesRead = input.read(buffer)) != -1) {
                // 限速
                rateLimiter.acquire(bytesRead);
                output.write(buffer,0,bytesRead);
            }

            this.input = input;
            this.output = output;
    }

    @Override
    public void after() {
        if(input != null ){
            try {
                input.close();
            } catch (IOException e) {
                log.error(this.name()+" input close fail ",e);
            }
        }

        if(output != null){
            try {
                output.close();
            } catch (IOException e) {
                log.error(this.name()+" output close fail ",e);
            }
        }
    }

    private void check(){
        if(rateLimiter == null){
             throw new BaseBizRuntimeException("{} is not init .", this.name());
        }
    }
}
