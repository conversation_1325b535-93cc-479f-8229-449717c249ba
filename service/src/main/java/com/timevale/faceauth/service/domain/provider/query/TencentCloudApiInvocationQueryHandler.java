package com.timevale.faceauth.service.domain.provider.query;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.domain.provider.support.HttpRequestResolver;
import com.timevale.faceauth.service.domain.provider.tencent.*;
import com.timevale.faceauth.service.domain.repository.FaceInfo;
import com.timevale.faceauth.service.domain.repository.FaceRepository;
import com.timevale.faceauth.service.domain.repository.ProviderFaceInfo;
import com.timevale.faceauth.service.domain.repository.ProviderFaceRepository;
import com.timevale.faceauth.service.domain.resource.*;
import com.timevale.faceauth.service.enums.FaceCertTypeEnum;
import com.timevale.faceauth.service.utils.UrlUtil;
import com.timevale.faceauth.service.utils.providerLog.ProviderLogService;
import com.timevale.faceauth.service.utils.providerLog.result.TencentCloudQueryResult;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.Objects;

/**
 * 腾讯云刷脸 API 调用基本处理
 *
 * <AUTHOR>
 * @since 2023/2/21
 */
@Slf4j
@Component
public class TencentCloudApiInvocationQueryHandler implements ProviderApiInvocationQueryHandler<TencentCloudCertificationQueryResponse>{


    @Autowired
    private TencentCloudApiInvocationQueryRequestConverter queryApiRequestConverter;
    @Autowired
    private ProviderTencentCloudH5RemoteQueryInvocationHandler queryInvocationHandler;
    @Autowired
    private
    ConfigurableProperties properties;
    @Autowired
    private ProviderLogService providerLogService;

    @Autowired
    private FaceRepository faceRepository;

    @Autowired
    private ProviderFaceRepository providerFaceRepository;


    @Override
    public ProviderResponse<TencentCloudCertificationQueryResponse> call(HttpRequestResolver requestResolver , ProviderQueryContext context) {
        ProviderTencentCloudQueryContext
                tenantCloudQueryContext = this.revokerContext(context);
        if(tenantCloudQueryContext == null){
            throw new FaceException(FaceStatusCode.ERR_INNER, "query context is bad . context is null ");
        }

        long start = System.currentTimeMillis();
        String faceId = context.getFaceId();
        FaceInfo faceInfo = context.getFaceInfo();
        TencentCloudCertificationQueryRequest queryRequest = tenantCloudQueryContext.getQueryRequest();
        TencentWebAppIdVersion appIdVersion = tenantCloudQueryContext.getAppIdVersion();
        queryRequest = queryApiRequestConverter.work(queryRequest, context);
        String uriStr = properties.getTencentCloudCertificationQueryApi();
        uriStr = UrlUtil.appendParam(uriStr, "orderNo", queryRequest.getOrderNo());

        log.info("tencentCloud query request param {},faceId={}", uriStr, faceId);
        TencentCloudQueryResult tencentCloudQueryResult = new TencentCloudQueryResult(faceId, 0);

        URI uri = requestResolver.resolveURI(uriStr);
        RequestEntity<TencentCloudCertificationQueryRequest> requestEntity =
                (new RequestEntity<>(queryRequest, HttpMethod.POST, uri));
        if(Objects.equals(Boolean.TRUE,ConfigurableProperties.getInstance().getTencentCloudQueryMock())){
            uri = requestResolver.resolveURI(ConfigurableProperties.getInstance().getTencentCloudQueryMockUrl());
            if(!context.getResourceTypes().contains(FaceResourceType.VIDEO)){
                uri = requestResolver.resolveURI(ConfigurableProperties.getInstance().getByteDanceH5QueryMockUrlBase());
            }
            requestEntity =
                    (new RequestEntity<>(queryRequest, HttpMethod.GET, uri));
        }
        ResponseEntity<String> responseEntity =
                requestResolver.resolveResponse(
                        faceId,
                        appIdVersion.getProviderName(),
                        requestEntity,
                        String.class,
                        tencentCloudQueryResult,
                        (x, y, z) -> providerLogService.logTencentCloudQuery((TencentCloudQueryResult) x, y, z),
                        (x, y, z) -> providerLogService.logTencentCloudQueryWithException(
                                (TencentCloudQueryResult) x, y, z));
        long end = System.currentTimeMillis();
        log.info("tencentCloud query result. faceId={}， cost: {} ms", faceId, (end - start));
        TencentCloudCertificationQueryResponse tencentQueryResponse =
                detectAuthorizationResult(responseEntity, requestEntity);

        TencentCloudCertificationQueryResult queryResult =
                tencentQueryResponse.getResultIfAbsent(new TencentCloudCertificationQueryResult());

        String videoBase64 = null;
        String photoBase64 = null;

        if (null != queryResult) {
            videoBase64 = queryResult.getVideo();
            photoBase64 = queryResult.getPhoto();
        }
        boolean completed = tencentQueryResponse.success();
        boolean success = false;
        if (completed) {
            boolean faceAuthorizationState =
                    this.isSuccessAuthorization(queryResult, faceInfo.getIdType());
            success = faceAuthorizationState;
        }
        return ProviderResponse.<TencentCloudCertificationQueryResponse>builder()
                .completed(completed)
                .success(success)
                .videoBase64(videoBase64)
                .photoBase64(photoBase64)
                .originData(tencentQueryResponse)
                .build();
    }

        private boolean isSuccessAuthorization(TencentCloudCertificationQueryResult queryResult, String certType) {
            //非大陆只有活体率分值
            if (FaceCertTypeEnum.notCHIDCard(certType)) {
                log.info("tencentCloud notCHIDCard isSuccessAuthorization , faceId = {} ", queryResult.getOrderNo());
                return (queryResult.getLiveRate() >= properties.getTencentCloudCertificationLiveRateMin());
            }

            //大陆有活体率分值和相似度分值
            return (queryResult.getSimilarity() >= properties.getTencentCloudCertificationSimilarityMin()
                    && queryResult.getLiveRate() >= properties.getTencentCloudCertificationLiveRateMin());
        }

    private TencentCloudCertificationQueryResponse detectAuthorizationResult(
            ResponseEntity<String> responseEntity,
            RequestEntity<TencentCloudCertificationQueryRequest> requestEntity)
            throws FaceException {

        String content = responseEntity.getBody();
        TencentCloudCertificationQueryResponse response;
        try {
            response = JsonUtils.json2pojo(content, TencentCloudCertificationQueryResponse.class);
        } catch (Exception cause) {
            log.error(
                    "Error response content["
                            + content
                            + "] on api["
                            + requestEntity.getUrl().toString()
                            + "]",
                    cause);
            throw FaceException.valueOf(FaceStatusCode.PROVIDER_ERROR_API_RESULT_SYNTAX);
        }


        return response;
    }


    protected ProviderTencentCloudQueryContext revokerContext(ProviderResourceBaseContext context){
        ProviderTencentCloudQueryContext tenantCloudQueryContext = null;
        if(context instanceof ProviderTencentCloudQueryContext){
            tenantCloudQueryContext = (ProviderTencentCloudQueryContext)context;
        } else {
            ProviderResourceQueryContext queryContext = initContext(context.getFaceId());
            FaceInfo faceInfo = queryContext.getFaceInfo();
            final TencentWebAppIdVersion appIdVersion =
                    TencentCloudUtil.getProviderVersion(faceInfo.getAppId(),faceInfo.getProviderApiVersion());
            TencentCloudCertificationQueryRequest queryBody = queryInvocationHandler.prepareRequestQueryBoby(queryContext.getFaceId(),queryContext.getProviderOrderNo(), appIdVersion);

            tenantCloudQueryContext = new ProviderTencentCloudQueryContext();
            tenantCloudQueryContext.setFaceId(faceInfo.getFaceId());
            tenantCloudQueryContext.setFaceInfo(faceInfo);
            tenantCloudQueryContext.setProviderOrderNo(queryContext.getProviderOrderNo());
            tenantCloudQueryContext.setAppIdVersion(appIdVersion);
            tenantCloudQueryContext.setQueryRequest(queryBody);
        }
        return tenantCloudQueryContext;
    }

    protected ProviderResourceQueryContext initContext(String faceId){
        FaceInfo faceInfo = faceRepository.getFaceInfoByFaceId(faceId);
        ProviderFaceInfo providerFaceInfo = providerFaceRepository.getByFaceId(faceId);

        ProviderResourceQueryContext queryContext = new ProviderResourceQueryContext();
        queryContext.setFaceId(faceId);
        queryContext.setFaceInfo(faceInfo);
        queryContext.setProviderOrderNo(providerFaceInfo.getOrderNo());
        return queryContext;
    }
}
