package com.timevale.faceauth.service.domain.provider.tiktok.face.service;

import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationResult;
import com.timevale.faceauth.service.domain.provider.tiktok.face.domain.TiktokFaceResultStatusDomain;
import com.timevale.faceauth.service.domain.repository.FaceInfo;
import com.timevale.faceauth.service.domain.repository.ProviderFaceInfo;

/**
 * <AUTHOR>
 * @DATE 2024/5/21 10:39
 */
public interface TiktokOpenApiClient {

    /**
     * code换openId
     *
     * @param code
     * @return
     */
    String getOpenId(String code);

    /**
     * 获取抖音刷脸票据
     *
     * @param openId
     * @param aid
     * @param identityName
     * @param identityCode
     * @return
     */
    String getTicket(String openId, String aid, String identityName, String identityCode);


    /**
     * 抽象模板内获取刷脸结果
     *
     * @param faceInfo
     * @param providerFaceInfo
     * @param outResultStatus  外部指定的错误码 在未刷脸比对成功的情况下&&有值 被启用
     * @return
     */
    ProviderFaceAuthorizationResult invoke(FaceInfo faceInfo, ProviderFaceInfo providerFaceInfo, TiktokFaceResultStatusDomain outResultStatus);


    /**
     * 抖音的错误码映射天谷错误码信息
     *
     * @param code
     * @param errMsg
     * @return
     */
    TiktokFaceResultStatusDomain getStatusByCode(int code, String errMsg);
}
