package com.timevale.faceauth.service.domain.provider.tiktok.face.entity.response;

import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @DATE 2024/5/13 20:44
 */
@Data
public class TiktokGetTicketResultResponse extends TiktokAbstractResponse {
    private int err_no;
    private String err_tips;

    /**
     * 0:实名成功，非0:实名失败
     */
    private String state;
    /**
     * 实名数据
     */
    private TiktokGetTicketResultResponse.Data flow_data;

    /**
     * 加密算法信息
     */
    private TiktokGetTicketResultResponse.ENCRYPTION_INFO encryption_info;

    /**
     * 完整响应body
     */
    private String responseBody;

    /**
     * 刷脸是否比对成功
     * 必须要错误码为0 和status 才是认证成功
     *
     * @return
     */
    public boolean success() {
        return StringUtils.equals(this.getState(), "0") && Objects.equals(getErr_no(), 0);
    }


    @lombok.Data
    public static class Data extends ToString {

        /**
         * 录制视频（.mp4），3m左右
         * 若是加密模式，返回的是加密的url
         */
        private String user_video;
        /**
         * 录制截图（.jpeg），1m左右
         * 若是加密模式，返回的是加密的url
         */
        private String user_image;

        /**
         * 分数（范围在0-100）
         */
        private String score;
        /**
         * 当前算法的误识率为1/1000时，对应的score
         */
        private String threshold1;
        /**
         * 当前算法的误识率为1/10000时，对应的score
         */
        private String threshold2;
        /**
         * 当前算法的误识率为1/100000时，对应的score
         */
        private String threshold3;

    }


    @lombok.Data
    public static class ENCRYPTION_INFO extends ToString {

        private String key;
        private String iv;

    }
}
