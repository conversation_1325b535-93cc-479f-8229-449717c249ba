package com.timevale.faceauth.service.domain.provider.query;

import lombok.Builder;

/**
 * <AUTHOR>
 */
@Builder
public class ProviderResponse<T> {

    private boolean success;
    private boolean completed;

    private String videoBase64;
    private String photoBase64;
    private String videoKey;
    private String photoKey;

    private T originData;



    public boolean isSuccess() {
        return success;
    }

    public boolean isCompleted() {
        return completed;
    }

    public String getVideoBase64() {
        return videoBase64;
    }

    public String getPhotoBase64() {
        return photoBase64;
    }

    public String getVideoKey() {
        return videoKey;
    }

    public String getPhotoKey() {
        return photoKey;
    }

    public T getOriginData() {
        return originData;
    }

    public void setVideoKey(String videoKey) {
        this.videoKey = videoKey;
    }

    public void setPhotoKey(String photoKey) {
        this.photoKey = photoKey;
    }
}
