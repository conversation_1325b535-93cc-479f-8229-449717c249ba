package com.timevale.faceauth.service.integration.grayscale.domain;

import lombok.Getter;

/**
 * 灰度功能点枚举
 * @link https://testsupport.tsign.cn/microfe/saas/grayManage/list
 * <AUTHOR>
 * @since 2024/5/22 18:08
 */

public enum GrayFunctionEnum implements GrayFunction {

    //
    BYTEDANCE_VERSION_UPGRADE("identity","bytedance-version-upgrade"),
    ;

    GrayFunctionEnum(String projectKey, String functionKey) {
        this.projectKey = projectKey;
        this.functionKey = functionKey;
    }
    @Getter
    private final String projectKey;
    @Getter
    private final String functionKey;

}