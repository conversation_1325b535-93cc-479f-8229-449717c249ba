package com.timevale.faceauth.service.domain.provider.tencent;

import com.timevale.faceauth.service.domain.ConfigurableProperties;

/**
 * 腾讯云刷脸意愿核身配置
 *
 * <AUTHOR>
 * @since 2021-11-09 20:17
 */
public class TencentWebAccessWillHolder implements TencentWebAccessHolder{
    @Override
    public String getWebAppId() {
        return "TIDALB0b";
    }

    @Override
    public String getWebAppSecret() {
        return "EoiYGrYNB1xAVTcnqMMhZ1Ln2P0vlalPRWyZW8KRMykDV3m1sbECK53rP75tGqxr";
    }

    @Override
    public boolean isDoRefresh() {
        return true;
    }
}
