package com.timevale.faceauth.service.domain.resource.http.download;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * 定义了一个带宽限制器接口，用于在下载过程中限制数据传输速率
 * 该接口的主要作用是提供一种机制来控制网络资源的使用，防止因数据传输过快而占用过多带宽
 *
 * <AUTHOR>
 * @param <T> 继承自OutputStream的输出流类型，表示数据的目标输出类型
 */
public interface BandwidthLimiter<T extends OutputStream> {

    // 默认的缓冲区大小，单位是字节
    int BUFFER_SIZE_DEFAULT  = 8192;
    // 默认的限速器名称
    String NAME_DEFAULT = "default";
    // 使用Guava库进行限速时的限速器名称
    String NAME_GUAVA_LIMITED = "guavaLimited";

    /**
     * 获取限速器的名称
     *
     * @return 限速器的名称，用于标识不同的限速策略
     */
    String name();

    /**
     * 初始化限速器
     *
     * @param maxRate 最大传输速率，单位是字节/秒，用于限制数据传输速度
     * @param bufferSize 缓冲区大小，单位是字节，表示每次读取数据的块大小
     */
    void init(long maxRate, int bufferSize);

    /**
     * 从输入流中读取数据，并将其写入到输出流中，同时应用带宽限制
     *
     * @param input 输入流，表示数据的来源
     * @param output 输出流，表示数据的目标，它是一个泛型，继承自OutputStream
     * @throws IOException 如果在读取或写入数据时发生I/O错误
     */
    void read(InputStream input,  T output) throws IOException;

    /**
     * 在数据传输完成后调用，用于释放资源或重置状态
     */
    void after();

}
