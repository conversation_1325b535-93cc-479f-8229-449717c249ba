package com.timevale.faceauth.service.domain.resource;

import com.timevale.faceauth.service.domain.provider.query.ProviderResponse;
import com.timevale.faceauth.service.domain.provider.query.TencentCloudApiInvocationQueryHandler;
import com.timevale.faceauth.service.domain.resource.http.download.BandwidthLimitHttpRequestResolver;
import com.timevale.faceauth.service.domain.resource.store.VideoResourceStore;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.timevale.faceauth.service.domain.provider.ConfigurableProviderService.PROVIDER_TENCENT_CLOUD;

/**
 * 供应商资源加载器
 * <AUTHOR>
 * @since 2025/12/23 14:35
 */
@Slf4j
@Component
public class ProviderTencentCloudH5RemoteVideoSyncResourceLoader extends ProviderTencentCloudRemoteResourceAbstractService {

    @Autowired
    private BandwidthLimitHttpRequestResolver requestResolver;
    @Autowired
    private TencentCloudApiInvocationQueryHandler invocationQueryHandler;
    @Autowired
    private VideoResourceStore resourceStore;


    @Override
    public String execute(ProviderResourceBaseContext context) {
        ProviderQueryContext
                queryContext = super.revokerContext(context);
        if(queryContext == null){
            super.logExecuteContextNull();
            return null;
        }

        return call(queryContext);
    }



    private String call(ProviderQueryContext queryContext) {
        try {
            ProviderResponse response =
                    invocationQueryHandler.call(requestResolver,
                            queryContext);
            resourceStore.done(queryContext, response);
            return response.getVideoKey();
        } catch (Exception e){
            log.error("call bytedance provider resource fail", e);
        }
        return null;
    }


    @Override
    public String strategy() {
        return ResourceStrategyEnum.SYNC.getCode();
    }


    @Override
    public String getProviderName() {
        return PROVIDER_TENCENT_CLOUD;
    }

    @Override
    public FaceResourceType resourceType() {
        return FaceResourceType.VIDEO;
    }
}
