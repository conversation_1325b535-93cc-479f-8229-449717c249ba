package com.timevale.faceauth.service.domain.event.sdk;

import com.timevale.faceauth.service.domain.event.FaceEvent;
import com.timevale.mandarin.weaver.utils.RequestContext;
import lombok.Getter;

/**
 * <AUTHOR>
 * @DATE 2024/8/29 10:25
 */
@Getter
public class AsyncSdkPointEvent extends FaceEvent<String> {
    private String gid;
    private String sdkVersion;
    /**
     * 包ID，用于唯一标识一个包
     */
    private String bundleId;
    private String sdkCustomerId;


    private String traceId;

    public AsyncSdkPointEvent(String gid, String sdkVersion, String bundleId, String sdkCustomerId) {
        super(null, sdkVersion);
        this.gid = gid;
        this.sdkVersion = sdkVersion;
        // 2024/11/9  新增包名
        this.bundleId = bundleId;
        this.sdkCustomerId = sdkCustomerId;

        //获取上下文的 traceId
        try {
            traceId = RequestContext.getTransactionId();
        } catch (Exception e) {

        }
    }
}
