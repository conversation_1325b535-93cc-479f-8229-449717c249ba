package com.timevale.faceauth.service.inner.impl.afterface;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @DATE 2024/8/13 14:24
 */
@Component
public class ProviderFaceResourceFactory {

    @Resource
    private List<ProviderFaceResourceService> serviceList;

    public ProviderFaceResourceService getService(Integer faceAuthModeValue) {
        for (ProviderFaceResourceService service : Optional.ofNullable(serviceList).orElse(Collections.emptyList())) {
            if (Objects.equals(service.mode().getMode(), faceAuthModeValue)) {
                return service;
            }
        }
        // unsupported, and throws
        throw FaceException.valueOf(FaceStatusCode.SERVICE_UNSUPPORTED);
    }

}
