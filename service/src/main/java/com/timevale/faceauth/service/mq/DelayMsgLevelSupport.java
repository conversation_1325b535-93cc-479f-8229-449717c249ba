package com.timevale.faceauth.service.mq;

import com.timevale.framework.mq.client.producer.DelayMsgLevel;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public class DelayMsgLevelSupport {

    public static DelayMsgLevel codeOf(Integer level){
        if(level == null|| level < 1){
            return null;
        }

        for(DelayMsgLevel msgLevel : DelayMsgLevel.values()){
            if(Objects.equals(msgLevel.getLevel(), level)){
                return msgLevel;
            }
        }
        return null;
    }
}
