package com.timevale.faceauth.service.domain.provider.audiovideodual.esign;

import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.exception.ProviderErrorEnum;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mediaauth.facade.enums.VideoCompletedErrorCodeEnum;
import com.timevale.mediaauth.facade.enums.VideoCompletedStatusEnum;

import java.util.Objects;

/**
 * 对外透出结果错误码
 *
 * <AUTHOR>
 * @since 2020-07-06 19:43
 */
public enum VideoCompletedStatus implements ProviderErrorEnum {
  OK(VideoCompletedErrorCodeEnum.OK,FaceStatusCode.OK),
  FAIL(VideoCompletedErrorCodeEnum.FAIL,FaceStatusCode.FAIL_AUTHORIZATION),
  DOING(VideoCompletedErrorCodeEnum.DOING,FaceStatusCode.RESOURCE_VIDEO_UNCOMPLETED),
  EXPIRED(VideoCompletedErrorCodeEnum.EXPIRED, FaceStatusCode.RESOURCE_VIDEO_UNCOMPLETED),
  INTERNAL_ERROR(VideoCompletedErrorCodeEnum.INTERNAL_ERROR,FaceStatusCode.PROVIDER_FAILED_API),


  SYS_VOICE_ERROR(VideoCompletedErrorCodeEnum.SYS_VOICE_ERROR,FaceStatusCode.FACE_VIDEO_SYS_VOICE_ERROR),



  USER_ANSWER_INCONSISTENT(VideoCompletedErrorCodeEnum.USER_ANSWER_INCONSISTENT,FaceStatusCode.FACE_VIDEO_USER_ANSWER_INCONSISTENT),

  USER_NO_AUDIO(VideoCompletedErrorCodeEnum.USER_NO_AUDIO,FaceStatusCode.FACE_VIDEO_USER_NO_AUDIO),

  INFO_ID_NAME_NOT_MATCH(VideoCompletedErrorCodeEnum.INFO_ID_NAME_NOT_MATCH,FaceStatusCode.IDENTITY_PHOTO_NAME_IDNO_ERROR),
  INFO_PHOTO_COMPARE_FAIL(VideoCompletedErrorCodeEnum.INFO_PHOTO_COMPARE_FAIL,FaceStatusCode.IDENTITY_PHOTO_CHECK_FAIL),


  CLIENT_BROWSER_UNSUPPORTED(VideoCompletedErrorCodeEnum.CLIENT_BROWSER_UNSUPPORTED,FaceStatusCode.FACE_CLIENT_BROWSER_UNSUPPORTED),

  CLIENT_DEVICE_UNSUPPORTED(VideoCompletedErrorCodeEnum.CLIENT_DEVICE_UNSUPPORTED,FaceStatusCode.FACE_CLIENT_DEVICE_UNSUPPORTED),

  ;

  private VideoCompletedErrorCodeEnum errorCodeEnum;
  private FaceStatusCode faceCode;


  VideoCompletedStatus(VideoCompletedErrorCodeEnum errorCodeEnum, FaceStatusCode faceCode) {
    this.errorCodeEnum = errorCodeEnum;
    this.faceCode = faceCode;
  }

  @Override
  public String errCode() {
    return errorCodeEnum.getErrorCode();
  }

  @Override
  public String errMsg() {
    return errorCodeEnum.getDescription();
  }

  public VideoCompletedErrorCodeEnum getErrorCodeEnum() {
    return errorCodeEnum;
  }

  public FaceStatusCode getFaceCode() {
    return faceCode;
  }

  public static VideoCompletedStatus errorCodeOf(String errorCode){
     if(StringUtils.isBlank(errorCode)){
        return DOING;
     }
     for(VideoCompletedStatus completedStatus : VideoCompletedStatus.values()){
        if(Objects.equals(completedStatus.errCode(), errorCode)){
            return completedStatus;
        }
     }
     return DOING;
  }
}
