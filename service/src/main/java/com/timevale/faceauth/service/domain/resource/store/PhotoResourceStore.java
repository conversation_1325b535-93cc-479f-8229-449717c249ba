package com.timevale.faceauth.service.domain.resource.store;

import com.timevale.faceauth.service.domain.provider.query.ProviderResponse;
import com.timevale.faceauth.service.domain.provider.support.FaceBase64Resources;
import com.timevale.faceauth.service.domain.provider.support.FaceResourcesInvocationHandler;
import com.timevale.faceauth.service.domain.repository.FaceInfo;
import com.timevale.faceauth.service.domain.repository.FaceOSSResources;
import com.timevale.faceauth.service.domain.resource.FaceResourceType;
import com.timevale.faceauth.service.domain.resource.ProviderQueryContext;
import com.timevale.mandarin.base.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class PhotoResourceStore implements ResourceStore{
    @Autowired
    private FaceResourcesInvocationHandler resourcesInvocationHandler;

    @Override
    public void done(ProviderQueryContext context, ProviderResponse providerResponse) {
        if (StringUtils.isBlank(providerResponse.getPhotoBase64())) {
           return;
        }
        FaceInfo faceInfo = context.getFaceInfo();
        FaceOSSResources ossResources =
                resourcesInvocationHandler.persistentFaceResources(
                        new FaceBase64Resources(providerResponse.getPhotoBase64(), null), faceInfo);
        if(providerResponse.isSuccess()){
            resourcesInvocationHandler.saveUserPhoto(faceInfo, ossResources);
        }
        providerResponse.setPhotoKey(ossResources.getPhoto());
    }

    @Override
    public FaceResourceType getResourceType() {
        return FaceResourceType.PHOTO;
    }
}
