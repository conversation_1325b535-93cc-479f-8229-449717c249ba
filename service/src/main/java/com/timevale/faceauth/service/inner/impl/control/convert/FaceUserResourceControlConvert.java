package com.timevale.faceauth.service.inner.impl.control.convert;

import com.timevale.faceauth.dal.pfs.dataobject.FaceUserResourceControlRecordDO;
import com.timevale.faceauth.service.domain.controll.FaceResourceControlStatusEnum;
import com.timevale.faceauth.service.input.control.ControlRecordInput;
import com.timevale.faceauth.service.result.control.ControlRecordResult;
import com.timevale.mandarin.base.util.JsonUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2025/7/7 15:03
 */
public class FaceUserResourceControlConvert {

    public static ControlRecordResult convert(FaceUserResourceControlRecordDO param, boolean convertProof) {
        if (param == null) {
            return null;
        }

        ControlRecordResult result = JsonUtils.obj2pojo(param, ControlRecordResult.class);
        // 2025/7/7  处理枚举描述
        result.setStatusDesc(FaceResourceControlStatusEnum.ofDesc(param.getStatus()));

        // 2025/7/7  处理证明材料
        if (convertProof) {
            List<String> fileKeys = Optional.ofNullable(param.getProofDocumentDO().getFileKeys()).orElse(Collections.emptyList());
            List<ControlRecordInput.ProofDocumentDTO> collect = fileKeys.stream().map(e -> {
                ControlRecordInput.ProofDocumentDTO dto = new ControlRecordInput.ProofDocumentDTO();
                dto.setFileKey(e);
                return dto;
            }).collect(Collectors.toList());
            result.setProofDocumentList(collect);
        }
        return result;
    }
}
