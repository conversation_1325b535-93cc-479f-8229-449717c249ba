package com.timevale.faceauth.service.integration.grayscale;

import com.timevale.faceauth.service.integration.grayscale.domain.GrayFunction;
import com.timevale.gray.config.manage.service.api.GrayscaleRpcService;
import com.timevale.gray.config.manage.service.model.base.BaseResult;
import com.timevale.gray.config.manage.service.model.request.CheckGrayscaleFunctionRequest;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * SaaS灰度版本的控制Adapt
 *
 * <AUTHOR>
 * @since 2023/8/10 15:07
 */
@Component
@Slf4j
public class RpcSaaSGrayConfigAdapt {

  @Autowired
  private GrayscaleRpcService grayscaleRpcService;


  public boolean inGray(String key, GrayFunction functionKey) {
    if (StringUtils.isBlank(key) || Objects.isNull(functionKey)) {
      return false;
    }
    CheckGrayscaleFunctionRequest request = new CheckGrayscaleFunctionRequest();
    request.setProjectKey(functionKey.getProjectKey());
    request.setFunctionKey(functionKey.getFunctionKey());
    request.setUserId(key);
    request.setGid(false);
    BaseResult<Boolean> baseResult = grayscaleRpcService.checkGrayscaleFunctionV2(request);
    return baseResult.getData();
  }

}