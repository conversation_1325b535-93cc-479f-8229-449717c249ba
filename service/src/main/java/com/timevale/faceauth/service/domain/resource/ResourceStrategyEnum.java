package com.timevale.faceauth.service.domain.resource;

/**
 * <AUTHOR>
 */
public enum ResourceStrategyEnum {

    SYNC("sync","同步"),
    ASYNC("async","异步"),
    DEFAULT("default","系统默认"),
    ;


    private String code;
    private String desc;


    ResourceStrategyEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public static ResourceStrategyEnum getByCode(String code) {
        for (ResourceStrategyEnum resourceStrategyEnum : ResourceStrategyEnum.values()){
            if (resourceStrategyEnum.code.equalsIgnoreCase(code)){
                return resourceStrategyEnum;
            }
        }

        return DEFAULT;
    }
}
