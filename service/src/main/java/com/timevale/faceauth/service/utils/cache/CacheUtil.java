//package com.timevale.faceauth.service.utils.cache;
//
//import com.timevale.framework.tedis.util.TedisUtil;
//import com.timevale.mandarin.base.util.StringUtils;
//import lombok.extern.slf4j.Slf4j;
//
///**
// * <AUTHOR>
// * @DATE 2024/8/6 16:09
// */
//@Slf4j
//public class CacheUtil {
//
//    /**
//     * |缓存 如果没有则从业务里获取
//     */
//    public static <V> V get(String key, MissesLoadBackString back) {
//        if (StringUtils.isBlank(key)) {
//            log.warn("不接收为空的key ,直接返回null对象");
//            return null;
//        }
//        Object value = TedisUtil.get(key.toString());
//        if (value == null) {
//            value = back.find();
//            // NPE控制处理要搞一下 但可能触发缓存穿透
//            if (value != null) {
//                TedisUtil.set(key, value, back.getTimeout(), back.getTimeUnit());
//            }
//        }
//        return (V) value;
//    }
//}
