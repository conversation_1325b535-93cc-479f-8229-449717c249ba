package com.timevale.faceauth.service.domain.resource.http;

import com.timevale.faceauth.service.domain.resource.ResourceSystemHolder;
import com.timevale.faceauth.service.domain.support.HttpTemplateConfig;
import com.timevale.faceauth.service.domain.support.RestTemplateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.client.RestTemplate;

@Slf4j
public class ResourceHttpUtil   {



  /** 私有化构造器 */
  private ResourceHttpUtil() {

  }

  private RestTemplate createRestTemplate() {
    ResourceSystemHolder systemHolder = ResourceSystemHolder.getInstance();
    HttpTemplateConfig templateConfig = new BandwidthLimitTemplateConfig(systemHolder);
    RestTemplate CLIENT_RECOGNITION = new RestTemplateUtil().restTemplate(templateConfig);
    CLIENT_RECOGNITION.getMessageConverters()
            .add(RestTemplateUtil.mappingJackson2HttpMessageConverter);
    return  CLIENT_RECOGNITION;
  }

  public static RestTemplate getRestTemplate() {
    return InnerHolder.HTTP_INSTANCE;
  }



  private static class InnerHolder {

    public static final RestTemplate HTTP_INSTANCE = new ResourceHttpUtil().createRestTemplate();
  }
}
