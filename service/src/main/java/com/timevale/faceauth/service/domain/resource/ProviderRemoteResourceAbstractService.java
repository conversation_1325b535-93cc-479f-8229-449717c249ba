package com.timevale.faceauth.service.domain.resource;

import com.google.common.collect.Sets;
import com.timevale.faceauth.service.domain.repository.FaceInfo;
import com.timevale.faceauth.service.domain.repository.FaceRepository;
import com.timevale.faceauth.service.domain.repository.ProviderFaceInfo;
import com.timevale.faceauth.service.domain.repository.ProviderFaceRepository;
import com.timevale.faceauth.service.domain.resource.task.ResourceDownloadMsg;
import com.timevale.faceauth.service.mq.MQManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class ProviderRemoteResourceAbstractService implements ProviderRemoteResourceLoader{
    @Autowired
    private MQManager mqManager;

    @Autowired
    private FaceRepository faceRepository;

    @Autowired
    private ProviderFaceRepository providerFaceRepository;


    protected String asyncTask(ProviderResourceQueryContext context){
        ResourceDownloadMsg downloadMsg = new ResourceDownloadMsg();
        downloadMsg.setFaceId(context.getFaceId());
        downloadMsg.setProvider(getProviderName());
        downloadMsg.setResourceType(resourceType().getCode());
        mqManager.sendResourceDownloadMsg(downloadMsg);
        return null;
    }

    protected void logExecuteContextNull(){
        log.error("ResourceLoader execute fail . the {} {} {}. context is null ",
                getProviderName(), resourceType().getCode(), strategy());
    }

    protected ProviderQueryContext revokerContext(ProviderResourceBaseContext context){
        ProviderQueryContext queryContext = new ProviderQueryContext();
        queryContext.setResourceTypes(Sets.newHashSet(resourceType()));
        queryContext.setFaceId(context.getFaceId());
        if(context instanceof ProviderResourceQueryContext){
            ProviderResourceQueryContext resourceQueryContext = (ProviderResourceQueryContext)context;
            queryContext.setFaceInfo(resourceQueryContext.getFaceInfo());
            queryContext.setProviderOrderNo(resourceQueryContext.getProviderOrderNo());
            return queryContext;
        }

        ProviderResourceQueryContext resourceQueryContext =
                initContext(context.getFaceId());
        queryContext.setFaceInfo(resourceQueryContext.getFaceInfo());
        queryContext.setProviderOrderNo(resourceQueryContext.getProviderOrderNo());
        return queryContext;
    }

    protected ProviderResourceQueryContext initContext(String faceId){
        FaceInfo faceInfo = faceRepository.getFaceInfoByFaceId(faceId);
        ProviderFaceInfo providerFaceInfo = providerFaceRepository.getByFaceId(faceId);

        ProviderResourceQueryContext queryContext = new ProviderResourceQueryContext();
        queryContext.setFaceId(faceId);
        queryContext.setFaceInfo(faceInfo);
        queryContext.setProviderOrderNo(providerFaceInfo.getOrderNo());
        return queryContext;
    }

    @Override
    public abstract String execute(ProviderResourceBaseContext context) ;

    @Override
    public abstract FaceResourceType resourceType() ;

    @Override
    public abstract String getProviderName() ;

    @Override
    public abstract String strategy() ;
}
