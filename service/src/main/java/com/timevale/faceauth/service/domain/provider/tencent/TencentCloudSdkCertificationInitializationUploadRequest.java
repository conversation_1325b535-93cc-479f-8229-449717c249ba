package com.timevale.faceauth.service.domain.provider.tencent;

import com.timevale.faceauth.service.core.support.ArgumentUtil;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.StringUtils;

import java.util.UUID;

/**
 * <AUTHOR>
 * @DATE 2024/8/15 20:06
 */
@Getter
public class TencentCloudSdkCertificationInitializationUploadRequest extends TencentCloudCertificationRequest {

    /**
     * 水纹照片类型
     */
    static final byte PHOTO_TYPE_WATER = (byte) 0x01;
    /**
     * 高清照片类型
     */
    static final byte PHOTO_TYPE_HIGH_DEFINITION = (byte) 0x02;

    /**
     * 用户 ID ，用户的唯一标识（不能带有特殊字符）
     */
    private final String userId;
    /**
     * 证件号码
     */
    private final String idNo;
    /**
     * 枚举值：不传该字段、传空、01为国内二代身份证（原有的证件服务）；
     * 02：港澳居民来往内地通行证、03：台湾居民来往内地通行证定、04：居国外的中国公
     * 民护照、05：外国人永久居留身份证
     * 腾讯云建议：国内二代身份证不用做修改，idType 不传；新增的出入境证件类型增加传
     * idType字段。
     */
    private final String idType;
    /**
     * 姓名
     */
    private final String name;

    /**
     * 姓名
     */
    private final String nonce = (UUID.randomUUID().toString().replace("-", "")); ;
    /**
     * 比对源照片类型 参数值为1：水纹正脸照 参数值为2：高清正脸照
     */
    private final Byte sourcePhotoType;
    /**
     * 比对源照片， 注意： 原始图片不能超过500KB，且必须为 JPG 或 PNG 格式 参数有值： 使用合作伙伴提供的比对源照片进行比对，必须注照片是正脸可信照片，照片质量由合作方保证
     * 参数为空： 根据身份证号+姓名使用权威数据源比对
     */
    @Setter
    private String sourcePhotoStr;

    private TencentCloudSdkCertificationInitializationUploadRequest(
            TencentCloudSdkCertificationInitializationUploadRequest.TencentCloudSdkCertificationInitializationUploadRequestBuilder builder) {
        super(builder);
        this.userId = builder.userId;
        this.idNo = builder.idNo;
        this.idType = builder.idType;
        this.name = builder.name;
        this.sourcePhotoStr = builder.sourcePhotoStr;
        this.sourcePhotoType = (StringUtils.isEmpty(builder.sourcePhotoStr) ? null : builder.sourcePhotoType);
    }




    static TencentCloudSdkCertificationInitializationUploadRequest.TencentCloudSdkCertificationInitializationUploadRequestBuilder createBuilder() {
        return (new TencentCloudSdkCertificationInitializationUploadRequest.TencentCloudSdkCertificationInitializationUploadRequestBuilder());
    }

    static class TencentCloudSdkCertificationInitializationUploadRequestBuilder
            extends TencentCloudCertificationRequest.TencentCloudCertificationRequestBuilder<
            TencentCloudSdkCertificationInitializationUploadRequest,
            TencentCloudSdkCertificationInitializationUploadRequest.TencentCloudSdkCertificationInitializationUploadRequestBuilder> {

        private String userId;
        private String idNo;
        private String idType;
        private String name;
        private String sourcePhotoStr;
        private Byte sourcePhotoType = null;

        @Override
        TencentCloudSdkCertificationInitializationUploadRequest build() {
            return (new TencentCloudSdkCertificationInitializationUploadRequest(this));
        }

        @Override
        protected TencentCloudSdkCertificationInitializationUploadRequest.TencentCloudSdkCertificationInitializationUploadRequestBuilder self() {
            return this;
        }

        TencentCloudSdkCertificationInitializationUploadRequest.TencentCloudSdkCertificationInitializationUploadRequestBuilder setUserId(String userId) {
            ArgumentUtil.throwIfEmptyArgument(userId, "userId");
            this.userId = userId;
            return this;
        }

        TencentCloudSdkCertificationInitializationUploadRequest.TencentCloudSdkCertificationInitializationUploadRequestBuilder setIdNo(String idNo) {
            ArgumentUtil.throwIfEmptyArgument(idNo, "idNo");
            this.idNo = idNo;
            return this;
        }

        TencentCloudSdkCertificationInitializationUploadRequest.TencentCloudSdkCertificationInitializationUploadRequestBuilder setIdType(String idType) {
            this.idType = idType;
            return this;
        }

        TencentCloudSdkCertificationInitializationUploadRequest.TencentCloudSdkCertificationInitializationUploadRequestBuilder setName(String name) {
            ArgumentUtil.throwIfEmptyArgument(name, "name");
            this.name = name;
            return this;
        }

        TencentCloudSdkCertificationInitializationUploadRequest.TencentCloudSdkCertificationInitializationUploadRequestBuilder setSourcePhotoStr(
                String sourcePhotoStr) {
            this.sourcePhotoStr = sourcePhotoStr;
            return this;
        }

        TencentCloudSdkCertificationInitializationUploadRequest.TencentCloudSdkCertificationInitializationUploadRequestBuilder setSourcePhotoType(
                byte sourcePhotoType) {
            ArgumentUtil.throwIfNotTrue(sourcePhotoType > (byte) 0x00, "sourcePhotoType");
            this.sourcePhotoType = sourcePhotoType;
            return this;
        }

    }
}