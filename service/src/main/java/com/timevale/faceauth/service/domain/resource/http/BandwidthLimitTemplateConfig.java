package com.timevale.faceauth.service.domain.resource.http;

import com.timevale.faceauth.service.domain.resource.ResourceSystemHolder;
import com.timevale.faceauth.service.domain.support.HttpTemplateConfig;

/**
 * 调用网关http配置
 * <AUTHOR>
 * @since 2025/1/1 20:47
 */
public class BandwidthLimitTemplateConfig implements HttpTemplateConfig {


    private ResourceSystemHolder systemHolder;
    public BandwidthLimitTemplateConfig(ResourceSystemHolder systemHolder) {
        this.systemHolder = systemHolder;
    }

    @Override
    public Integer connectionTimeout() {
        return systemHolder.getResourceHttConfigConnectionTimeout();
    }

    @Override
    public Integer soTimeout() {
        return systemHolder.getResourceHttConfigSoTimeout();
    }

    @Override
    public Integer connectionsPerHost() {
        return systemHolder.getResourceHttConfigConnectionsPerHost();
    }

    @Override
    public Integer connectionsTotal() {
        return systemHolder.getResourceHttConfigConnectionsTotal();
    }


    @Override
    public boolean retryEnabled() {
        return Boolean.TRUE;
    }
}
