package com.timevale.faceauth.service.domain.controll;

import com.timevale.mandarin.base.util.StringUtils;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @DATE 2025/7/4 17:49
 */
@Getter
public enum FaceResourceControlStatusEnum {

    REVOKE_AGREE("REVOKE_AGREE","撤回"),
    DISENABLE("DISENABLE","失效当前记录"),
    REPEAT_AGREE("REPEAT_AGREE","再次同意刷脸")
    ;

    FaceResourceControlStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private String code;
    private String desc;

    public static String ofDesc(String status) {
        FaceResourceControlStatusEnum of = of(status);
        return Objects.isNull(of) ? null : of.getDesc();
    }

    public static FaceResourceControlStatusEnum of(String status) {
        for (FaceResourceControlStatusEnum value : values()) {
            if (StringUtils.equals(status, value.getCode())) {
                return value;
            }
        }
        return null;
    }
}
