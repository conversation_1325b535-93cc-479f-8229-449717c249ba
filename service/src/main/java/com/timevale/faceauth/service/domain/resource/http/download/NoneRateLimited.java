package com.timevale.faceauth.service.domain.resource.http.download;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

@Slf4j
public class NoneRateLimited implements BandwidthLimiter{


    private int buffSize = BUFFER_SIZE_DEFAULT;
    private InputStream input;
    private OutputStream output;

    @Override
    public String name() {
        return NAME_DEFAULT;
    }

    @Override
    public void init(long maxRate, int bufferSize) {
        if(bufferSize > 1){
            this.buffSize = bufferSize;
        }
    }

    @Override
    public void read(InputStream input, OutputStream output)throws IOException {
            check();

            byte[] buffer = new byte[this.buffSize];
            int bytesRead;
            while ((bytesRead = input.read(buffer)) != -1) {
                output.write(buffer,0,bytesRead);
            }

            this.input = input;
            this.output = output;
    }

    @Override
    public void after() {
        if(input != null ){
            try {
                input.close();
            } catch (IOException e) {
                log.error(this.name()+" input close fail ",e);
            }
        }

        if(output != null){
            try {
                output.close();
            } catch (IOException e) {
                log.error(this.name()+" output close fail ",e);
            }
        }
    }

    private void check(){

    }
}
