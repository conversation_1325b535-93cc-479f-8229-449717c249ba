package com.timevale.faceauth.service.domain.controll;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @DATE 2025/7/4 16:17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FaceUserResourceControlCheckDTO extends ToString {

//    private String name;
//    private String idNo;
//    private String certType;
//    private String appId;

    private String faceId;


}
