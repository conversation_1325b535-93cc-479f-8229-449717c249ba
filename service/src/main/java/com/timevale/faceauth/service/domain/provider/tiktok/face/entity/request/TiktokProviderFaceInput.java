package com.timevale.faceauth.service.domain.provider.tiktok.face.entity.request;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @DATE 2024/5/14 19:24
 */
@Data
@Accessors(chain = true)
public class TiktokProviderFaceInput extends ToString {

    /**
     * 用户身份姓名
     */
    private String name;

    /**
     * 用户身份证号
     */
    private String idNo;


    /**
     * 抖音主体ID
     */
    private String aid;

    /**
     * 抖音的 openId
     */
    private String openId;


}
