package com.timevale.faceauth.service.domain.resource;

import com.alibaba.fastjson.JSON;
import com.timevale.faceauth.service.domain.resource.http.ConstantsUtil;
import com.timevale.faceauth.service.utils.SpringUtil;
import com.timevale.framework.puppeteer.model.ConfigChange;
import com.timevale.framework.puppeteer.model.ConfigChangeEvent;
import com.timevale.framework.puppeteer.spring.annotation.PuppeteerConfigChangeListener;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 配置中心
 * <AUTHOR>  2019-06-14 17:07:00
 * @version 1.0.0
 *
 */
@Getter
@Slf4j
@Component
public class ResourceSystemHolder {

  /**
   * ## 调用网关http配置
   * ## connectiontimeout = 请求超时时间
   * ## sotimeout = 读取超时时间
   * ## connections.perhost = 单个ip最大连接数
   * ## connections.total = 总连接数
   * ## connection-manager.timeout = 连接池获取请求等待最大时间
   */
  @Value("${resource.property.http.config.connectiontimeout:10000}")
  private Integer resourceHttConfigConnectionTimeout;
  @Value("${resource.property.http.config.sotimeout:10000}")
  private Integer resourceHttConfigSoTimeout;
  @Value("${resource.property.http.config.connections.perhost:80}")
  private Integer resourceHttConfigConnectionsPerHost;
  @Value("${resource.property.http.config.connections.total:300}")
  private Integer resourceHttConfigConnectionsTotal;
  @Value("${resource.property.http.config.connection-manager.timeout:10000}")
  private Long resourceHttConfigConnectionManagerTimeout;

  /**
   * 每秒下载速率 51200 kb/s  = 50m/s ,是 8192的倍数
   * @see ConstantsUtil#KB
   */
  @Value("${resource.property.http.maxLate:512}")
  private Integer resourceHttConfigMaxLate;

  @Value("${resource.property.http.lateBufferSize:8192}")
  private Integer resourceHttConfigLateBufferSize;

  @Value("${resource.property.http.lateStrategy:guavaLimited}")
  private String resourceHttConfigLateStrategy;

  @Getter
  @Value("${resource.property.download.mq.delay.level:1}")
  private int resourceDownloadMqDelayLevel;

  @Getter
  @Value("${resource.property.download.mq.topic:face_resource_download_task}")
  private String resourceDownloadMqTopic;



  private static Map<String, ResourceStrategyModel> resourceStrategyMap = new HashMap<>();
  /**
   * 资源下载策略
   */
  @Value("${resource.property.download.strategy}")
  private void setResourceStrategy(String text) {


    if (StringUtils.isBlank(text)) {
      resourceStrategyMap.clear();
      return;
    }
    try{
      List<ResourceStrategyModel> list = JSON.parseArray(text, ResourceStrategyModel.class);
      Map<String, ResourceStrategyModel> resourceStrategyTempMap = new HashMap<>();
      list.forEach(x->{
        String key = x.getProvider()+x.getResourceType();
        resourceStrategyTempMap.put(key, x);
      });

      resourceStrategyMap = resourceStrategyTempMap;
    }catch (Exception e){
      log.error("load ResourceStrategy config   fail",e);
    }
  }


  public  String getResourceStrategy(String provider, FaceResourceType resourceType) {
    String key = provider+resourceType.getCode();
    ResourceStrategyModel strategyModel = resourceStrategyMap.get(key);
    if(strategyModel != null){
       return strategyModel.getStrategy();
    }
    return ResourceStrategyEnum.DEFAULT.getCode();
  }

  private static volatile ResourceSystemHolder DEFAULT_PRO;

  public static ResourceSystemHolder getInstance() {
    if(DEFAULT_PRO == null){
      synchronized (ResourceSystemHolder.class){
        if (DEFAULT_PRO == null) {
          DEFAULT_PRO = SpringUtil.getBean(ResourceSystemHolder.class);
        }
      }
    }

    return DEFAULT_PRO;
  }





  /**
   * 监听配置变动
   *
   * @param changeEvent
   */
  @PuppeteerConfigChangeListener
  private void configChangeListener(ConfigChangeEvent changeEvent) {


    ConfigChange configChange =
            changeEvent.getChange("resource.property.http.config.connectiontimeout");
    if (configChange != null) {
      String val  = configChange.getNewValue();
      resourceHttConfigConnectionTimeout = Integer.valueOf(val);
    }

    configChange =
            changeEvent.getChange("resource.property.http.config.sotimeout");
    if (configChange != null) {
      String val  = configChange.getNewValue();
      resourceHttConfigSoTimeout = Integer.valueOf(val);
    }

    configChange =
            changeEvent.getChange("resource.property.http.config.connections.perhost");
    if (configChange != null) {
      String val  = configChange.getNewValue();
      resourceHttConfigConnectionsPerHost = Integer.valueOf(val);
    }

    configChange =
            changeEvent.getChange("resource.property.http.config.connections.total");
    if (configChange != null) {
      String val  = configChange.getNewValue();
      resourceHttConfigConnectionsTotal = Integer.valueOf(val);
    }

    configChange =
            changeEvent.getChange("resource.property.http.config.connection-manager.timeout");
    if (configChange != null) {
      String val  = configChange.getNewValue();
      resourceHttConfigConnectionManagerTimeout = Long.valueOf(val);
    }

    configChange =
            changeEvent.getChange("resource.property.http.maxLate");
    if (configChange != null) {
      String val  = configChange.getNewValue();
      resourceHttConfigMaxLate = Integer.valueOf(val);
    }

    configChange =
            changeEvent.getChange("resource.property.http.lateBufferSize");
    if (configChange != null) {
      String val  = configChange.getNewValue();
      resourceHttConfigLateBufferSize = Integer.valueOf(val);
    }

    configChange =
            changeEvent.getChange("resource.property.http.lateStrategy");
    if (configChange != null) {
      String val  = configChange.getNewValue();
      resourceHttConfigLateStrategy = val;
    }

  }


}
