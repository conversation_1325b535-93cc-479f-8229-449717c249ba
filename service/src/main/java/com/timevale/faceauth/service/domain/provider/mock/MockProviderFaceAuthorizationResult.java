package com.timevale.faceauth.service.domain.provider.mock;

import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationResult;
import com.timevale.faceauth.service.exception.ProviderException;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/16 16
 */
class MockProviderFaceAuthorizationResult implements ProviderFaceAuthorizationResult {

  private final String provider;
  private final String faceId;
  private final String content;
  private final long timestamp;

  MockProviderFaceAuthorizationResult(String provider, String faceId, String content) {
    this.provider = provider;
    this.faceId = faceId;
    this.content = content;
    this.timestamp = System.currentTimeMillis();
  }

  @Override
  public String getProvider() {
    return provider;
  }

  @Override
  public String getFaceId() {
    return faceId;
  }

  @Override
  public String getResultContent() {
    return content;
  }

  @Override
  public boolean isCompleted() {
    return true;
  }

  @Override
  public boolean isSuccess() {
    return true;
  }

  @Override
  public ProviderException getError() {
    return null;
  }

  @Override
  public long getTimestamp() {
    return timestamp;
  }
}
