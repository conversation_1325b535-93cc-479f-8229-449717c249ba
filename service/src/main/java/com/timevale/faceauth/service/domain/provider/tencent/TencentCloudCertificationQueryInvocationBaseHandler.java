package com.timevale.faceauth.service.domain.provider.tencent;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.domain.repository.ProviderFaceInfo;

/**
 * <AUTHOR>
 */
public abstract class TencentCloudCertificationQueryInvocationBaseHandler extends TencentCloudFaceInvocationErrorHandler  implements TencentCloudCertificationSignatureResolver<TencentCloudCertificationQueryRequest> {
    @Override
    public String resolveSignature(TencentCloudCertificationQueryRequest request)
            throws FaceException {
        return TencentCloudUtil.signature(
                request.getAppId(),
                request.getNonce(),
                request.getOrderNo(),
                request.getVersion(),
                request.obtainTicket());
    }

    public TencentCloudCertificationQueryRequest prepareRequestQueryBoby(
            String faceId, String providerOrderNo, TencentWebAppIdVersion appIdVersion) throws FaceException {
        TencentCloudCertificationQueryRequest request =
                TencentCloudCertificationQueryRequest.createBuilder()
                        .setAppId(appIdVersion.getAccessHolder().getWebAppId())
                        .setOrderNo(providerOrderNo)
                        .setSignatureResolver(this)
                        .setTicket(TencentCloudUtil.getTicketCache(faceId, appIdVersion))
                        .build();
        request.signatureRequest();
        return request;
    }
}
