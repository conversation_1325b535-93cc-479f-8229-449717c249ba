package com.timevale.faceauth.service.domain.resource;

import com.timevale.faceauth.service.domain.provider.query.ByteDanceApiInvocationQueryHandler;
import com.timevale.faceauth.service.domain.provider.query.ProviderResponse;
import com.timevale.faceauth.service.domain.resource.http.download.BandwidthLimitHttpRequestResolver;
import com.timevale.faceauth.service.domain.resource.store.PhotoResourceStore;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.timevale.faceauth.service.domain.provider.ConfigurableProviderService.PROVIDER_BYTEDANCE;

/**
 * 供应商资源加载器
 * <AUTHOR>
 * @since 2025/12/23 14:35
 */
@Slf4j
@Component
public class ProviderBytedanceRemotePhotoSyncResourceLoader extends ProviderBytedanceRemoteResourceAbstractService {

    @Autowired
    private BandwidthLimitHttpRequestResolver requestResolver;
    @Autowired
    private ByteDanceApiInvocationQueryHandler invocationQueryHandler;
    @Autowired
    private PhotoResourceStore resourceStore;

    @Override
    public String getProviderName() {
        return PROVIDER_BYTEDANCE;
    }



    @Override
    public String execute(ProviderResourceBaseContext context) {
        ProviderQueryContext queryContext = super.revokerContext(context);
        if(queryContext == null){
            super.logExecuteContextNull();
             return null;
        }


        return call(queryContext);
    }

    private String call(ProviderQueryContext context) {

        try {
            ProviderResponse response = invocationQueryHandler.call(requestResolver, context);
            resourceStore.done(context, response);
            return response.getPhotoKey();
        }catch (Exception e){
            log.error("call bytedance provider resource fail", e);
        }


        return null;
    }


    @Override
    public String strategy() {
        return ResourceStrategyEnum.SYNC.getCode();
    }


    @Override
    public FaceResourceType resourceType() {
        return FaceResourceType.PHOTO;
    }
}
