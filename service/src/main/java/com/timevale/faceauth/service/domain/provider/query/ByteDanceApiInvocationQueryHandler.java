package com.timevale.faceauth.service.domain.provider.query;

import com.alibaba.fastjson.JSON;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.domain.provider.ConfigurableProviderService;
import com.timevale.faceauth.service.domain.provider.bytedance.ByteDanceApiInvocationQueryRequestConverter;
import com.timevale.faceauth.service.domain.provider.bytedance.ByteDanceFaceResultStatus;
import com.timevale.faceauth.service.domain.provider.bytedance.STSServiceExtend;
import com.timevale.faceauth.service.domain.provider.bytedance.VisualServiceExtend;
import com.timevale.faceauth.service.domain.provider.bytedance.domain.FaceQueryExtendResponse;
import com.timevale.faceauth.service.domain.provider.bytedance.domain.FaceQueryRequest;
import com.timevale.faceauth.service.domain.provider.bytedance.domain.FaceQueryResponse;
import com.timevale.faceauth.service.domain.provider.support.HttpRequestResolver;
import com.timevale.faceauth.service.domain.resource.ProviderQueryContext;
import com.timevale.faceauth.service.utils.providerLog.ProviderLogService;
import com.timevale.faceauth.service.utils.providerLog.result.ByteDanceQueryResult;
import com.timevale.mandarin.base.util.StringUtils;
import com.volcengine.model.Credentials;
import com.volcengine.model.request.AssumeRoleRequest;
import com.volcengine.model.response.AssumeRoleResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 字节 API 调用基本处理
 *
 * <AUTHOR>
 * @since 2023/2/21
 */
@Slf4j
@Component
public class ByteDanceApiInvocationQueryHandler implements ProviderApiInvocationQueryHandler<FaceQueryExtendResponse>{

    public static final String BASE_URL = "https://h5.kych5.com";
    public static final String ROLE_SESSION_NAME = "just_for_test";
    /**
     * 字节刷脸基础班 r
     */
    public static final String QUERY_REQ_KEY = "cert_verify_query";
    /**
     * 字节增强版  r
     */
    public static final String QUERY_PRO_REQ_KEY = "cert_pro_verify_query";
    @Autowired
    private ProviderLogService providerLogService;

    @Autowired
    private ByteDanceApiInvocationQueryRequestConverter queryApiRequestConverter;
    @Autowired
    private ConfigurableProperties configurableProperties;


    @Override
    public ProviderResponse<FaceQueryExtendResponse>  call(HttpRequestResolver requestResolver, ProviderQueryContext context)  {
        String faceId = context.getFaceId();
        long startTime = System.currentTimeMillis();
        try {

            STSServiceExtend stsService = STSServiceExtend.getInstance();
            stsService.setAccessKey(configurableProperties.getByteDanceAccessKey());
            stsService.setSecretKey(configurableProperties.getByteDanceSecretKey());

            AssumeRoleRequest request = new AssumeRoleRequest();
            request.setRoleSessionName(ROLE_SESSION_NAME);
            request.setDurationSeconds(3600);
            request.setRoleTrn(configurableProperties.getByteDanceRoleTrn());
//            AssumeRoleResponse resp = stsService.assumeRole(faceId, requestResolver, request);
            AssumeRoleResponse resp = stsService.assumeRole(request);
            log.info("ByteDance.queryInvoke assumeRole faceId={}, requestId={} clientCost= {}  ms",
                    faceId,
                    resp.getResponseMetadata().getRequestId(),
                    (System.currentTimeMillis() - startTime));

            VisualServiceExtend serviceExtend = VisualServiceExtend.getInstance();
//            serviceExtend.setAccessKey(resp.getResult().getCredentials().getAccessKeyId());
//            serviceExtend.setSecretKey(resp.getResult().getCredentials().getSecretAccessKey());
//            serviceExtend.setSessionToken(resp.getResult().getCredentials().getSessionToken());

            Credentials credentials = new Credentials();
            credentials.setAccessKeyID(resp.getResult().getCredentials().getAccessKeyId());
            credentials.setSecretAccessKey(resp.getResult().getCredentials().getSecretAccessKey());
            credentials.setSessionToken(resp.getResult().getCredentials().getSessionToken());
            credentials.setService(serviceExtend.getServiceInfo().getCredentials().getService());
            credentials.setRegion(serviceExtend.getServiceInfo().getCredentials().getRegion());

            FaceQueryRequest queryRequest = new FaceQueryRequest();
            queryRequest.setByted_token(context.getProviderOrderNo());
            // 2025/4/3
            if (StringUtils.equals(context.getFaceInfo().getProviderApiVersion(), ConfigurableProviderService.PROVER_TOW_V)) {
                queryRequest.setReq_key(QUERY_PRO_REQ_KEY);
            }else {
                queryRequest.setReq_key(QUERY_REQ_KEY);
            }
            queryRequest = queryApiRequestConverter.work(queryRequest, context);
//            FaceQueryResponse response = serviceExtend.query(queryRequest);
            FaceQueryExtendResponse response =
                    serviceExtend.queryPlus(faceId, requestResolver, queryRequest, credentials);


            long cost = System.currentTimeMillis() - startTime;

            providerLogService.logByteDanceQuery(
                    new ByteDanceQueryResult(faceId, cost),
                    queryRequest,
                    response);

            if (cost > 3000) {
                log.info("ByteDance.queryInvoke much time faceId={},  clientCost: {} ms", faceId, cost);
            }

            log.info("ByteDance.queryInvoke query faceId = {}, requestId={} providerCost : {} ms,  clientCost : {} ms",
                    faceId, response.getRequestId(), response.getTimeElapsed(), cost);

            if (log.isDebugEnabled()) {
                log.debug("ByteDance.queryInvoke query faceId={}, response:{} ",
                        faceId, JSON.toJSONString(response));
            }

            ByteDanceFaceResultStatus faceResultStatus = deduceActualProviderCode(response);
            response.setFaceResultStatus(faceResultStatus);
            boolean completed = faceResultStatus.isCompleted();
            Boolean success = response.getData().getResult();

            String photoBase64 = null;
            String videoBase64 = null;
            if (null != response.getData().getImages()) {
                photoBase64 = response.getData().getImages().getImageEnv();
            }
            if (null != response.getData().getVideo()) {
                videoBase64 = response.getData().getVideo();
            }
            return ProviderResponse.<FaceQueryExtendResponse>builder()
                    .completed(completed)
                    .success(success)
                    .videoBase64(videoBase64)
                    .photoBase64(photoBase64)
                    .originData(response)
                    .build();
        }catch (Exception e){
            providerLogService.logByteDanceQueryWithException(
                    new ByteDanceQueryResult(faceId, System.currentTimeMillis() - startTime),
                    null,
                    e);
            throw FaceException.valueOf(FaceStatusCode.PROVIDER_ERROR_API_RESULT_SYNTAX);
        }
    }



    private ByteDanceFaceResultStatus deduceActualProviderCode(FaceQueryResponse response) {
        if (null == response.getData().getVerifyAlgorithmBaseResp()) {
            if (!response.getData().getResult() && 10000 == response.getCode()){
                return ByteDanceFaceResultStatus.UN_COMPLETED;
            }
            return ByteDanceFaceResultStatus.CODE_210311;
        }

        try {
            int code = response.getData().getVerifyAlgorithmBaseResp().getStatusCode();
            return ByteDanceFaceResultStatus.getStatusByCode(code);
        } catch (NumberFormatException e) {
        }
        return ByteDanceFaceResultStatus.SYSTEM_ERROR;
    }
}
