package com.timevale.faceauth.service.domain.resource.http.download;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.event.FaceEventPublisher;
import com.timevale.faceauth.service.domain.event.ProviderApiFailEvent;
import com.timevale.faceauth.service.domain.provider.support.HttpRequestResolver;
import com.timevale.faceauth.service.domain.resource.ResourceSystemHolder;
import com.timevale.faceauth.service.domain.resource.http.ConstantsUtil;
import com.timevale.faceauth.service.domain.resource.http.ResourceHttpUtil;
import com.timevale.faceauth.service.utils.providerLog.AbstractProviderLogResultResolver;
import com.timevale.faceauth.service.utils.providerLog.LogConsumer;
import com.timevale.mandarin.base.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URI;
import java.util.Objects;

/**
 *
 * 宽带限制http client
 * <AUTHOR>
 */
@Slf4j
@Component
public class BandwidthLimitHttpRequestResolver implements HttpRequestResolver
{
    @Override
    public URI resolveURI(String uriString) throws FaceException {
        try {
            return URI.create(uriString.trim());
        } catch (Exception cause) {
            throw FaceException.valueOf(
                    FaceStatusCode.RESOURCE_ERROR_HTTP_INITIALIZATION,
                    "Error uri[" + uriString + "] .",
                    cause);
        }
    }

    @Override
    public <T> ResponseEntity<T> resolveResponse(String faceId, String provider, RequestEntity requestEntity, Class<T> responseType) throws FaceException {
        RestTemplate restTemplate = ResourceHttpUtil.getRestTemplate();
        ResponseEntity<Resource> responseEntity;
        long beginTime = System.currentTimeMillis();
        try {
            responseEntity = restTemplate.exchange(requestEntity, Resource.class);
        } catch (Exception cause) {
            ProviderApiFailEvent event =
                    (new ProviderApiFailEvent(faceId, requestEntity.getUrl().getPath(), provider, cause));
            FaceEventPublisher.publishEvent(event);
            throw FaceException.valueOf(
                    FaceStatusCode.PROVIDER_FAILED_API,
                    "Error invoke api[" + requestEntity.getUrl().toString() + "] .",
                    cause);
        }
        if (HttpServletResponse.SC_OK != responseEntity.getStatusCodeValue()) {
            throw FaceException.valueOf(
                    FaceStatusCode.PROVIDER_FAILED_API,
                    "Error response["
                            + responseEntity.getStatusCodeValue()
                            + ","
                            + responseEntity.getBody()
                            + "] on api["
                            + requestEntity.getUrl().toString()
                            + "] .");
        }


        try {
            StringOutputStream outputStream = new StringOutputStream();
            InputStream  originalResponseStream = responseEntity.getBody().getInputStream();
            Integer maxLate = ResourceSystemHolder.getInstance().getResourceHttConfigMaxLate();
            Integer bufferSize = ResourceSystemHolder.getInstance().getResourceHttConfigLateBufferSize();

            BandwidthLimiter bandwidthLimiter =
                    BandwidthLimitedDispatch.select(ResourceSystemHolder.getInstance().getResourceHttConfigLateStrategy());
            bandwidthLimiter.init(maxLate * ConstantsUtil.KB, bufferSize);
            bandwidthLimiter.read(originalResponseStream, outputStream);
            String body =  outputStream.toContent();
            bandwidthLimiter.after();
            log.info("BandwidthLimitHttp download size = {} use {} ms", body.length(),System.currentTimeMillis() - beginTime);
            if(Objects.equals(responseType.getName(), String.class.getName())){
                return (ResponseEntity<T>) ResponseEntity.ok(body);
            }
            return  ResponseEntity.ok(JsonUtils.json2pojo(body, responseType));
        }catch (Exception e){
            log.error("BandwidthLimitHttp execute fail", e);
        }
        throw FaceException.valueOf(
                FaceStatusCode.ERR_INNER,
                "Error response["
                        + responseEntity.getStatusCodeValue()
                        + ","
                        + responseEntity.getBody()
                        + "] on api["
                        + requestEntity.getUrl().toString()
                        + "] .");
    }

    @Override
    public <T> ResponseEntity<T> resolveResponse(String faceId, String provider, RequestEntity requestEntity, Class<T> responseType, AbstractProviderLogResultResolver providerLogResultResolver, LogConsumer<AbstractProviderLogResultResolver, RequestEntity<?>, ResponseEntity<String>> logConsumer, LogConsumer<AbstractProviderLogResultResolver, RequestEntity<?>, Exception> logExceptionConsumer) throws FaceException {
        return resolveResponse(faceId, provider, requestEntity, responseType);
    }


}
