package com.timevale.faceauth.service.utils;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.groups.Default;
import java.util.Collection;
import java.util.Iterator;
import java.util.Objects;
import java.util.Set;

/**
 * 功能说明：
 */
public class FaceAuthValidationUtils {

    private static Validator validator = Validation.buildDefaultValidatorFactory().getValidator();


    public static <T> void validateBean(T obj) {
        Set<ConstraintViolation<T>> set = validator.validate(obj, new Class[]{Default.class});
        Iterator var2 = set.iterator();
        if (var2.hasNext()) {
            ConstraintViolation<T> bean = (ConstraintViolation)var2.next();
            throw FaceException.valueOf(FaceStatusCode.ILLEGAL_ARGUMENT,bean.getMessage());
        }
    }

    public static void notBlank(String param, RuntimeException e) {
        if (StringUtils.isBlank(param)) {
            throw e;
        }
    }


    public static void assertTrue(boolean success, RuntimeException e) {
        if (!success) {
            throw e;
        }
    }

    public static void assertFalse(boolean success, RuntimeException e) {
        if (success) {
            throw e;
        }
    }

    public static void assertEqual(Object param, Object param1, RuntimeException e) {
        if (!Objects.equals(param, param1)) {
            throw e;
        }
    }

    public static void assertStringEqualIgnoreCase(String param, String param1, RuntimeException e) {
        if (!StringUtils.equalsIgnoreCase(param, param1)) {
            throw e;
        }
    }

    public static void notNull(Object param, RuntimeException e) {
        if (Objects.isNull(param)) {
            throw e;
        }
    }

    public static void notEmpty(Collection param, RuntimeException e) {
        if (CollectionUtils.isEmpty(param)) {
            throw e;
        }
    }

    public static void empty(Collection param, RuntimeException e) {
        if (CollectionUtils.isNotEmpty(param)) {
            throw e;
        }
    }
}