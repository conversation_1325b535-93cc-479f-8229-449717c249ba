package com.timevale.faceauth.service.domain.provider.tiktok.face.util;


import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.SecureRandom;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

import static com.alibaba.fastjson.util.IOUtils.decodeBase64;

/**
 * https://bytedance.larkoffice.com/wiki/KiHBw8Pc1ii3cWkk81VcQWSYn5f?sheet=mramcv
 */
//抖音身份认证
@Slf4j
public class TiktokIdentityAuthUtil {

    /**
     * 随机生成32字节长度的对称密钥key以及16字节的初始向量iv
     *
     * @param num
     * @return
     */
    public static byte[] randomKeyOrIv(int num) {
        byte[] key = new byte[num];
        SecureRandom randomKey = new SecureRandom();
        randomKey.nextBytes(key);
        return key;
    }

    //第二步RSA加密对称密钥key和初始向量iv
    public static byte[] encryptPublicKey(byte[] binaryData, String publicKey) {
        try {
            byte[] keyBytes = decodeBase64(publicKey);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
            // 获取RSA算法实例
            KeyFactory keyFactory = null;
            keyFactory = KeyFactory.getInstance("RSA");

            Key pubKey = keyFactory.generatePublic(keySpec);
            // 初始化加密器
            Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
            cipher.init(Cipher.ENCRYPT_MODE, pubKey);
            return cipher.doFinal(binaryData);
        } catch (Exception e) {
            log.error("RSA加密对称密钥 加密失败  请检查是否更改抖音秘钥相关配置 ", e);
            throw FaceException.valueOf(FaceStatusCode.PROVIDER_ERROR_SIGNATURE);
        }

    }

    /**
     * RSA解密对称密钥key和初始向量iv
     *
     * @param content    需解密内容
     * @param privateKey 私钥
     * @return
     */
    public static String decryptByPrivateKey(byte[] content, String privateKey) {
        String decryptContent = null;
        try {
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(Base64.getDecoder().decode(privateKey));
            PrivateKey priKey = KeyFactory.getInstance("RSA").generatePrivate(keySpec);
            //RSA解密
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.DECRYPT_MODE, priKey);
            byte[] encryptedData = content;
            int inputLen = encryptedData.length;
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            int offSet = 0;
            byte[] cache;
            int i = 0;
            // 对数据分段解密
            while (inputLen - offSet > 0) {
                if (inputLen - offSet > 256) {
                    cache = cipher.doFinal(encryptedData, offSet, 256);
                } else {
                    cache = cipher.doFinal(encryptedData, offSet, inputLen - offSet);
                }
                out.write(cache, 0, cache.length);
                i++;
                offSet = i * 256;
            }
            out.close();
            decryptContent = out.toString();
        } catch (Exception e) {
            log.error("decryptByPrivateKey error",e);
        }
        return decryptContent;
    }

    //--------------第三步CBC加密敏感数据-------------------------
    public static String encryptCBC(String message, byte[] keyByte, byte[] ivByte) {
        try {
            byte[] content = message.getBytes(StandardCharsets.UTF_8);
            keyByte = fillKey(keyByte);
            SecretKeySpec keySpec = new SecretKeySpec(keyByte, "AES");
            ivByte = fillIv(ivByte);
            IvParameterSpec ivSpec = new IvParameterSpec(ivByte);
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
            byte[] data = cipher.doFinal(content);
            return Base64.getEncoder().encodeToString(data);
        } catch (Exception e) {
        }
        return null;
    }

    //--------------第三步CBC解密敏感数据-------------------------
    public static String decryptCBC(String message, byte[] keyByte, byte[] ivByte) {
        try {
            keyByte = fillKey(keyByte);
            SecretKeySpec keySpec = new SecretKeySpec(keyByte, "AES");
            ivByte = fillIv(ivByte);
            IvParameterSpec ivSpec = new IvParameterSpec(ivByte);
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
            byte[] data = cipher.doFinal(Base64.getDecoder().decode(message));
            return new String(data);
        } catch (Exception e) {
        }
        return null;
    }


    /**
     * 填充key
     * key的字节长度只能是16位、24位、32位。
     * key长度不足时，这里将以0x00填充，超出部分将被忽略。
     *
     * @return 填充后的key
     */
    public static byte[] fillKey(byte[] keyByte) {
        int length = keyByte.length;
        int len;
        if (length == 16 || length == 24 || length == 32) {
            return keyByte;
        } else if (length < 16) {
            len = 16;
        } else if (length < 24) {
            len = 24;
        } else {
            len = 32;
        }
        byte[] newKeyByte = new byte[len];
        System.arraycopy(keyByte, 0, newKeyByte, 0, length < len ? length : len);
        return newKeyByte;
    }

    /**
     * 填充iv
     * iv的字节长度只能是16位。
     * iv长度不足时，这里将以0x00填充，超出部分将被忽略。
     *
     * @return 填充后的iv
     */
    public static byte[] fillIv(byte[] ivByte) {
        int length = ivByte.length;
        int len;
        if (length == 16) {
            return ivByte;
        } else {
            len = 16;
        }
        byte[] newIvByte = new byte[len];
        System.arraycopy(ivByte, 0, newIvByte, 0, length < len ? length : len);
        return newIvByte;
    }
}

