package com.timevale.faceauth.service.domain.support;

import com.timevale.faceauth.service.domain.ConfigurableProperties;

/**
 * 调用网关http配置
 * <AUTHOR>
 * @since 2025/1/1 20:47
 */
public class RestTemplateConfig implements HttpTemplateConfig{


    private ConfigurableProperties properties;
    public RestTemplateConfig(ConfigurableProperties properties) {
        this.properties = properties;
    }

    @Override
    public Integer connectionTimeout() {
        return properties.getProviderApiClientConnTimeoutMillis();
    }

    @Override
    public Integer soTimeout() {
        return properties.getProviderApiClientReadTimeoutMillis();
    }

    @Override
    public Integer connectionsPerHost() {
        return properties.getProviderApiClientMaxRequestsPerHost();
    }

    @Override
    public Integer connectionsTotal() {
        return properties.getProviderApiClientMaxRequests();
    }



    @Override
    public int retryCount() {
        return properties.getProviderApiClientAutoRetryLimit();
    }

    @Override
    public boolean retryEnabled() {
        return Boolean.TRUE;
    }
}
