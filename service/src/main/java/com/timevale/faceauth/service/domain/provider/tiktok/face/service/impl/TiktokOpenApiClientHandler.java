package com.timevale.faceauth.service.domain.provider.tiktok.face.service.impl;

import com.alibaba.fastjson.JSON;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.provider.ConfigurableProviderService;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationResult;
import com.timevale.faceauth.service.domain.provider.support.FaceBase64Resources;
import com.timevale.faceauth.service.domain.provider.support.FaceResourcesInvocationHandler;
import com.timevale.faceauth.service.domain.provider.support.RestTemplateRequestResolver;
import com.timevale.faceauth.service.domain.provider.tiktok.face.domain.TiktokAppSecretDomain;
import com.timevale.faceauth.service.domain.provider.tiktok.face.domain.TiktokFaceAuthorizationResult;
import com.timevale.faceauth.service.domain.provider.tiktok.face.domain.TiktokFaceResultStatusDomain;
import com.timevale.faceauth.service.domain.provider.tiktok.face.entity.request.TiktokProviderFaceInput;
import com.timevale.faceauth.service.domain.provider.tiktok.face.entity.response.TiktokGetAccessTokenResponse;
import com.timevale.faceauth.service.domain.provider.tiktok.face.entity.response.TiktokGetDetailResponse;
import com.timevale.faceauth.service.domain.provider.tiktok.face.entity.response.TiktokGetOpenIdResponse;
import com.timevale.faceauth.service.domain.provider.tiktok.face.entity.response.TiktokGetTicketResponse;
import com.timevale.faceauth.service.domain.provider.tiktok.face.entity.response.TiktokGetTicketResultResponse;
import com.timevale.faceauth.service.domain.provider.tiktok.face.service.TiktokOpenApiClient;
import com.timevale.faceauth.service.domain.provider.tiktok.face.util.TiktokConfig;
import com.timevale.faceauth.service.domain.provider.tiktok.face.util.TiktokIdentityAuthUtil;
import com.timevale.faceauth.service.domain.repository.FaceInfo;
import com.timevale.faceauth.service.domain.repository.FaceOSSResources;
import com.timevale.faceauth.service.domain.repository.ProviderFaceInfo;
import com.timevale.faceauth.service.exception.ProviderException;
import com.timevale.faceauth.service.utils.HttpUtilPlus;
import com.timevale.faceauth.service.utils.cache.MissesLoadBackString;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.MapUtils;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import java.util.Base64;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @DATE 2024/5/13 17:27
 */
@Service
@Slf4j
public class TiktokOpenApiClientHandler implements TiktokOpenApiClient {

    @Autowired
    private TiktokConfig tiktokConfig;

    @Autowired
    private FaceResourcesInvocationHandler resourcesInvocationHandler;


    /**
     * API 获取 ACCESS_TOKEN
     * <p>
     * https://developer.open-douyin.com/docs/resource/zh-CN/mini-app/develop/server/interface-request-credential/non-user-authorization/get-access-token
     * <p>
     * 正式地址：https://developer.toutiao.com/api/apps/v2/token
     * 沙盒地址：https://open-sandbox.douyin.com/api/apps/v2/token
     */
    public static String API_GET_ACCESS_TOKEN = "https://developer.toutiao.com/api/apps/v2/token";

    /**
     * API 获取 OPEN_ID
     * <p>
     * https://developer.open-douyin.com/docs/resource/zh-CN/mini-app/develop/server/log-in/code-2-session/
     * <p>
     * 正式地址：https://developer.toutiao.com/api/apps/v2/jscode2session
     * 沙盒地址：https://open-sandbox.douyin.com/api/apps/v2/jscode2session
     */
    public static String API_CODE_2_SESSION = "https://developer.toutiao.com/api/apps/v2/jscode2session";

    /**
     * 域名
     */
    public static String API_HOST = "https://ma.zijieapi.com";

    /**
     * API 获取 TICKET
     */
    public static String API_GET_TICKET = "/api/apps/facial_recognition/v3/get_ticket";
    /**
     * API 获取 TICKET
     */
    public static String API_GET_TICKET_RESULT = "/api/apps/facial_recognition/v3/get_res";
    /**
     * 实名认证接口查询计费信息
     */
    public static String API_GET_DETAIL = "/api/apps/facial_recognition/v3/get_detail";


    @Autowired
    private RestTemplateRequestResolver restTemplateRequestResolver;


    /**
     * 获取票据  用来拉起刷脸使用
     */
    @Override
    public String getTicket(String openId, String aid, String identityName, String identityCode) {
        TiktokAppSecretDomain configAppSecret = getConfigAppSecret();
        //无需加密的参数
        Map<String, Object> requestParam = new HashMap<>();
        requestParam.put("app_id", configAppSecret.getTiktokAppId());
        requestParam.put("open_id", openId);
        requestParam.put("access_token", getAccessToken());
        requestParam.put("scene", getScene(aid));
        //抖音：1128；抖极：2329；抖火：8663
        requestParam.put("aid", aid);
        requestParam.put("is_encrypt", true);

        String pubKeyFormat = String.format(configAppSecret.getPlatformPublishKey());
        //------------------------第一步生成对称密钥和向量--------------------
        byte[] key = TiktokIdentityAuthUtil.randomKeyOrIv(32);
        byte[] iv = TiktokIdentityAuthUtil.randomKeyOrIv(16);
        //----------------------第二步RSA加解密对称密钥key和初始向量iv---------------------------
        byte[] keyEncrypt = TiktokIdentityAuthUtil.encryptPublicKey(Base64.getEncoder().encode(key), pubKeyFormat);
        byte[] ivEncrypt = TiktokIdentityAuthUtil.encryptPublicKey(Base64.getEncoder().encode(iv), pubKeyFormat);
        //
        Map<String, String> encryption_info = new HashMap<>();
        encryption_info.put("key", Base64.getEncoder().encodeToString(keyEncrypt));
        encryption_info.put("iv", Base64.getEncoder().encodeToString(ivEncrypt));
        requestParam.put("encryption_info", encryption_info);
        //-------------------------------第三步加解密敏感数据------------------------------------
        String name = TiktokIdentityAuthUtil.encryptCBC(identityName, key, iv);
        String idcard = TiktokIdentityAuthUtil.encryptCBC(identityCode, key, iv);
        requestParam.put("identity_code", idcard);
        requestParam.put("identity_name", name);

        //处理请求体
        RequestEntity<Map<String, Object>> requestEntity = restTemplateRequestResolver.resolveRequestEntity(requestParam, API_HOST + API_GET_TICKET, HttpMethod.POST, MediaType.APPLICATION_JSON);
        //开始请求
        ResponseEntity<String> stringResponseEntity = restTemplateRequestResolver.resolveResponse(ConfigurableProviderService.PROVIDER_FACE_TIKTOK_MINI, requestEntity, String.class);
        log.info("api getTicket {} 获取抖音票据  request={},  response:{}  Headers = {}", API_GET_TICKET, Lists.newArrayList(identityName, identityCode, requestParam), stringResponseEntity.getBody(), stringResponseEntity.getHeaders());

        //处理响应体
        TiktokGetTicketResponse apiResponse = JsonUtils.json2pojo(stringResponseEntity.getBody(), TiktokGetTicketResponse.class);
        return apiResponse.getTicket();
    }


    /**
     * 通过实名票据，获取用户最近一次实名认证流程的结果
     */
    public TiktokGetTicketResultResponse getTicketResult(String ticket, String aid) {

        TiktokAppSecretDomain configAppSecret = getConfigAppSecret();
        Map<String, Object> map = new HashMap<>();
        map.put("app_id", configAppSecret.getTiktokAppId());
        map.put("access_token", getAccessToken());
        map.put("ticket", ticket);
        map.put("scene", getScene(aid));
        //抖音：1128；抖极：2329；抖火：8663
        map.put("aid", aid);
        map.put("is_encrypt_data", true);
        //处理请求体
        RequestEntity<Map<String, Object>> requestEntity = restTemplateRequestResolver.resolveRequestEntity(map, API_HOST + API_GET_TICKET_RESULT, HttpMethod.POST, MediaType.APPLICATION_JSON);
        //开始请求
        ResponseEntity<String> stringResponseEntity = restTemplateRequestResolver.resolveResponse(ConfigurableProviderService.PROVIDER_FACE_TIKTOK_MINI, requestEntity, String.class);
        log.info("api getTicketResult {} 抖音刷脸结果 未解密的资源 request={},  response:{}  Headers = {}", API_GET_TICKET_RESULT, map, stringResponseEntity.getBody(), stringResponseEntity.getHeaders());

        String body = stringResponseEntity.getBody();

        //处理响应体
        TiktokGetTicketResultResponse apiResponse = JsonUtils.json2pojo(body, TiktokGetTicketResultResponse.class);
        apiResponse.setResponseBody(body);
        // 防止NPE
        apiResponse.setFlow_data(Objects.nonNull(apiResponse.getFlow_data()) ? apiResponse.getFlow_data() : new TiktokGetTicketResultResponse.Data());
        apiResponse.setEncryption_info(Objects.nonNull(apiResponse.getEncryption_info()) ? apiResponse.getEncryption_info() : new TiktokGetTicketResultResponse.ENCRYPTION_INFO());

        //如果刷脸未完 则快速返回 不做解密
        if (!apiResponse.success()) {
            return apiResponse;
        }

        //------------------------------------私钥解密------------------------------------------
        String secretKeyFormat = String.format(configAppSecret.getAppPrivateKey());
        //1.RSA解密key、iv
        String keyRSAE = apiResponse.getEncryption_info().getKey();
        String ivRSAE = apiResponse.getEncryption_info().getIv();
        String keyDecrypt = TiktokIdentityAuthUtil.decryptByPrivateKey(Base64.getDecoder().decode(keyRSAE), secretKeyFormat);
        String ivDecrypt = TiktokIdentityAuthUtil.decryptByPrivateKey(Base64.getDecoder().decode(ivRSAE), secretKeyFormat);
        //2、解密后的结果
        String userVideo = TiktokIdentityAuthUtil.decryptCBC(StringUtils.defaultString(apiResponse.getFlow_data().getUser_video()), Base64.getDecoder().decode(keyDecrypt), Base64.getDecoder().decode(ivDecrypt));
        String userImage = TiktokIdentityAuthUtil.decryptCBC(StringUtils.defaultString(apiResponse.getFlow_data().getUser_image()), Base64.getDecoder().decode(keyDecrypt), Base64.getDecoder().decode(ivDecrypt));
        apiResponse.getFlow_data().setUser_video(userVideo);
        apiResponse.getFlow_data().setUser_image(userImage);
        logTicketResult(apiResponse);
        return apiResponse;
    }

    public static void logTicketResult(TiktokGetTicketResultResponse apiResponse) {
        if (StringUtils.isAllBlank(apiResponse.getFlow_data().getUser_video(), apiResponse.getFlow_data().getUser_image())) {
            log.error("抖音刷脸结果不影响业务告警 api getTicketResult   解密之后的资源全部为空 apiResponse:{}",   JSON.toJSONString(apiResponse) );
        } else if (StringUtils.isAnyBlank(apiResponse.getFlow_data().getUser_video(), apiResponse.getFlow_data().getUser_image())) {
            log.warn("抖音刷脸结果不影响业务告警 api getTicketResult  解密之后的资源部分为空 apiResponse:{}",   JSON.toJSONString(apiResponse) );
        }else {
            log.info("api getTicketResult抖音刷脸结果 解密之后的资源 apiResponse:{}",   JSON.toJSONString(apiResponse) );
        }
    }

    public boolean fee(String ticket, String aid) {

        TiktokGetDetailResponse detailInfo = this.getDetailInfo(ticket, aid);
        if (Objects.isNull(detailInfo) || Objects.isNull(detailInfo.getDetail_info())) {
            return false;
        }
        int chargeNum = detailInfo.getDetail_info().getCharge_num();

        if (chargeNum > 1) {
            log.error("抖音供应商多次扣费  ticket={}, aid={}, detailInfo={}", ticket, aid, JSON.toJSONString(detailInfo));
        }
        return chargeNum >= 1;

    }

    /**
     * 获取是否供应商扣费
     * https://bytedance.larkoffice.com/docx/U7fwdxaRpol7zLxZ2IUctwNBnsd
     */
    public TiktokGetDetailResponse getDetailInfo(String ticket, String aid) {
        TiktokAppSecretDomain configAppSecret = getConfigAppSecret();
        Map<String, Object> map = new HashMap<>();
        map.put("app_id", configAppSecret.getTiktokAppId());
        map.put("access_token", getAccessToken());
        map.put("ticket", ticket);
        map.put("scene", getScene(aid));
        //抖音：1128；抖极：2329；抖火：8663
        map.put("aid", aid);

        String url = API_GET_DETAIL;
        //处理请求体
        RequestEntity<Map<String, Object>> requestEntity = restTemplateRequestResolver.resolveRequestEntity(map, API_HOST + url, HttpMethod.POST, MediaType.APPLICATION_JSON);
        //开始请求
        ResponseEntity<String> stringResponseEntity = restTemplateRequestResolver.resolveResponse(ConfigurableProviderService.PROVIDER_FACE_TIKTOK_MINI, requestEntity, String.class);
        log.info("api getDetailInfo {}  request={},  response:{}  Headers = {}", url, map, stringResponseEntity.getBody(), stringResponseEntity.getHeaders());
        //处理响应体
        return JsonUtils.json2pojo(stringResponseEntity.getBody(), TiktokGetDetailResponse.class);
    }

    /**
     * 由火山引擎AI中台产品解决方案同学@谢欣睿 为客户创建服务树ID（该步骤由火山同学操作，用户无感知），小程序侧为客户申请配置scene，用户可根据该scene接入服务；后续账单出具均以该ID为准。
     * 配置完成后由火山同学将scene参数信息统一同步至协议签署对接人。
     */
    private String getScene(String aid) {
        //    配置化 未来会变
        Map<String, String> tiktokSourceChannel = Optional.ofNullable(tiktokConfig.getTiktokAppCommonDomain().getTiktokChannelSceneMap()).orElse(MapUtils.EMPTY_MAP);
        return tiktokSourceChannel.get(aid);
    }

    /**
     * 获取秘钥相关
     *
     * @return
     */
    private TiktokAppSecretDomain getConfigAppSecret() {
        return tiktokConfig.getTiktokAppSecretDomain();
    }


    /**
     * 过小程序的JSAPI用login接口获取到登录凭证后，开发者可以通过服务器发送请求的方式获取 session_key 和 openid。
     */
    @Override
    public String getOpenId(String code) {

        TiktokAppSecretDomain configAppSecret = getConfigAppSecret();
        Map<String, String> map = new HashMap<>();
        map.put("code", code);
        map.put("appid", configAppSecret.getTiktokAppId());
        map.put("secret", configAppSecret.getTiktokAppSecret());
        //处理请求体
        RequestEntity<Map<String, String>> requestEntity = restTemplateRequestResolver.resolveRequestEntity(map, API_CODE_2_SESSION, HttpMethod.POST, MediaType.APPLICATION_JSON);
        //开始请求
        ResponseEntity<String> stringResponseEntity = restTemplateRequestResolver.resolveResponse(ConfigurableProviderService.PROVIDER_FACE_TIKTOK_MINI, requestEntity, String.class);
        log.info("api getOpenId {} 抖音刷脸结果 request={},  response:{}  Headers = {}", API_CODE_2_SESSION, code, stringResponseEntity.getBody(), stringResponseEntity.getHeaders());

        //处理响应体
        TiktokGetOpenIdResponse apiResponse = JsonUtils.json2pojo(stringResponseEntity.getBody(), TiktokGetOpenIdResponse.class);

        return apiResponse.getData().getOpenid();
    }


    /**
     * 抖音token获取
     */
    public String getAccessToken() {
        //获取秘钥配置信息
        TiktokAppSecretDomain configAppSecret = getConfigAppSecret();
        TiktokGetAccessTokenResponse response = get(tiktokConfig.getTiktokAppCommonDomain().getTiktokAccessTokenTedisKey() + configAppSecret.getTiktokAppId(), new MissesLoadBackString() {
            @Override
            public Object find() {
                Map<String, String> map = new HashMap<>();
                map.put("grant_type", "client_credential");
                map.put("appid", configAppSecret.getTiktokAppId());
                map.put("secret", configAppSecret.getTiktokAppSecret());
                //处理请求体
                RequestEntity<Map<String, String>> requestEntity = restTemplateRequestResolver.resolveRequestEntity(map, API_GET_ACCESS_TOKEN, HttpMethod.POST, MediaType.APPLICATION_JSON);
                //开始请求
                ResponseEntity<String> stringResponseEntity = restTemplateRequestResolver.resolveResponse(ConfigurableProviderService.PROVIDER_FACE_TIKTOK_MINI, requestEntity, String.class);
                log.info("api tiktok api getAccessToken  {} result = {}", API_GET_ACCESS_TOKEN, stringResponseEntity.getBody());

                //处理响应体
                TiktokGetAccessTokenResponse apiResponse = JsonUtils.json2pojo(stringResponseEntity.getBody(), TiktokGetAccessTokenResponse.class);
                return apiResponse;
            }
            @Override
            public Integer getTimeout() {
                //config
                return tiktokConfig.getTiktokAppCommonDomain().getAccessTokenMinuteExpireTime();
            }

            @Override
            public TimeUnit getTimeUnit() {
                return TimeUnit.MINUTES;
            }
        });
        return response.getData().getAccess_token();
    }


    /**
     * |缓存 如果没有则从业务里获取
     */
    public static <V> V get(String key, MissesLoadBackString back) {
        if (StringUtils.isBlank(key)) {
            log.warn("不接收为空的key ,直接返回null对象");
            return null;
        }
        Object value = TedisUtil.get(key.toString());
        if (value == null) {
            value = back.find();
            // NPE控制处理要搞一下 但可能触发缓存穿透
            if (value != null) {
                TedisUtil.set(key, value, back.getTimeout(), back.getTimeUnit());
            }
        }
        return (V) value;
    }


    @Override
    public ProviderFaceAuthorizationResult invoke(FaceInfo faceInfo, ProviderFaceInfo providerFaceInfo, TiktokFaceResultStatusDomain outResultStatus) {
        //ticket
        String ticket = providerFaceInfo.getThirdpardId();
        //aid
        TiktokProviderFaceInput providerFaceInput = JsonUtils.json2pojo(providerFaceInfo.getFaceInput(), TiktokProviderFaceInput.class);
        String aid = providerFaceInput.getAid();
        //查询ticket结果
        TiktokGetTicketResultResponse ticketResult = this.getTicketResult(ticket, aid);

        TiktokFaceResultStatusDomain errorCodeStatus = getTiktokFaceResultStatus(outResultStatus, ticketResult);
        log.info("invoke TiktokFaceResultStatusDomain faceId = {} orderNo = {} outResultStatus = {}  这里的completed只是建议值最终还是看接口返回是否收费", faceInfo.getFaceId(), providerFaceInfo.getOrderNo(), outResultStatus);
        //刷脸是否比对成功
        boolean success = ticketResult.success();
        //是否收费
        boolean fee = this.fee(ticket, aid);
        //刷脸比对成功  || 错误码标记收费
        boolean completed = success || errorCodeStatus.isCompleted();

        log.info("查询抖音认证结果的状态值  faceId = {} orderNo = {} , ticket={},success={},fee={},completed={}", faceInfo.getFaceId(), providerFaceInfo.getOrderNo(), ticket, success, fee, completed);

        ProviderException error = new ProviderException(errorCodeStatus.getFaceStatusCode(), errorCodeStatus.errMsg(), errorCodeStatus);

        //构建返回数据结构
        TiktokFaceAuthorizationResult.TiktokFaceAuthorizationResultBuilder resultBuilder = TiktokFaceAuthorizationResult.createBuilder();

        float similarity = 0;
        if (success) {
            similarity = Float.parseFloat(ticketResult.getFlow_data().getScore());
            String userImage = ticketResult.getFlow_data().getUser_image();
            String userVideo = ticketResult.getFlow_data().getUser_video();

            //下载资源 形成base64
            String base64Image = getBase64ImageByUri(userImage);
            String base64Video = getBase64ImageByUri(userVideo);
            //上传资源到e签宝并保存
            FaceOSSResources ossResources = resourcesInvocationHandler.persistentFaceResources(new FaceBase64Resources(base64Image, base64Video), faceInfo);
            log.info("tiktok  persistentFaceResources ：ossResources = {}  ", ossResources);
            resourcesInvocationHandler.saveUserPhoto(faceInfo, ossResources);

            //设置图片类信息
            resultBuilder.setPhoto(ossResources.getPhoto())
                    .setPhotoType(ossResources.getPhotoType())
                    .setVideo(ossResources.getVideo());
        } else {
            // TODO mangcao      失败的情况
        }

        resultBuilder.setCompleted(completed)
                .setSuccess(success)
                .setFaceId(faceInfo.getFaceId())
                .setError(error)
                .setSimilarity(similarity)
                .setLiveRate(0)
                .setContent(JSON.toJSONString(ticketResult))
                .setFee(fee);
        return resultBuilder.build();
    }

    public static String getBase64ImageByUri(String resourceUri) {
        if (StringUtils.isBlank(resourceUri)) {
            return null;
        }
        try {
            return encodeToBase64(HttpUtilPlus.getAsResponseBytes(resourceUri));
        } catch (Exception e) {
            log.error("getBase64ImageByUri 抖音刷脸返回资源地址下载失败 : {}", resourceUri, e);
            return null;
        }
    }

    public TiktokFaceResultStatusDomain getTiktokFaceResultStatus(TiktokFaceResultStatusDomain outResultStatus, TiktokGetTicketResultResponse ticketResult) {
        //刷脸成功或者外部不给错误码
        if (ticketResult.success() || Objects.isNull(outResultStatus)) {
            return getStatusByCode(ticketResult.getErr_no(), ticketResult.getErr_tips());
        }
        //使用外部错误码
        return outResultStatus;
    }

    private static String encodeToBase64(byte[] data) {
        return Base64Utils.encodeToString(data);
    }

    /**
     * @param code
     * @param errMsg
     * @return
     */
    @Override
    public TiktokFaceResultStatusDomain getStatusByCode(int code, String errMsg) {
        List<TiktokFaceResultStatusDomain> domains = Optional.ofNullable(tiktokConfig.getTiktokFaceResultStatusDomains()).orElse(Collections.emptyList());
        //根据code获取对应的状态
        Optional<TiktokFaceResultStatusDomain> matchFirst = domains.stream().filter(e -> e.getCode() == code).findFirst();
        //可以匹配到数据
        if (matchFirst.isPresent()) {
            return matchFirst.get();
        }

        //匹配不到数据实名默认
        Optional<TiktokFaceResultStatusDomain> defaultFirst = domains.stream().filter(TiktokFaceResultStatusDomain::isDefault).findFirst();
        if (defaultFirst.isPresent()) {
            log.error("TiktokFaceResultStatusDomain  errMsg = {} use defaultFirst = {}", errMsg, defaultFirst);
            return defaultFirst.get();
        }
        //配置里不关刷脸流程
        return new TiktokFaceResultStatusDomain().setCode(code).setCompleted(false)
                .setDesc(StringUtils.defaultIfBlank(errMsg, FaceStatusCode.FACE_FIELD_COMPLETED_INVOCATION.getMsg())).setFaceStatusCode(FaceStatusCode.FACE_FIELD_COMPLETED_INVOCATION.getCode());
    }

}
