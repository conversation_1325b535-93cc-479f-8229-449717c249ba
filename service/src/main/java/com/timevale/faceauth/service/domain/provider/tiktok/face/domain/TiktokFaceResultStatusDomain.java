package com.timevale.faceauth.service.domain.provider.tiktok.face.domain;

import com.timevale.faceauth.service.exception.ProviderErrorEnum;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 此配置类 ，业务上不允许进行set操作
 *
 * <AUTHOR>
 * @DATE 2024/5/21 19:42
 */
@Data
@Accessors(chain = true)
public class TiktokFaceResultStatusDomain extends ToString implements ProviderErrorEnum {


    /**
     * 供应商错误码
     */
    private int code;
    /**
     * 流程是否完成
     */
    private boolean completed;
    /**
     *
     */
    private String desc;

    /**
     * @see com.timevale.faceauth.service.core.FaceStatusCode#code
     */
    private Integer faceStatusCode;


    private boolean isDefault;

    /**
     * 0 停留当前小程序 ，1 返回客户小程序
     */
    private Integer returnType;

    /**
     *  0 不提示 ，1 轻提示， 2 重提示，3引导授权相机权限..
     */
    private Integer tipsStatus;


    @Override
    public String errCode() {
        return String.valueOf(code);
    }

    @Override
    public String errMsg() {
        return desc;
    }

}
