package com.timevale.faceauth.service.domain.repository;

import com.timevale.faceauth.dal.pfs.face.support.FaceLocalLibraryPhotoDAO;
import com.timevale.faceauth.dal.pfs.face.support.FaceLocalLibraryPhotoDO;
import com.timevale.faceauth.dal.pfs.face.support.FaceLocalLibraryPhotoV2DAO;
import com.timevale.faceauth.dal.pfs.face.support.FaceLocalLibraryPhotoV2DO;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.core.support.ArgumentUtil;
import com.timevale.faceauth.service.domain.UserFactor2Token;
import com.timevale.faceauth.service.localphoto.PhotoOriginConfigBean;
import com.timevale.faceauth.service.localphoto.UseLocalPhotoConfig;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 刷脸缓存数据存储
 *
 * <AUTHOR>
 * @copyright 2024
 * @date 2024/07/11 17
 */
@Repository
public class FaceLocalLibraryPhotoRepository {

    @Autowired
    private UseLocalPhotoConfig localPhotoConfig;
    @Autowired
    private UserPhotoRepository userPhotoRepository;

    private final FaceLocalLibraryPhotoDAO localLibraryPhotoDAO;

    private final FaceLocalLibraryPhotoV2DAO localLibraryPhotoV2DAO;

    @Autowired
    public FaceLocalLibraryPhotoRepository(FaceLocalLibraryPhotoDAO localLibraryPhotoDAO, FaceLocalLibraryPhotoV2DAO localLibraryPhotoV2DAO) {
        this.localLibraryPhotoDAO = localLibraryPhotoDAO;
        this.localLibraryPhotoV2DAO = localLibraryPhotoV2DAO;
    }

    public void saveUserPhoto(FaceLocalLibraryPhoto photoEntity) throws FaceException {

        //通过配置决定是否保存到本地刷脸缓存库
        PhotoOriginConfigBean config = UseLocalPhotoConfig.getConfig(photoEntity.getProvider());
        if (config != null && config.isLocalPhotoOriginEnable()) {
            preposeSave(photoEntity);
            if(localPhotoConfig.isLocalCacheNew()){
                saveV2(photoEntity);
            }else {
                save(photoEntity);
            }
        }

        //是否存储到老的缓存库
        if (localPhotoConfig.isOriginOldWrite()) {
            userPhotoRepository.saveUserPhoto(photoEntity);
        }
    }
    private void preposeSave(FaceLocalLibraryPhoto photoEntity){
        String hashFactor2 = photoEntity.getHashFactor2();
        if(localPhotoConfig.isLocalCacheNew()){
            localLibraryPhotoV2DAO.delUserPhotoByProvider(hashFactor2, photoEntity.getProvider());
        }else {
            localLibraryPhotoDAO.delUserPhotoByProvider(hashFactor2, photoEntity.getProvider());
        }
    }

    private void save(FaceLocalLibraryPhoto photoEntity) {
        FaceLocalLibraryPhotoDO photoDO = photoEntity.toEntity();
        int rows;
        try {
            photoDO.switchEnabled = 1;
            photoDO.expiredTime = DateUtils.addDays(new Date(), localPhotoConfig.getOriginExpiredDay());
            rows = localLibraryPhotoDAO.insert(photoDO);
        } catch (Exception cause) {
            String msg;
            try {
                msg = JsonUtils.obj2json(photoDO);
            } catch (Exception ignore) {
                msg = null;
            }
            if (null == msg) {
                throw FaceException.valueOf(
                        FaceStatusCode.RESOURCE_FAILED_DATASOURCE, "Fail persistence to db .", cause);
            } else {
                throw FaceException.valueOf(
                        FaceStatusCode.RESOURCE_FAILED_DATASOURCE, "Fail persistence on data[" + msg + "] .", cause);
            }
        }
        if (0 >= rows) {
            String msg;
            try {
                msg = JsonUtils.obj2json(photoDO);
            } catch (Exception ignore) {
                msg = null;
            }
            if (null == msg) {
                throw FaceException.valueOf(FaceStatusCode.RESOURCE_FAILED_DATASOURCE, "Fail persistence to db .");
            } else {
                throw FaceException.valueOf(
                        FaceStatusCode.RESOURCE_FAILED_DATASOURCE, "Fail persistence on data[" + msg + "] .");
            }
        }
    }

    private void saveV2(FaceLocalLibraryPhoto photoEntity) {
        FaceLocalLibraryPhotoV2DO photoDO = photoEntity.toEntityV2();
        int rows;
        try {
            photoDO.switchEnabled = 1;
            photoDO.expiredTime = DateUtils.addDays(new Date(), localPhotoConfig.getOriginExpiredDay());
            rows = localLibraryPhotoV2DAO.insert(photoDO);
        } catch (Exception cause) {
            String msg;
            try {
                msg = JsonUtils.obj2json(photoDO);
            } catch (Exception ignore) {
                msg = null;
            }
            if (null == msg) {
                throw FaceException.valueOf(
                        FaceStatusCode.RESOURCE_FAILED_DATASOURCE, "Fail persistence to db .", cause);
            } else {
                throw FaceException.valueOf(
                        FaceStatusCode.RESOURCE_FAILED_DATASOURCE, "Fail persistence on data[" + msg + "] .", cause);
            }
        }
        if (0 >= rows) {
            String msg;
            try {
                msg = JsonUtils.obj2json(photoDO);
            } catch (Exception ignore) {
                msg = null;
            }
            if (null == msg) {
                throw FaceException.valueOf(FaceStatusCode.RESOURCE_FAILED_DATASOURCE, "Fail persistence to db .");
            } else {
                throw FaceException.valueOf(
                        FaceStatusCode.RESOURCE_FAILED_DATASOURCE, "Fail persistence on data[" + msg + "] .");
            }
        }
    }

    public UserPhoto getPhotoByFactor2(String appId, String idNo, String name) throws FaceException {


        ArgumentUtil.throwIfEmptyArgument(idNo, "idNo");
        ArgumentUtil.throwIfEmptyArgument(name, "name");


        //用户年纪限制
        if(!localPhotoConfig.isValidAge(idNo) || localPhotoConfig.containsIgnoreAppId(appId)){
            return null;
        }

        String hashing = buildHashing(idNo, name);

        try {
            List<FaceLocalLibraryPhotoDO> list;
            if(localPhotoConfig.isLocalCacheNew()){
                list = localLibraryPhotoV2DAO.getLatestPhotoByFactor2(hashing);
            }else {
                list = localLibraryPhotoDAO.getLatestPhotoByFactor2(hashing);
            }
            if (CollectionUtils.isNotEmpty(list)) {
                return checkUserPhotoExpired(FaceLocalLibraryPhoto.valueOf(list.get(0)));
            }
        } catch (Exception cause) {
            throw FaceException.valueOf(
                    FaceStatusCode.RESOURCE_FAILED_DATASOURCE,
                    "Fail get photo on factor2[" + idNo + "/" + name + "] .",
                    cause);
        }


        //是否使用老的缓存库
        if (localPhotoConfig.isOriginOldRead()) {
            UserPhoto userPhoto = userPhotoRepository.getPhotoByFactor2(hashing);
            if (userPhoto != null) {
                 return checkUserPhotoExpired(userPhoto);
            }
        }
        return null;
    }



    public UserPhoto getPhotoByFactor2(LocalLibraryUser localUser) throws FaceException {
        localUser.valid();
        //1、用户年纪限制
        //2、命中忽略刷脸缓存appId
        if(!localPhotoConfig.isValidAge(localUser.getIdNo()) ||
                localPhotoConfig.containsIgnoreAppId(localUser.getBizAppId())){
            return null;
        }

        //通过配置决定是否保存到本地刷脸缓存库
        PhotoOriginConfigBean config = UseLocalPhotoConfig.getConfig(localUser.getProvider());
        //符合条件才走刷脸缓存
        //1、开启全局的刷脸缓存
        if (config != null && config.isUseOriginEnable()) {
            UserPhoto userPhoto = get(localUser);
            if (userPhoto != null) {
                return userPhoto;
            }
        }

        //是否使用老的缓存库
        if (localPhotoConfig.isOriginOldRead()) {
            String hashing = buildHashing(localUser.getIdNo(), localUser.getName());
            UserPhoto userPhoto = userPhotoRepository.getPhotoByFactor2(hashing);
            if (userPhoto != null) {
                return checkUserPhotoExpired(userPhoto);
            }
        }

        return null;
    }

    private UserPhoto checkUserPhotoExpired(UserPhoto userPhoto){
        if(userPhoto == null){
            return userPhoto;
        }
        Long expired = userPhoto.getCreateTime().getTime()  + localPhotoConfig.getOriginExpiredDay() * 24 * 3600*1000L;
        if(expired > System.currentTimeMillis()){
            return userPhoto;
        }
        return null;
    }

    private UserPhoto get(LocalLibraryUser localUser) {
        String idNo = localUser.getIdNo();
        String name = localUser.getName();
        String hashing = buildHashing(idNo, name);
        UserPhoto userPhoto = null;
        List<String> useOriginProvider = localPhotoConfig.getUseOriginProviders();
        if (CollectionUtils.isEmpty(useOriginProvider)) {
            return null;
        }
        try {
            List<FaceLocalLibraryPhotoDO> list;
            if(localPhotoConfig.isLocalCacheNew()){
                list = localLibraryPhotoV2DAO.getLatestPhotoByFactor2(hashing);
            }else {
                list = localLibraryPhotoDAO.getLatestPhotoByFactor2(hashing);
            }
            if (CollectionUtils.isEmpty(list)) {
                return null;
            }
            for (String provider : useOriginProvider) {
                FaceLocalLibraryPhotoDO entity =
                        list.stream()
                            .filter(x ->
                                        Objects.equals(x.provider, provider))
                            .findFirst()
                            .orElse(null);
                userPhoto  = FaceLocalLibraryPhoto.valueOf(entity);
                userPhoto = checkUserPhotoExpired(userPhoto);
                if (userPhoto != null) {
                    break;
                }
            }
        } catch (Exception cause) {
            throw FaceException.valueOf(
                    FaceStatusCode.RESOURCE_FAILED_DATASOURCE,
                    "Fail get photo on factor2[" + idNo + "/" + name + "] .",
                    cause);
        }
        return userPhoto;
    }


    public boolean clearUserPhoto(String idNo, String name) throws FaceException {
        String hashing = buildHashing(idNo, name);
        int count;
        try {
            if(localPhotoConfig.isLocalCacheNew()){
                count = localLibraryPhotoV2DAO.delUserPhoto(hashing);
            }else {
                count = localLibraryPhotoDAO.delUserPhoto(hashing);
            }

            userPhotoRepository.clearUserPhoto(idNo);
        } catch (Exception cause) {
            throw FaceException.valueOf(
                    FaceStatusCode.RESOURCE_FAILED_DATASOURCE, "Fail del photo on idNo[" + idNo + "] .", cause);
        }
        return count > 0;
    }

    private static String buildHashing(String idNo, String name) {
        ArgumentUtil.throwIfEmptyArgument(idNo, "idNo");
        ArgumentUtil.throwIfEmptyArgument(name, "name");
        String hashing = UserFactor2Token.valueOf(idNo, name).getHashingValue();
        return hashing;
    }
}
