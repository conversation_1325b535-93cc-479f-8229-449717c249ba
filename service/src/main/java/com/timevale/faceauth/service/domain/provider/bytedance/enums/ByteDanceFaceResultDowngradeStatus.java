//package com.timevale.faceauth.service.domain.provider.bytedance.enums;
//
//import lombok.Getter;
//
///**
// * <AUTHOR>
// * @DATE 2025/4/21 20:25
// */
//@Getter
//public enum ByteDanceFaceResultDowngradeStatus {
//    ;
//
//    ByteDanceFaceResultDowngradeStatus(int code, String desc) {
//        this.code = code;
//        this.desc = desc;
//    }
//
//    private int code;
//
//    private String desc;
//}
