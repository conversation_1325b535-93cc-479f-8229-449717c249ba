package com.timevale.faceauth.service.domain.provider.audiovideodual.esign;

import com.timevale.faceauth.service.domain.provider.audiovideodual.AudioVideoDualFaceApplyResultResolve;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.timevale.faceauth.service.domain.provider.ConfigurableProviderService.*;


/**
 * H5智能视频认证初始化认证数据转换
 *
 * <AUTHOR>
 * @copyright 2024
 * @date 2024/9/9
 */
@Slf4j
@Component
class EsignVideoDualFaceApplyResultResolve
    extends AudioVideoDualFaceApplyResultResolve {

  @Override
  public String getProviderName() {
    return PROVIDER_AUDIO_VIDEO_ESIGN;
  }

  @Override
  public String getFullName() {
    return FULL_NAME_PROVIDER_AUDIO_VIDEO_ESIGN;
  }


}
