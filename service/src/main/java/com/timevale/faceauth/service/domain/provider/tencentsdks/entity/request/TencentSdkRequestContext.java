package com.timevale.faceauth.service.domain.provider.tencentsdks.entity.request;

import com.alibaba.fastjson.JSON;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.processor.FaceRequestContext;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021-07-13 16:55
 */
public class TencentSdkRequestContext implements FaceRequestContext {

    private String appId;
    private String idNo;
    private String idType;
    private String name;
    private String provider;
    private String photo;
    private String photoType;

    public TencentSdkRequestContext(String appId, String name, String idType, String idNo, String provider) {
        this.appId = appId;
        this.idNo = idNo;
        this.idType = idType;
        this.name = name;
        this.provider = provider;
    }

    public TencentSdkRequestContext(String appId, String name, String idType, String idNo, String provider, String photo, String photoType) {
        this.appId = appId;
        this.idNo = idNo;
        this.idType = idType;
        this.name = name;
        this.provider = provider;
        this.photo = photo;
        this.photoType = photoType;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getIdNo() {
        return idNo;
    }

    @Override
    public String getIdType() {
        return idType;
    }

    @Override
    public String getAppId() {
        return appId;
    }

    @Override
    public String getOid() {
        throw FaceException.valueOf(FaceStatusCode.SERVICE_UNSUPPORTED);
    }

    @Override
    public String getBizId() {
        throw FaceException.valueOf(FaceStatusCode.SERVICE_UNSUPPORTED);
    }

    @Override
    public String getBizCode() {
        throw FaceException.valueOf(FaceStatusCode.SERVICE_UNSUPPORTED);
    }

    @Override
    public String getClientType() {
        throw FaceException.valueOf(FaceStatusCode.SERVICE_UNSUPPORTED);
    }

    @Override
    public String getProvider() {
        return provider;
    }

    @Override
    public String getPhoto() {
        return photo;
    }

    @Override
    public String getPhotoType() {
        return photoType;
    }

    @Override
    public String getReturnUrl() {
        throw FaceException.valueOf(FaceStatusCode.SERVICE_UNSUPPORTED);
    }

    @Override
    public String getCallbackUrl() {
        throw FaceException.valueOf(FaceStatusCode.SERVICE_UNSUPPORTED);
    }

    @Override
    public long getTimestamp() {
        throw FaceException.valueOf(FaceStatusCode.SERVICE_UNSUPPORTED);
    }

    @Override
    public String getInput() {
        Map<String, String> input = new HashMap<>();
        input.put("name", name);
        input.put("idType", idType);
        input.put("idNo", idNo);
        input.put("provider", provider);
        input.put("photo", photo);
        input.put("photoType", photoType);
        return JSON.toJSONString(input);
    }
}
