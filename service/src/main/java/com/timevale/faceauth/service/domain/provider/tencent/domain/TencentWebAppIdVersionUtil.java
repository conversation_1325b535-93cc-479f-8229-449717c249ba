package com.timevale.faceauth.service.domain.provider.tencent.domain;

import com.timevale.faceauth.service.constant.SystemConfig;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.provider.tencent.TencentWebAppIdVersion;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;

/**
 * <AUTHOR>
 * @DATE 2024/8/26 10:26
 */
public class TencentWebAppIdVersionUtil {

    /**
     * 录制视频模式
     */
    private static final String SCENE_CODE_RECORD_VIDEO = "RECORD_VIDEO";

    /**
     * 获取配置项
     *
     * @return
     */
    private static TencentCloudCommonIdentificationConfigDomain getTencentCloudCommonIdentificationConfigDomain() {
        TencentCloudCommonIdentificationConfigDomain domain = SystemConfig.tencentCloudCommonIdentificationConfigDomain;
        if (domain == null || CollectionUtils.isEmpty(domain.getIdentificationList())) {
            throw new FaceException(FaceStatusCode.FACE_FAILED_AUTHORIZATION_INITIALIZE);
        }
        return domain;
    }


    /**
     * 根据APPID获取数据
     *
     * @param client
     * @param appId
     * @return
     */
    public static TencentWebAppIdVersion getAppVersionByAppId(String client, String appId) {
        TencentCloudCommonIdentificationConfigDomain domain = getTencentCloudCommonIdentificationConfigDomain();

        for (TencentWebAppIdVersionHookDTO versionHookDTO : domain.getIdentificationList()) {
            TencentWebAccessHolderHookDTO accessHolder = versionHookDTO.getAccessHolder();
            if (!StringUtils.equals(versionHookDTO.getAbility(), client) || accessHolder == null) {
                continue;
            }
            if (StringUtils.equals(appId, accessHolder.getAppId())) {
                return versionHookDTO;
            }
        }
        return null;
    }


    /**
     * 根据配置项获取数据
     *
     * @param client
     * @param code
     * @return
     */
    public static TencentWebAppIdVersion getAppVersionByCode(String client, String code) {
        TencentCloudCommonIdentificationConfigDomain domain = getTencentCloudCommonIdentificationConfigDomain();

        for (TencentWebAppIdVersionHookDTO versionHookDTO : domain.getIdentificationList()) {
            if (!StringUtils.equals(versionHookDTO.getAbility(), client)) {
                continue;
            }
            if (StringUtils.equals(code, versionHookDTO.getCode())) {
                return versionHookDTO;
            }
        }
        return null;

    }


    public static TencentWebAppIdVersion getAppVersionBySceneCode(String client, String sceneCode) {
        TencentCloudCommonIdentificationConfigDomain domain = getTencentCloudCommonIdentificationConfigDomain();

        for (TencentWebAppIdVersionHookDTO versionHookDTO : domain.getIdentificationList()) {
            if (!StringUtils.equals(versionHookDTO.getAbility(), client)) {
                continue;
            }
            if (StringUtils.equals(sceneCode, versionHookDTO.getSceneCode())) {
                return versionHookDTO;
            }
        }
        return null;
    }

    public static TencentWebAppIdVersion getAppVersionByVersion(String client, String version) {
        TencentCloudCommonIdentificationConfigDomain domain = getTencentCloudCommonIdentificationConfigDomain();

        for (TencentWebAppIdVersionHookDTO versionHookDTO : domain.getIdentificationList()) {
            if (!StringUtils.equals(versionHookDTO.getAbility(), client)) {
                continue;
            }
            if (StringUtils.equals(version, versionHookDTO.getVersion())) {
                return versionHookDTO;
            }
        }
        return null;
    }

    public static TencentWebAppIdVersion getH5RecordVideoAppVersion() {
        String client = TencentWebAppIdVersionHookDTO.ABILITY_H5;
        return getAppVersionBySceneCode(client, SCENE_CODE_RECORD_VIDEO);
    }
}
