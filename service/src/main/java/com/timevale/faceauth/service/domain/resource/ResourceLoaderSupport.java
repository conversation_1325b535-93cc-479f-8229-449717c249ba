package com.timevale.faceauth.service.domain.resource;

import com.timevale.faceauth.service.domain.resource.task.ResourceDownloadMsg;

/**
 * <AUTHOR>
 */
public class ResourceLoaderSupport {

    public static String buildResourceLoaderKey(ProviderRemoteResourceLoader resourceLoader ){
        if(resourceLoader == null){
            throw new IllegalArgumentException("resourceLoader is null");
        }

        return resourceLoader.getProviderName()
                +resourceLoader.resourceType().getCode()
                +resourceLoader.strategy();
    }

    public static String buildResourceLoaderKey(String provider, FaceResourceType resourceType , ResourceStrategyEnum strategy){
        return provider+resourceType.getCode()+strategy.getCode();
    }
    public static String buildResourceLoaderKey(ResourceDownloadMsg downloadMsg ){
        return downloadMsg.getProvider()
                +downloadMsg.getResourceType()
                +ResourceStrategyEnum.SYNC.getCode();
    }
}
