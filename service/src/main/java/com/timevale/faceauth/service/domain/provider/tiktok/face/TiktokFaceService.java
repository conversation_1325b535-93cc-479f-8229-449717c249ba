package com.timevale.faceauth.service.domain.provider.tiktok.face;

import com.alibaba.fastjson.JSON;
import com.timevale.faceauth.service.constant.SystemConfig;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStartResult;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.core.support.FaceQueryableExtend;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.domain.provider.ConfigurableProviderApplyResultResolver;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationData;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationResult;
import com.timevale.faceauth.service.domain.provider.bytedance.ByteDanceFaceResultStatus;
import com.timevale.faceauth.service.domain.provider.support.AbstractProviderFaceAuthorizationResult;
import com.timevale.faceauth.service.domain.provider.support.AbstractProviderService;
import com.timevale.faceauth.service.domain.provider.support.FaceAuthorizationInitializingContext;
import com.timevale.faceauth.service.domain.provider.tiktok.face.domain.TiktokFaceAuthorizationResult;
import com.timevale.faceauth.service.domain.provider.tiktok.face.domain.TiktokFaceResultStatusDomain;
import com.timevale.faceauth.service.domain.provider.tiktok.face.entity.request.TiktokProviderFaceInput;
import com.timevale.faceauth.service.domain.provider.tiktok.face.entity.response.TiktokGetTicketResultResponse;
import com.timevale.faceauth.service.domain.provider.tiktok.face.service.TiktokOpenApiClient;
import com.timevale.faceauth.service.domain.provider.tiktok.face.util.TiktokConfig;
import com.timevale.faceauth.service.domain.repository.FaceInfo;
import com.timevale.faceauth.service.domain.repository.FaceOSSResources;
import com.timevale.faceauth.service.domain.repository.FaceRepository;
import com.timevale.faceauth.service.domain.repository.FaceSwitchRepository;
import com.timevale.faceauth.service.domain.repository.ProviderFaceInfo;
import com.timevale.faceauth.service.domain.repository.ProviderFaceRepository;
import com.timevale.faceauth.service.domain.repository.ProviderReturnInfo;
import com.timevale.faceauth.service.domain.support.DnsResolver;
import com.timevale.faceauth.service.exception.ProviderException;
import com.timevale.faceauth.service.input.HandleFaceAuthorizationReturnInput;
import com.timevale.faceauth.service.result.FaceAuthResult;
import com.timevale.framework.sands.Sahara;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.MapUtils;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @DATE 2024/5/7 10:49
 */
@Slf4j
@Component
public class TiktokFaceService extends AbstractProviderService  implements ConfigurableProviderApplyResultResolver {
    @Autowired
    private TiktokOpenApiClient tiktokOpenApiClient;

    @Autowired
    private SystemConfig systemConfig;

    @Autowired
    private TiktokConfig tiktokConfig;

    @Autowired
    private FaceRepository faceRepository;
    @Autowired
    private ProviderFaceRepository providerFaceRepository;

    @Autowired
    public TiktokFaceService(FaceRepository faceRepository,
                             ProviderFaceRepository providerFaceRepository,
                             DnsResolver dnsResolver,
                             ConfigurableProperties configurableProperties,
                             FaceSwitchRepository faceSwitchRepository) {
        super(faceRepository, providerFaceRepository, dnsResolver, configurableProperties, faceSwitchRepository);
    }

    @Override
    public FaceAuthResult resolveApplyResult(String clientType, FaceStartResult result) {
        // 2024/8/29  mangcao      不需要返回url
        return resolveApplyResultOnlyFaceUrl(result);
    }

    @Override
    public String getProviderName() {
        return PROVIDER_FACE_TIKTOK_MINI;
    }

    @Override
    public String getFullName() {
        return FULL_NAME_PROVIDER_FACE_TIKTOK_MINI;
    }

    @Override
    protected AbstractProviderFaceAuthorizationResult resolveDoneAuthorizationResult(ProviderReturnInfo providerReturn, FaceQueryableExtend extend, FaceInfo faceInfo, ProviderFaceInfo providerFaceInfo) throws FaceException {
        log.info("TiktokFaceService # resolveDoneAuthorizationResult  providerReturn:{} extend:{} faceInfo:{} providerFaceInfo:{}", JSON.toJSONString(providerReturn), JSON.toJSONString(extend), JSON.toJSONString(faceInfo), JSON.toJSONString(providerFaceInfo));

        boolean completed = providerFaceInfo.isDone();
        boolean success = faceInfo.isOk();
        ProviderException error = null;
        //流程完成，但是刷脸比对不成功
        if (completed && !success) {
            String errCode = providerFaceInfo.errCode;
            ByteDanceFaceResultStatus resultStatus = ByteDanceFaceResultStatus.getStatusByCode(Integer.parseInt(errCode));
            error = new ProviderException(resultStatus.getFaceCode(), resultStatus);
        }


        float similarity = this.extractedSimilarity(providerReturn);

        FaceOSSResources faceOSSResources = this.getFaceResources(faceInfo.getFaceId());

        TiktokFaceAuthorizationResult.TiktokFaceAuthorizationResultBuilder authorizationResultBuilder = TiktokFaceAuthorizationResult.createBuilder()
                .setCompleted(completed)
                .setSuccess(success)
                .setFaceId(faceInfo.getFaceId())
                .setPhoto(faceOSSResources.getPhoto())
                .setPhotoType(faceOSSResources.getPhotoType())
                .setVideo(faceOSSResources.getVideo())
                .setError(error)
                .setSimilarity(similarity)
                .setLiveRate(0);
        if (completed) {
            authorizationResultBuilder.setLiveRate(100);
        }
        return authorizationResultBuilder.build();
    }

    /**
     * 提取比对分
     *
     * @param providerReturn
     * @return
     */
    public float extractedSimilarity(ProviderReturnInfo providerReturn) {
        float similarity = 0;
        if (Objects.isNull(providerReturn) || StringUtils.isBlank(providerReturn.getData())) {
            return similarity;
        }

        TiktokGetTicketResultResponse ticketResultResponse = JSON.parseObject(providerReturn.getData(), TiktokGetTicketResultResponse.class);
        if (Objects.isNull(ticketResultResponse) || Objects.isNull(ticketResultResponse.getFlow_data()) || StringUtils.isBlank(ticketResultResponse.getFlow_data().getScore())) {
            return similarity;
        }
        return Float.parseFloat(ticketResultResponse.getFlow_data().getScore());
    }

    @Override
    protected ProviderFaceAuthorizationResult doQueryAuthorizeResult(FaceQueryableExtend extend, FaceInfo faceInfo, ProviderFaceInfo providerFaceInfo) throws FaceException {
        log.info("TiktokFaceService#doQueryAuthorizeResult  doQueryAuthorizeResult ={} ", JSON.toJSONString(faceInfo));
        return innerGetProviderResult(faceInfo, providerFaceInfo, null);
    }

    private ProviderFaceAuthorizationResult innerGetProviderResult(FaceInfo faceInfo, ProviderFaceInfo providerFaceInfo, TiktokFaceResultStatusDomain outResultStatus) {
        if (this.checkTasKExpired(faceInfo)) {
            throw FaceException.valueOf(FaceStatusCode.FACE_TASK_EXPIRED);
        }
        return tiktokOpenApiClient.invoke(faceInfo, providerFaceInfo, outResultStatus);
    }

    @Override
    protected ProviderFaceAuthorizationResult detectHandleFaceAuthorizationReturned(String faceId, HandleFaceAuthorizationReturnInput request) {
        FaceInfo faceInfo = faceRepository.getFaceInfoByFaceId(faceId);
        ProviderFaceInfo providerFaceInfo = providerFaceRepository.getByFaceId(faceInfo.getFaceId());

        TiktokFaceResultStatusDomain outResultStatus = this.getOutTiktokFaceResultStatus(request.getExtendedMap());
        log.info("TiktokFaceResultStatusDomain faceId = {} orderNo = {} outResultStatus = {} 这里的completed只是建议值最终还是看接口返回是否收费", faceId, providerFaceInfo.getOrderNo(), outResultStatus);
        //查询供应商
        return this.innerGetProviderResult(faceInfo, providerFaceInfo, outResultStatus);
    }

    /**
     * 获取小程序端传入的错误码信息
     *
     * @param extendedMap
     * @return
     */
    public TiktokFaceResultStatusDomain getOutTiktokFaceResultStatus(Map<String, String> extendedMap) {
        if (MapUtils.isEmpty(extendedMap)) {
            return null;
        }
        //归一错误码
        String errCode = extendedMap.get(HandleFaceAuthorizationReturnInput.ExtendedMapConstant.errCode);
        //errNo 小程序端返回的错误 errNo对实名错误码做了归一化处理
        String errNo = extendedMap.get(HandleFaceAuthorizationReturnInput.ExtendedMapConstant.errNo);
        //eg : {\"errMsg\":\"startFacialRecognitionVerify:fail 1002023 流程异常，请退出后重试\",\"errCode\":1200,\"errNo\":21102}
        //eg : {\"errMsg\":\"startFacialRecognitionVerify:fail 6003030 认证失败，请重新输入证件信息\",\"errCode\":1200,\"errNo\":21102}
        //eg : {\"errMsg\":\"startFacialRecognitionVerify:fail user deny\",\"errCode\":2200,\"errNo\":10200}
        //eg : {\"errMsg\":\"startFacialRecognitionVerify:fail -1006 用户取消操作\",\"errCode\":3200,\"errNo\":21108}
        //eg : {\"errMsg\":\"startFacialRecognitionVerify:fail 1003114 保持人物姿态端正无遮挡  屏幕内无过亮、过暗区域\",\"errCode\":1800,\"errNo\":21113}
        //eg : {\"errCode\":0,\"verifyResult\":\"byted1b714d99e4e340ecb5ab51dcd16582a9_lf\",\"errMsg\":\"startFacialRecognitionVerify:ok\"}
        String errMsg = extendedMap.get(HandleFaceAuthorizationReturnInput.ExtendedMapConstant.errMsg);

        if (StringUtils.isAllBlank(errCode, errNo, errMsg)) {
            return null;
        }
        try {
            // 解析errMsg中 key= 抖音服务端返回的错误码  ;  value = 可用的文案信息
            Pair<Integer, String> pair = analyzeErrMsgErrorCode(errMsg);
            //刷脸成功
            if (StringUtils.equals(errCode, "0")) {
                return tiktokOpenApiClient.getStatusByCode(Integer.parseInt(errCode), pair.getRight());
            }
            //如果errMsg 中含抖音服务端返回的错误码
            if (Objects.nonNull(pair.getLeft())) {
                TiktokFaceResultStatusDomain errMsgResult = tiktokOpenApiClient.getStatusByCode(pair.getLeft(), pair.getRight());
                if (Objects.nonNull(errMsgResult) && !errMsgResult.isDefault()) {
                    return errMsgResult;
                }
            }
            //兜底使用errNo
            return tiktokOpenApiClient.getStatusByCode(Integer.parseInt(errNo), pair.getRight());
        } catch (Exception e) {
            log.warn("无法处理  getTiktokFaceResultStatus  request : {} ", extendedMap, e);
        }
        return null;
    }


    public Pair<Integer, String> analyzeErrMsgErrorCode(String msg) {
        //npe
        msg = StringUtils.defaultString(msg);
        //替换无用的字符串
        List<String> replaceBlank = tiktokConfig.getTiktokAppCommonDomain().getTiktokOutResultStatusReplaceBlank();
        if (CollectionUtils.isNotEmpty(replaceBlank)) {
            for (String item : replaceBlank) {
                msg = msg.replaceAll(item, StringUtils.EMPTY);
            }
        }
        //前后去空格
        msg = msg.trim();
        if (StringUtils.isBlank(msg)) {
            return Pair.of(null, null);
        }
        String[] split = msg.split(StringUtils.SPACE, 2);
        Integer errMsgErrorCode = string2Integer(split[0]);
        return Pair.of(errMsgErrorCode, split[split.length == 1 ? 0 : 1]);

    }

    private static Integer string2Integer(String split) {
        try {
            return Integer.parseInt(split);
        } catch (Exception e) {
            log.info("string2Integer fail , {}", split);
            return null;
        }
    }


    /**
     * 推断供应商回跳的数据结果
     *
     * @param faceId
     * @param request
     */
    @Override
    protected ProviderFaceAuthorizationResult detectFaceAuthorizationResultOnReturn(String faceId, HttpServletRequest request) throws FaceException {
        log.info("TiktokFaceService # detectFaceAuthorizationResultOnReturn faceId:{}  ", faceId);
        FaceInfo faceInfo = faceRepository.getFaceInfoByFaceId(faceId);
        ProviderFaceInfo providerFaceInfo = providerFaceRepository.getByFaceId(faceInfo.getFaceId());

        return innerGetProviderResult(faceInfo, providerFaceInfo, null);
    }

    /**
     * 推断供应商回调的数据结果
     *
     * @param faceId
     * @param dataBuffer
     * @param request
     */
    @Override
    protected ProviderFaceAuthorizationResult detectFaceAuthorizationResultOnCallback(String faceId, byte[] dataBuffer, HttpServletRequest request) throws FaceException {
        log.info("TiktokFaceService # detectFaceAuthorizationResultOnCallback faceId:{}  ", faceId);
        // unsupported, and throws
        throw FaceException.valueOf(FaceStatusCode.SERVICE_UNSUPPORTED);
    }

    @Override
    protected ProviderFaceAuthorizationData doInitialize(FaceAuthorizationInitializingContext initializingContext) throws FaceException {
        log.info("TiktokFaceService#doInitialize  initializingContext ={} ", JSON.toJSONString(initializingContext));
        //第一步生成 orderNo
        String orderNo = "e-tt-" + Sahara.instance.getHexSand();
        //第二步生产 data
        String tiktokAuthUrl = tiktokConfig.getTiktokAppCommonDomain().getTiktokAuthUrl();
        String data = String.format(tiktokAuthUrl, orderNo, System.currentTimeMillis(), systemConfig.getEnv(), tiktokConfig.getTiktokAppSecretDomain().getTiktokAppId());
        return ProviderFaceAuthorizationData.createBuilder()
                .setThirdpartId(null)
                .setProviderName(getFullName())
                .setProvider(getProviderName())
                .setOrderNo(orderNo)
                //定义抖音自己的刷脸失效时间
                .setExpireMinutes(properties.getTiktokTaskExpire())
                .setData(data)
                .setAuthorizeInput(resolveRequestContent(initializingContext))
                .build();
    }

    private String resolveRequestContent(FaceAuthorizationInitializingContext authInput) {

        try {
            TiktokProviderFaceInput tiktokProviderFaceInput = new TiktokProviderFaceInput();
            tiktokProviderFaceInput.setName(authInput.getRequest().getName());
            tiktokProviderFaceInput.setIdNo(authInput.getRequest().getIdNo());

            return JsonUtils.obj2json(tiktokProviderFaceInput);
        } catch (Exception cause) {
            log.warn("Fail to serialization on request .", cause);

            return StringUtils.EMPTY;
        }
    }


    /**
     * 获取刷脸票据
     */
    public String getTicket(String openId, String aid, String identityName, String identityCode) {
        return tiktokOpenApiClient.getTicket(openId, aid, identityName, identityCode);
    }

    /**
     * 根据抖音小程序授权码获取open_id
     */
    public String getOpenId(String code) {
        return tiktokOpenApiClient.getOpenId(code);
    }

    /**
     * 抖音的错误码映射天谷错误码信息
     */
    public TiktokFaceResultStatusDomain getStatusByCode(int code, String errMsg) {
        return tiktokOpenApiClient.getStatusByCode(code, errMsg);
    }

}
