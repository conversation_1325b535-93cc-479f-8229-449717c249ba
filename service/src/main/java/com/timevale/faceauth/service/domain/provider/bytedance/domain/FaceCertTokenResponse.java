package com.timevale.faceauth.service.domain.provider.bytedance.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.volcengine.service.visual.model.response.VisualBaseResponse;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/2/21
 */
@Data
public class FaceCertTokenResponse extends VisualBaseResponse {


    @JSONField(name = "data")
    private VatInvoiceData data;

    @Data
    public static class VatInvoiceData {

        /**
         * 本次人脸核身的唯一token
         */
        @JSONField(name = "byted_token")
        private String bytedToken;

        /**
         * 客户端配置信息，请直接透传给端上。
         */
        @JSONField(name = "client_config")
        private String clientConfig;

    }
}
