package com.timevale.faceauth.service.domain.resource;

import com.timevale.faceauth.service.domain.FaceAuthorizationResourceResolver;
import com.timevale.faceauth.service.domain.UserFacePhotoResource;
import com.timevale.faceauth.service.domain.provider.tencent.TencentCloudCertificationQueryPhotosHandler;
import com.timevale.faceauth.service.domain.provider.tencent.TencentCloudCertificationQueryPhotosResponse;
import com.timevale.faceauth.service.domain.provider.tencent.TencentWebAppIdVersion;
import com.timevale.faceauth.service.domain.repository.FaceInfo;
import com.timevale.faceauth.service.utils.task.AsyncTask;
import com.timevale.faceauth.service.utils.task.AsyncTaskFactory;
import com.timevale.mandarin.base.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import static com.timevale.faceauth.service.domain.provider.ConfigurableProviderService.PROVIDER_TENCENT_CLOUD;

/**
 * 供应商资源加载器
 * <AUTHOR>
 * @since 2025/12/23 14:35
 */
@Slf4j
@Component
public class ProviderTencentCloudH5RemotePhotoAllResourceLoader implements ProviderRemoteResourceLoader {


    private static final String TAG = "ALL_PHOTO";
    //获取最大照片数
    private static final int MAX_PHOTO_NUM = 5;

    @Autowired
    private TencentCloudCertificationQueryPhotosHandler queryPhotosHandler;

    @Autowired
    private FaceAuthorizationResourceResolver resourceResolver;
    @Override
    public String getProviderName() {
        return PROVIDER_TENCENT_CLOUD;
    }

    @Override
    public String strategy() {
        return TAG;
    }


    @Override
    public String execute(ProviderResourceBaseContext context) {
        if(!(context instanceof ProviderTencentCloudQueryContext)){
            log.error("ResourceLoader execute fail . the {} {} {}. context is null ",
                    getProviderName(), resourceType().getCode(), strategy());
            return null;
        }
        ProviderTencentCloudQueryContext tenantCloudQueryContext = (ProviderTencentCloudQueryContext)context;
        FaceInfo faceInfo = tenantCloudQueryContext.getFaceInfo();
        String providerOrderNo = tenantCloudQueryContext.getProviderOrderNo();
        TencentWebAppIdVersion appIdVersion = tenantCloudQueryContext.getAppIdVersion();
        asyncSaveAllPhotos(faceInfo, providerOrderNo, appIdVersion);
        return null;
    }



    private void asyncSaveAllPhotos(FaceInfo face,
                                    String orderNo,
                                    TencentWebAppIdVersion appIdVersion) {
        AsyncTaskFactory.execute(new AsyncTask() {
            @Override
            public String name() {
                return AsyncTask.TENCENT_CLOUD_FACE_NAME;
            }
            @Override
            public Boolean exe() {
                return saveFaceAllPhoto(face, orderNo, appIdVersion);
            }
        });
    }

    public boolean saveFaceAllPhoto(FaceInfo face,
                                    String orderNo,
                                    TencentWebAppIdVersion appIdVersion){
        List<UserFacePhotoResource> list = getMultiPhoto(face, orderNo, appIdVersion);
        if (CollectionUtils.isEmpty(list) || list.size() == 1) {
            return true;
        }

        //去除第一张照片，获取刷脸状态接口已经保存了第一张
        List<UserFacePhotoResource> resources = list.subList(1, list.size());
        resourceResolver.saveFaceResource(
                resources.toArray(new UserFacePhotoResource[resources.size()]));
        return true;
    }

    private List<UserFacePhotoResource> getMultiPhoto(
            FaceInfo face,
            String orderNo,
            TencentWebAppIdVersion appIdVersion) {

        String faceId = face.getFaceId();
        long start = System.currentTimeMillis();
        List<UserFacePhotoResource> list = new ArrayList();
        try {

            TencentCloudCertificationQueryPhotosResponse queryPhotosResponse =
                    queryPhotosHandler.invoke(faceId, orderNo, appIdVersion);
            if (queryPhotosResponse != null
                    && queryPhotosResponse.getResult() != null
                    && CollectionUtils.isNotEmpty(queryPhotosResponse.getResult().getPhotoList())) {
                List<String> photoList = queryPhotosResponse.getResult().getPhotoList();
                log.info("tencentCloud query face photo All size={},faceId={}", photoList.size(), faceId);
                if (photoList.size() > MAX_PHOTO_NUM) {
                    photoList.subList(0, MAX_PHOTO_NUM);
                }
                for (String photo : photoList) {
                    if (StringUtils.isEmpty(photo)) {
                        continue;
                    }
                    UserFacePhotoResource photoBestResource = detectUserFacePhotoResource(photo, face);
                    list.add(photoBestResource);
                }
            }
        } catch (Exception e) {
            log.error("tencentCloud query tencent cloud face multi photo fail,faceId=" + faceId, e);
        }
        long end = System.currentTimeMillis();
        log.info("tencentCloud query face photo size={},faceId={}, cost:{} ms", list.size(), faceId,(end - start));
        return list;
    }



    private UserFacePhotoResource detectUserFacePhotoResource(String photo, FaceInfo face) {
        if (StringUtils.isEmpty(photo)) {
            return null;
        }
        String photoKey = resourceResolver.savePhoto(face.getFaceId(), photo);
        if (StringUtils.isEmpty(photoKey)) {
            return null;
        }

        return UserFacePhotoResource.createBuilder()
                .setOriginPhoto(face.getPhoto())
                .setIdNo(face.getIdNo())
                .setName(face.getName())
                .setProvider(face.getProvider())
                .setFaceId(face.getFaceId())
                .setResourceContent(photoKey)
                .setMimeType(face.getPhotoType())
                .build();
    }



    @Override
    public FaceResourceType resourceType() {
        return FaceResourceType.PHOTO;
    }
}
