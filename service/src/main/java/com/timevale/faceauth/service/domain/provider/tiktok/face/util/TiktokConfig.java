package com.timevale.faceauth.service.domain.provider.tiktok.face.util;

import com.alibaba.fastjson.JSON;
import com.timevale.faceauth.service.domain.provider.tiktok.face.domain.TiktokAppCommonDomain;
import com.timevale.faceauth.service.domain.provider.tiktok.face.domain.TiktokAppSecretDomain;
import com.timevale.faceauth.service.domain.provider.tiktok.face.domain.TiktokFaceResultStatusDomain;
import com.timevale.framework.puppeteer.model.ConfigChangeEvent;
import com.timevale.framework.puppeteer.spring.annotation.PuppeteerConfigChangeListener;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2024/5/17 10:49
 */
@Component
@Getter
@Slf4j
public class TiktokConfig {

    /**
     * tiktok 的应用配置
     */
    private TiktokAppSecretDomain tiktokAppSecretDomain;

    @Value("${tiktok.config.tiktokAppSecretDomain:{}}")
    public void setTiktokAppSecretDomain(String result) {
        tiktokAppSecretDomain = JSON.parseObject(result, TiktokAppSecretDomain.class);
        log.info("tiktok.config.tiktokAppSecretDomain new value =  {}", result);
    }

    /**
     * 抖音错误码配置映射
     */
    private List<TiktokFaceResultStatusDomain> tiktokFaceResultStatusDomains = Lists.newArrayList();

    @Value("${tiktok.config.faceResultStatusDomain:[]}")
    public void setTiktokFaceResultStatusDomains(String result) {
        tiktokFaceResultStatusDomains = JSON.parseArray(result, TiktokFaceResultStatusDomain.class);
        log.info("tiktok.config.faceResultStatusDomain new value =  {}", result);
    }

    /**
     * tiktok 的应用配置
     */
    private TiktokAppCommonDomain tiktokAppCommonDomain;

    @Value("${tiktok.config.tiktokAppCommonDomain:{}}")
    public void setTiktokAppCommonDomain(String result) {
        tiktokAppCommonDomain = JSON.parseObject(result, TiktokAppCommonDomain.class);
        log.info("tiktok.config.tiktokAppCommonDomain new value =  {}", result);
    }

    @PuppeteerConfigChangeListener
    private void configChangeListener(ConfigChangeEvent changeEvent) {
        if (changeEvent.isChanged("tiktok.config.tiktokAppCommonDomain")) {
            setTiktokAppCommonDomain(changeEvent.getChange("tiktok.config.tiktokAppCommonDomain").getNewValue());
        }

        if (changeEvent.isChanged("tiktok.config.faceResultStatusDomain")) {
            setTiktokFaceResultStatusDomains(changeEvent.getChange("tiktok.config.faceResultStatusDomain").getNewValue());
        }

        if (changeEvent.isChanged("tiktok.config.tiktokAppSecretDomain")) {
            setTiktokAppSecretDomain(changeEvent.getChange("tiktok.config.tiktokAppSecretDomain").getNewValue());
        }
    }
}
