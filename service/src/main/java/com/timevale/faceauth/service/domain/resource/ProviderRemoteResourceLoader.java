package com.timevale.faceauth.service.domain.resource;

import com.timevale.faceauth.service.domain.provider.ConfigurableProviderService;
import com.timevale.faceauth.service.domain.provider.NameableProviderService;

/**
 * 供应商资源加载器
 * <AUTHOR>
 * @since 2025/12/23 14:35
 */
public interface ProviderRemoteResourceLoader  {


    /**
     * 执行下载资源
     * @param context
     * @return
     */
    String execute(ProviderResourceBaseContext context);

    /**
     * ResourceType
     * @see  FaceResourceType
     * @return
     */
    FaceResourceType resourceType();


    /**
     * 供应商名称
     * @see NameableProviderService#getProviderName()
     * @see ConfigurableProviderService#PROVIDER_BYTEDANCE 等等
     * @return
     */
    String getProviderName();

    /**
     * 执行策略
     * @return
     */
    String strategy();

}
