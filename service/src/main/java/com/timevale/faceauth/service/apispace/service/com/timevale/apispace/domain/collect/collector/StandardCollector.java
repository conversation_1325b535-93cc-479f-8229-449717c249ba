package com.timevale.faceauth.service.apispace.service.com.timevale.apispace.domain.collect.collector;

import com.timevale.faceauth.service.apispace.service.com.timevale.apispace.domain.collect.sink.SlsSink;
import com.timevale.faceauth.service.apispace.service.com.timevale.apispace.domain.collect.store.SlsStore;
import com.timevale.faceauth.service.apispace.service.com.timevale.apispace.domain.collect.store.StandardPushStore;
import com.timevale.faceauth.service.apispace.service.com.timevale.apispace.request.ApiResponseEnhanceMetric;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
@Component
public class StandardCollector extends AbstractCollector<ApiResponseEnhanceMetric> {

  @Autowired
  public StandardCollector(SlsSink slsSink, StandardPushStore standardPushStore) {
    super(slsSink, standardPushStore);
  }
}
