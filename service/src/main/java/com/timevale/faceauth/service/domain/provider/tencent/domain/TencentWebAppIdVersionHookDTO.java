package com.timevale.faceauth.service.domain.provider.tencent.domain;

import com.timevale.faceauth.service.domain.provider.ConfigurableProviderService;
import com.timevale.faceauth.service.domain.provider.tencent.TencentWebAppIdVersion;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE 2024/8/26 10:28
 */
@Data
public class TencentWebAppIdVersionHookDTO extends ToString implements TencentWebAppIdVersion {


    public static final String ABILITY_H5 = "H5";
    public static final String ABILITY_SDK = "SDK";

    //接口版本号
    private String version;
    //运营支持平台code
    private String code;
    //应用场景
    private String scene;
    //应用场景
    private String sceneCode;
    /**
     * 产品能力
     */
    private String ability;
    /**
     * 供应商类型
     *
     * @see ConfigurableProviderService#PROVIDER_FACE_TENCENT_SDK_BASIC
     */
    private String providerName;
    //webAccess 配置
    private TencentWebAccessHolderHookDTO accessHolder;


}
