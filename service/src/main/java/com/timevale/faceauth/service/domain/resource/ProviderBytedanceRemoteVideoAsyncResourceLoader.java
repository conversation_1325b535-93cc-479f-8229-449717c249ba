package com.timevale.faceauth.service.domain.resource;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.timevale.faceauth.service.domain.provider.ConfigurableProviderService.PROVIDER_BYTEDANCE;

/**
 * 供应商资源加载器
 * <AUTHOR>
 * @since 2025/12/23 14:35
 */
@Slf4j
@Component
public class ProviderBytedanceRemoteVideoAsyncResourceLoader extends ProviderBytedanceRemoteResourceAbstractService {



    @Override
    public String getProviderName() {
        return PROVIDER_BYTEDANCE;
    }
    @Override
    public String strategy() {
        return ResourceStrategyEnum.ASYNC.getCode();
    }



    @Override
    public String execute(ProviderResourceBaseContext context) {
        ProviderQueryContext queryContext = super.revokerContext(context);
        if(queryContext == null){
            super.logExecuteContextNull();
            return null;
        }

        return super.asyncTask(queryContext);
    }



    @Override
    public FaceResourceType resourceType() {
        return FaceResourceType.VIDEO;
    }
}
