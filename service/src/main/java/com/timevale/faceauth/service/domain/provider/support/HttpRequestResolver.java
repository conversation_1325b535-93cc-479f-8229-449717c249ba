package com.timevale.faceauth.service.domain.provider.support;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.utils.providerLog.AbstractProviderLogResultResolver;
import com.timevale.faceauth.service.utils.providerLog.LogConsumer;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;

import java.net.URI;

/**
 * <AUTHOR>
 */
public interface HttpRequestResolver{

    URI resolveURI(String uriString) throws FaceException;

    <T> ResponseEntity<T> resolveResponse(
            String faceId, String provider, RequestEntity requestEntity, Class<T> responseType)
            throws FaceException ;

     <T> ResponseEntity<T> resolveResponse(
            String faceId, String provider, RequestEntity requestEntity, Class<T> responseType, AbstractProviderLogResultResolver providerLogResultResolver, LogConsumer<AbstractProviderLogResultResolver, RequestEntity<?>, ResponseEntity<String>> logConsumer, LogConsumer<AbstractProviderLogResultResolver, RequestEntity<?>, Exception> logExceptionConsumer) throws FaceException;
}
