package com.timevale.faceauth.service.inner.impl.sdk.config;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2024/8/8 10:07
 */
@Data
public class SdkCustomerConfigDomain extends ToString {

    /**
     * 渠道列表
     */
    private List<ConfigItem> sdkChannels;


    private String urlDomain;
    @Data
    public static class ConfigItem extends ToString {
        private String status;
        private String name;
        private String appId;
        private String code;
    }
}
