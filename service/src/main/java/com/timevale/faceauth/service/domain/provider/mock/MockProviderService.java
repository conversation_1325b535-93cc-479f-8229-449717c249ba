package com.timevale.faceauth.service.domain.provider.mock;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.support.FaceQueryableExtend;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.domain.provider.ConfigurableProviderService;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationData;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationResult;
import com.timevale.faceauth.service.domain.provider.support.AbstractProviderFaceAuthorizationResult;
import com.timevale.faceauth.service.domain.provider.support.AbstractProviderService;
import com.timevale.faceauth.service.domain.provider.support.FaceAuthorizationInitializingContext;
import com.timevale.faceauth.service.domain.repository.*;
import com.timevale.faceauth.service.domain.support.DnsResolver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.UUID;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/16 16
 */
@Component
public class MockProviderService extends AbstractProviderService {


    @Autowired
    public MockProviderService(
            FaceRepository faceRepository,
            ProviderFaceRepository providerFaceRepository,
            DnsResolver dnsResolver,
            ConfigurableProperties configurableProperties) {
    super(
        faceRepository,
        providerFaceRepository,
        dnsResolver,
        configurableProperties);
  }

  @Override
  public String getProviderName() {
    return ConfigurableProviderService.PROVIDER_MOCK;
  }

  @Override
  public String getFullName() {
    return ConfigurableProviderService.FULL_NAME_PROVIDER_MOCK;
  }

  @Override
  protected ProviderFaceAuthorizationData doInitialize(
      FaceAuthorizationInitializingContext initializingContext) throws FaceException {
    return ProviderFaceAuthorizationData.createBuilder()
        .setAuthorizeInput("{}")
        .setData("MOCK_DATA")
        .setOrderNo(UUID.randomUUID().toString().replace("-", ""))
        .setProvider(getProviderName())
        .setProviderName(getFullName())
        .setThirdpartId("MOCK_ID")
        .build();
  }

  @Override
  protected ProviderFaceAuthorizationResult doQueryAuthorizeResult(
      FaceQueryableExtend extend, FaceInfo faceInfo, ProviderFaceInfo providerFaceInfo)
      throws FaceException {
    return mockProviderFaceAuthorizationResult(providerFaceInfo.getFaceId());
  }

  private MockProviderFaceAuthorizationResult mockProviderFaceAuthorizationResult(String faceId) {
    return (new MockProviderFaceAuthorizationResult(
        getProviderName(), faceId, "{\"code\":0,\"msg\":,\"state\":true}"));
  }

  @Override
  protected ProviderFaceAuthorizationResult detectFaceAuthorizationResultOnReturn(
      String faceId, HttpServletRequest request) throws FaceException {
    return mockProviderFaceAuthorizationResult(faceId);
  }

  @Override
  protected byte[] extractRequestData(HttpServletRequest request) throws FaceException {
    return (new byte[0]);
  }

  @Override
  protected ProviderFaceAuthorizationResult detectFaceAuthorizationResultOnCallback(
      String faceId, byte[] dataBuffer, HttpServletRequest request) throws FaceException {
    return mockProviderFaceAuthorizationResult(faceId);
  }

  @Override
  protected AbstractProviderFaceAuthorizationResult resolveDoneAuthorizationResult(
      ProviderReturnInfo providerReturn,
      FaceQueryableExtend extend,
      FaceInfo faceInfo,
      ProviderFaceInfo providerFaceInfo)
      throws FaceException {
    return null;
  }
}
