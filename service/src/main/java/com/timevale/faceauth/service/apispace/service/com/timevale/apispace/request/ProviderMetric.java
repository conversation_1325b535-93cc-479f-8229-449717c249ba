package com.timevale.faceauth.service.apispace.service.com.timevale.apispace.request;

import com.timevale.mandarin.common.result.ToString;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/6/25 11:24
 */
@Data
@Builder
public class ProviderMetric  extends ToString {

    private long timestamp;
    private String userId;
    private String bizId;
    private String bizType;
    private String bizTitle;
    private String action;
    private String markStatus;
    private long costTime;
    private String providerKey;
    private String providerTitle;
    private String providerOrderNo;
    private String providerCode;
    private String providerMsg;
    private int codeMsgFirst;
    private int codeMsgFocusOn;
    private int codeMsgSensitive;
    private String tag;


}
