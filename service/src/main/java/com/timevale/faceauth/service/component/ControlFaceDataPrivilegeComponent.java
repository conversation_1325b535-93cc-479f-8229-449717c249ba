package com.timevale.faceauth.service.component;

import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.domain.controll.FaceUserResourceControlCheckDTO;
import com.timevale.faceauth.service.inner.FaceUserResourceControlInnerService;
import com.timevale.faceauth.service.input.FaceAuthResInput;
import com.timevale.faceauth.service.input.FaceAuthorizationDetailQueryInput;
import com.timevale.faceauth.service.input.control.ControlFaceDataPrivilegeInput;
import com.timevale.faceauth.service.result.FaceAuthorizationDetailResult;
import com.timevale.faceauth.service.result.FaceAuthorizationProviderResponse;
import com.timevale.faceauth.service.result.QueryFaceAuthResResult;
import com.timevale.faceauth.service.result.SupportResult;
import com.timevale.faceauth.service.result.control.ControlFaceDataPrivilegeResult;
import com.timevale.faceauth.service.utils.exceptions.IgnoreExceptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @DATE 2025/7/7 10:57
 */
@Component
@Slf4j
public class ControlFaceDataPrivilegeComponent {


    @Autowired
    private ConfigurableProperties configurableProperties;

    private static final String NOT_RETURN_DATE = null;


    @Autowired
    private FaceUserResourceControlInnerService faceUserResourceControlInnerService;

    public SupportResult<QueryFaceAuthResResult>  controlData(FaceAuthResInput input, SupportResult<QueryFaceAuthResResult> result) {
        return IgnoreExceptionUtil.ofExceptionable(() -> {
            if (configurableProperties.isConfigurableSkipFaceUserResourceControlAll()) {
                return result;
            }
            if (Objects.isNull(input) || Objects.isNull(result)) {
                return result;
            }

            QueryFaceAuthResResult faceAuthResResult = result.getData();
            if (Objects.isNull(faceAuthResResult)) {
                return result;
            }

            ControlFaceDataPrivilegeInput privilegeInput = ObjectUtils.defaultIfNull(input.getControlFaceDataPrivilegeInput(), new ControlFaceDataPrivilegeInput());


            // 2025/7/7  减少一次SQL 查询
            if (ControlFaceDataPrivilegeInput.privilege(privilegeInput.getPhotoDataPrivilege())) {
                return result;
            }

            // 2025/7/7  只返回照片
            final String faceId = input.getFaceAuthId();
            FaceUserResourceControlCheckDTO build = FaceUserResourceControlCheckDTO.builder().faceId(faceId).build();
            ControlFaceDataPrivilegeResult privilegeResult = faceUserResourceControlInnerService.checkUserResourceControl(build);
            if (privilegeResult.isRefusePhotoAndVideo()) {
                faceAuthResResult.setPhoto(NOT_RETURN_DATE);
                faceAuthResResult.setPrivilegeResult(privilegeResult);
                log.info("controlData 刷脸合规 不返回刷脸照片视频 ：faceId : {} , name:{}", faceId, privilegeResult.getName());
            }
            return result;
        }).orElse(result);
    }

    public SupportResult<FaceAuthorizationDetailResult> controlData(FaceAuthorizationDetailQueryInput input, SupportResult<FaceAuthorizationDetailResult> result) {
        return IgnoreExceptionUtil.ofExceptionable(() -> {
            if (configurableProperties.isConfigurableSkipFaceUserResourceControlAll()) {
                return result;
            }
            if (Objects.isNull(input) || Objects.isNull(result)) {
                return result;
            }

            FaceAuthorizationDetailResult faceAuthResResult = result.getData();
            if (Objects.isNull(faceAuthResResult)) {
                return result;
            }

            ControlFaceDataPrivilegeInput privilegeInput = ObjectUtils.defaultIfNull(input.getControlFaceDataPrivilegeInput(), new ControlFaceDataPrivilegeInput());


            // 2025/7/7  减少一次SQL 查询
            if (ControlFaceDataPrivilegeInput.privilege(privilegeInput.getVideoDataPrivilege()) && ControlFaceDataPrivilegeInput.privilege(privilegeInput.getPhotoDataPrivilege())) {
                return result;
            }


            String faceId = input.getFaceId();
            FaceUserResourceControlCheckDTO build = FaceUserResourceControlCheckDTO.builder().faceId(faceId).build();
            ControlFaceDataPrivilegeResult privilegeResult = faceUserResourceControlInnerService.checkUserResourceControl(build);

            if (privilegeResult.isRefusePhotoAndVideo()) {

                FaceAuthorizationProviderResponse response = faceAuthResResult.getProviderResponse();

                if (!ControlFaceDataPrivilegeInput.privilege(privilegeInput.getVideoDataPrivilege())) {
                    // 2025/7/7  不返回视频
                    response.setVideo(NOT_RETURN_DATE);
                }
                
                if (!ControlFaceDataPrivilegeInput.privilege(privilegeInput.getPhotoDataPrivilege())) {
                    // 2025/7/7  不返回照片
                    faceAuthResResult.getProviderOrder().setPhoto(NOT_RETURN_DATE);
                    response.setPhoto(NOT_RETURN_DATE);
                    response.setPhotoAll(Lists.newArrayList());
                }

                faceAuthResResult.setPrivilegeResult(privilegeResult);
                log.info("controlData 刷脸合规 不返回刷脸照片视频 ：faceId : {} , name:{}", faceId, privilegeResult.getName());
            }
            return result;
        }).orElse(result);
    }
}
