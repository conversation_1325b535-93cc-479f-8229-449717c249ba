package com.timevale.faceauth.service.domain.provider.audiovideodual.esign;

import com.alibaba.fastjson.JSON;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.support.FaceQueryableExtend;
import com.timevale.faceauth.service.domain.ConfigurableFaceAuthorizationCompletedInvocationHandler;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.domain.processor.FaceRequestContext;
import com.timevale.faceauth.service.domain.processor.FaceReturnProcessor;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationData;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationResult;
import com.timevale.faceauth.service.domain.provider.audiovideodual.AudioVideoDualFaceApplyV2InvocationHandler;
import com.timevale.faceauth.service.domain.provider.audiovideodual.AudioVideoDualFaceAuthorizationResult;
import com.timevale.faceauth.service.domain.provider.audiovideodual.AudioVideoDualFaceQueryV2InvocationHandler;
import com.timevale.faceauth.service.domain.provider.support.*;
import com.timevale.faceauth.service.domain.provider.tencent.TencentCloudFaceResultStatus;
import com.timevale.faceauth.service.domain.repository.*;
import com.timevale.faceauth.service.domain.support.DnsResolver;
import com.timevale.faceauth.service.enums.EsignFaceStatusEnum;
import com.timevale.faceauth.service.exception.ProviderException;
import com.timevale.faceauth.service.thirdparty.audiovideodual.context.AudioVideoDualQueryResult;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mediaauth.facade.enums.VideoChannel;
import com.timevale.mediaauth.facade.enums.VideoClientType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * 基于H5智能视频认证的刷脸认证服务实现
 *
 * <AUTHOR>
 * @copyright 2024
 * @date 2024/9/8
 */
@Slf4j
@Component
public class EsignVideoDualFaceService extends AbstractProviderService {


  @Autowired private AudioVideoDualFaceApplyV2InvocationHandler applyV2InvocationHandler;
  @Autowired private AudioVideoDualFaceQueryV2InvocationHandler queryV2InvocationHandler;
  @Autowired private ProviderFaceRepository providerFaceRepository;
  @Autowired private FaceRepository faceRepository;

  public EsignVideoDualFaceService(
      FaceRepository faceRepository,
      ProviderFaceRepository providerFaceRepository,
      DnsResolver dnsResolver,
      ConfigurableProperties configurableProperties) {
    super(faceRepository, providerFaceRepository, dnsResolver, configurableProperties);
  }

  @Override
  public String getProviderName() {
    return PROVIDER_AUDIO_VIDEO_ESIGN;
  }

  @Override
  public String getFullName() {
    return FULL_NAME_PROVIDER_AUDIO_VIDEO_ESIGN;
  }

  @Override
  protected ProviderFaceAuthorizationData doInitialize(
      FaceAuthorizationInitializingContext initializingContext) throws FaceException {
    ProviderFaceAuthorizationData authorizationData =
        applyV2InvocationHandler.invoke(
            VideoClientType.H5, VideoChannel.H5_MEGVII,
                initializingContext, this);
    return authorizationData;
  }

  @Override
  protected ProviderFaceAuthorizationResult doQuery(
      String completedType,
      FaceQueryableExtend extend,
      FaceInfo faceInfo,
      ProviderFaceInfo providerFaceInfo)
      throws FaceException {
    return super.doQuery(
        ConfigurableFaceAuthorizationCompletedInvocationHandler.TYPE_COMPLETED_CALLBACK,
        extend,
        faceInfo,
        providerFaceInfo);
  }

  @Override
  protected AbstractProviderFaceAuthorizationResult resolveDoneAuthorizationResult(
      ProviderReturnInfo providerReturn,
      FaceQueryableExtend extend,
      FaceInfo faceInfo,
      ProviderFaceInfo providerFaceInfo)
      throws FaceException {
    return resolveResult(faceInfo, providerFaceInfo);
  }

  @Override
  protected ProviderFaceAuthorizationResult detectFaceAuthorizationResultOnReturn(
      String faceId, HttpServletRequest request) throws FaceException {
    return resolveResult(faceId);
  }

  private AbstractProviderFaceAuthorizationResult resolveResult(
      FaceInfo faceInfo, ProviderFaceInfo providerFaceInfo) {
    String faceId = faceInfo.getFaceId();
    String orderNo = providerFaceInfo.getOrderNo();
    String version = faceInfo.getProviderApiVersion();
    AudioVideoDualQueryResult queryResult = queryV2InvocationHandler.invoke(VideoClientType.H5, faceId, orderNo);

    AudioVideoDualFaceAuthorizationResult authorizationResult = buildFaceResult(queryResult);
    return authorizationResult;
  }

  private AbstractProviderFaceAuthorizationResult resolveResult(String faceId) {
    ProviderFaceInfo providerFaceInfo = providerFaceRepository.getByFaceId(faceId);
    FaceInfo faceInfo = faceRepository.getFaceInfoByFaceId(faceId);
    return resolveResult(faceInfo, providerFaceInfo);
  }

  @Override
  protected byte[] extractRequestData(HttpServletRequest request) throws FaceException {
    // obtain data with query, and do nothing .
    return "SUCCESS".getBytes();
  }

  @Override
  protected ProviderFaceAuthorizationResult detectFaceAuthorizationResultOnCallback(
      String faceId, byte[] dataBuffer, HttpServletRequest request) throws FaceException {
    return resolveResult(faceId);
  }

  @Override
  protected ProviderFaceAuthorizationResult doQueryAuthorizeResult(
      FaceQueryableExtend extend, FaceInfo faceInfo, ProviderFaceInfo providerFaceInfo)
      throws FaceException {
    String faceId = faceInfo.getFaceId();
    String orderNo = providerFaceInfo.getOrderNo();
    AudioVideoDualQueryResult queryResult = queryV2InvocationHandler.invoke(VideoClientType.H5, faceId, orderNo);
    AudioVideoDualFaceAuthorizationResult authorizationResult = buildFaceResult(queryResult);
    return authorizationResult;
  }

  @Override
  protected String deduceReturnUrl(
      String returnServiceUrl,
      FaceRequestContext requestContext,
      FaceReturnProcessor faceReturnProcessor) {
    return super.deduceReturnUrl(returnServiceUrl, requestContext, faceReturnProcessor);
  }

  private AudioVideoDualFaceAuthorizationResult buildFaceResult(
      AudioVideoDualQueryResult queryResult) {
    String faceId = queryResult.getBizId();

    boolean isSuccess = queryResult.getResult() == EsignFaceStatusEnum.SUCCESS.getStatus();
    String content =
        queryResult.getExtend() == null ? null : JSON.toJSONString(queryResult.getExtend());
    boolean isCompleted = queryResult.isCompleted();
    String errorCode = queryResult.getErrorCode();
    ProviderException providerEx = null;
    if(!isSuccess && StringUtils.isNotBlank(errorCode)){
      VideoCompletedStatus completedStatus = VideoCompletedStatus.errorCodeOf(errorCode);
      //定制处理，计费错误码才标记为完成
      isCompleted = completedStatus.getErrorCodeEnum().isFee();
      providerEx = new ProviderException(completedStatus.getFaceCode(), completedStatus);
    }

    return AudioVideoDualFaceAuthorizationResult.createBuilder(getProviderName())
        .setFaceId(faceId)
        .setCompleted(isCompleted)
        .setSuccess(isSuccess)
        .setTimestamp(queryResult.getCompletedTimeMillis())
        .setPhoto(queryResult.getPhoto())
        .setVideo(queryResult.getVideo())
        .setFee(queryResult.isProviderFee())
        .setContent(content)
        .setError(providerEx)
        .build();
  }
}
