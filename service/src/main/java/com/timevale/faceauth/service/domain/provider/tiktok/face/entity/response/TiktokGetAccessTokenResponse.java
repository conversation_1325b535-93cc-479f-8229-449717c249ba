package com.timevale.faceauth.service.domain.provider.tiktok.face.entity.response;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE 2024/5/13 18:14
 */
@Data
public class TiktokGetAccessTokenResponse  extends TiktokAbstractResponse {

    private int err_no;
    private String err_tips;
    private Data data;



    @lombok.Data
    public static class Data extends ToString {
        /**
         * 获取的 access_tokenTip: token 是小程序级别 token，不要为每个用户单独分配一个 token，会导致 token 校验失败。建议每小时更新一次即可。
         */
        private String access_token;
        /**
         * access_token 有效时间，单位：秒
         */
        private int expires_in;
    }

}
