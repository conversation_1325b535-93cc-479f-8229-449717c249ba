package com.timevale.faceauth.service.domain.provider.tencentsdks;

import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.domain.repository.FaceRepository;
import com.timevale.faceauth.service.domain.repository.FaceSwitchRepository;
import com.timevale.faceauth.service.domain.repository.ProviderFaceRepository;
import com.timevale.faceauth.service.domain.support.DnsResolver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @DATE 2024/8/12 17:20
 */
@Service
public class TencentSdkPlusService extends TencentSdkBasicService {

    @Autowired
    public TencentSdkPlusService(FaceRepository faceRepository,
                                 ProviderFaceRepository providerFaceRepository,
                                 DnsResolver dnsResolver,
                                 ConfigurableProperties configurableProperties,
                                 FaceSwitchRepository faceSwitchRepository) {
        super(faceRepository, providerFaceRepository, dnsResolver, configurableProperties, faceSwitchRepository);
    }


    @Override
    public String getProviderName() {
        return PROVIDER_FACE_TENCENT_SDK_PLUS;
    }

    @Override
    public String getFullName() {
        return FULL_NAME_PROVIDER_FACE_TENCENT_SDK_PLUS;
    }


}
