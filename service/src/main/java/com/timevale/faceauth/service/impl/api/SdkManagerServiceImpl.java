package com.timevale.faceauth.service.impl.api;

import com.alibaba.fastjson.JSON;
import com.timevale.faceauth.service.api.SdkManagerService;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.event.FaceEventPublisher;
import com.timevale.faceauth.service.domain.event.sdk.AsyncSdkPointEvent;
import com.timevale.faceauth.service.domain.sdk.SdkChannelDomain;
import com.timevale.faceauth.service.enums.SdkStatusEnum;
import com.timevale.faceauth.service.inner.SdkManagerInnerService;
import com.timevale.faceauth.service.input.sdk.BatchSaveCustomerChannelInput;
import com.timevale.faceauth.service.input.sdk.GetManualConfigInput;
import com.timevale.faceauth.service.input.sdk.GetSdkCustomerDetailInput;
import com.timevale.faceauth.service.input.sdk.QuerySdkCustomerListInput;
import com.timevale.faceauth.service.input.sdk.QuerySdkVersionListInput;
import com.timevale.faceauth.service.input.sdk.SaveSdkVersionInput;
import com.timevale.faceauth.service.input.sdk.UpdateBundleIdIdInput;
import com.timevale.faceauth.service.input.sdk.UpdateSdkVersionStatusInput;
import com.timevale.faceauth.service.input.sdk.VerifyAndQueryChannelSdkInput;
import com.timevale.faceauth.service.result.BaseFaceAuthResult;
import com.timevale.faceauth.service.result.SupportResult;
import com.timevale.faceauth.service.result.sdk.BatchSaveCustomerChannelResult;
import com.timevale.faceauth.service.result.sdk.GetManualConfigResult;
import com.timevale.faceauth.service.result.sdk.GetSdkCustomerDetailResult;
import com.timevale.faceauth.service.result.sdk.QuerySdkCustomerListResult;
import com.timevale.faceauth.service.result.sdk.QuerySdkVersionListResult;
import com.timevale.faceauth.service.result.sdk.SaveSdkVersionResult;
import com.timevale.faceauth.service.result.sdk.UpdateSdkVersionStatusResult;
import com.timevale.faceauth.service.result.sdk.VerifyAndQueryChannelSdkResult;
import com.timevale.faceauth.service.utils.FaceAuthValidationUtils;
import com.timevale.mandarin.common.annotation.RestService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2024/8/6 11:25
 */
@Slf4j
@RestService
@Api(tags = "SDK管理接口")
public class SdkManagerServiceImpl implements SdkManagerService {

    @Autowired
    private SdkManagerInnerService sdkManagerInnerService;

    /**
     * 出参
     *
     * @param input
     * @return
     */
    @Override
    public SupportResult<String> verifyAndQueryChannelList(VerifyAndQueryChannelSdkInput input) {
        FaceAuthValidationUtils.notNull(input, FaceException.valueOf(FaceStatusCode.NULL_ARGUMENT, "input不能为空"));
        FaceAuthValidationUtils.validateBean(input);

        final Pair<VerifyAndQueryChannelSdkResult, String> pair = sdkManagerInnerService.verifyAndQueryChannelList(input.getOrgGid(), input.getSdkVersion(), input.getBundleId());
        FaceEventPublisher.publishEvent(new AsyncSdkPointEvent(input.getOrgGid(), input.getSdkVersion(), input.getBundleId(), pair.getRight()));
        return SupportResult.success(JSON.toJSONString(pair.getLeft()));
    }

    @Override
    public SupportResult<SaveSdkVersionResult> saveSdkVersion(SaveSdkVersionInput input) {
        FaceAuthValidationUtils.notNull(input, FaceException.valueOf(FaceStatusCode.NULL_ARGUMENT, "input不能为空"));
        FaceAuthValidationUtils.validateBean(input);
        String id = sdkManagerInnerService.saveSdkVersion(input);
        //
        SaveSdkVersionResult result = new SaveSdkVersionResult();
        result.setId(id);
        return SupportResult.success(result);
    }

    /**
     * 查询SDK版本列表
     *
     * @param input
     * @return
     */
    @Override
    public SupportResult<QuerySdkVersionListResult> querySdkVersionList(QuerySdkVersionListInput input) {
        FaceAuthValidationUtils.notNull(input, FaceException.valueOf(FaceStatusCode.NULL_ARGUMENT, "input不能为空"));
        QuerySdkVersionListResult result = sdkManagerInnerService.querySdkVersionList(input);
        return SupportResult.success(result);
    }

    /**
     * 更新SDK的状态 ，敏感接口  停用后直接导致SDK不可用 慎用
     *
     * @param input
     * @return
     */
    @Override
    public SupportResult<UpdateSdkVersionStatusResult> updateSdkVersionStatus(UpdateSdkVersionStatusInput input) {
        FaceAuthValidationUtils.notNull(input, FaceException.valueOf(FaceStatusCode.NULL_ARGUMENT, "input不能为空"));
        FaceAuthValidationUtils.validateBean(input);
        FaceAuthValidationUtils.notNull(SdkStatusEnum.of(input.getStatus()), FaceException.valueOf(FaceStatusCode.ILLEGAL_ARGUMENT, "status 不合法"));

        sdkManagerInnerService.updateSdkVersionStatus(input);
        return SupportResult.success(new UpdateSdkVersionStatusResult());
    }

    /**
     * 查询集成SDK的客户列表
     *
     * @param input
     * @return
     */
    @Override
    public SupportResult<QuerySdkCustomerListResult> querySdkCustomerList(QuerySdkCustomerListInput input) {
        FaceAuthValidationUtils.notNull(input, FaceException.valueOf(FaceStatusCode.NULL_ARGUMENT, "input不能为空"));
        QuerySdkCustomerListResult result = sdkManagerInnerService.querySdkCustomerList(input);
        return SupportResult.success(result);
    }

    /**
     * 批量新增客户SDK
     *
     * @param input
     * @return
     */
    @Override
    public SupportResult<BatchSaveCustomerChannelResult> batchSaveCustomerChannel(BatchSaveCustomerChannelInput input) {
        checkParam(input);
        String sdkCustomerId = sdkManagerInnerService.batchSaveCustomerChannel(input);
        return SupportResult.success(BatchSaveCustomerChannelResult.builder().sdkCustomerId(sdkCustomerId).build());
    }

    private void checkParam(BatchSaveCustomerChannelInput input) {
        FaceAuthValidationUtils.notNull(input, FaceException.valueOf(FaceStatusCode.NULL_ARGUMENT, "input不能为空"));
        FaceAuthValidationUtils.validateBean(input);

        //确保ChannelCode 有效
        List<SdkChannelDomain> channelDomains = Optional.ofNullable(sdkManagerInnerService.queryAllSdkChannel()).orElse(Collections.emptyList());
        List<String> useChannels = channelDomains.stream().map(SdkChannelDomain::getChannelCode).collect(Collectors.toList());
        for (BatchSaveCustomerChannelInput.BatchSaveCustomerChannelItem batchSaveCustomerChannelItem : input.getCustomerChannelList()) {
            FaceAuthValidationUtils.validateBean(batchSaveCustomerChannelItem);
            FaceAuthValidationUtils.assertTrue(useChannels.contains(batchSaveCustomerChannelItem.getChannelCode()),
                    FaceException.valueOf(FaceStatusCode.ILLEGAL_ARGUMENT, "channelCode不合法"));
        }

        //确保ChannelCode 不被重复添加
        Map<String, Long> channelCodeCount = input.getCustomerChannelList().stream().collect(Collectors.groupingBy(BatchSaveCustomerChannelInput.BatchSaveCustomerChannelItem::getChannelCode, Collectors.counting()));
        for (Map.Entry<String, Long> entry : channelCodeCount.entrySet()) {
            FaceAuthValidationUtils.assertTrue(entry.getValue() == 1, FaceException.valueOf(FaceStatusCode.ILLEGAL_ARGUMENT, "入参" + entry.getKey() + "重复"));
        }


        // 获取入参中的customerChannelList，并使用stream流将bundleId提取出来，去重后计算数量
        long bundleDistinctCount = input.getCustomerChannelList().stream().map(BatchSaveCustomerChannelInput.BatchSaveCustomerChannelItem::getBundleId).distinct().count();
        // 断言bundleDistinctCount的值为1，如果不为1，则抛出异常，异常信息为"入参bundleId存在不一致情况，请联系管理员处理"
        FaceAuthValidationUtils.assertTrue(bundleDistinctCount == 1, FaceException.valueOf(FaceStatusCode.ILLEGAL_ARGUMENT, "入参bundleId存在不一致情况，请联系管理员处理"));
    }

    @Override
    public SupportResult<GetSdkCustomerDetailResult> getSdkCustomerDetail(GetSdkCustomerDetailInput input) {
        FaceAuthValidationUtils.notNull(input, FaceException.valueOf(FaceStatusCode.NULL_ARGUMENT, "input不能为空"));
        FaceAuthValidationUtils.validateBean(input);
        GetSdkCustomerDetailResult result = sdkManagerInnerService.getSdkCustomerDetail(input);
        return SupportResult.success(result);
    }

    @Override
    public SupportResult<GetManualConfigResult> getManualConfig(GetManualConfigInput input) {
        GetManualConfigResult result = sdkManagerInnerService.getManualConfig(input);
        return SupportResult.success(result);
    }

    /**
     * 更新BundleIdId接口的实现方法
     * 该方法用于处理更新BundleIdId的请求，主要负责验证输入参数的合法性，
     * 并调用内部SDK管理服务进行实际的更新操作
     *
     * @param input 更新BundleIdId的输入参数，包含需要更新的BundleIdId相关信息
     * @return 返回一个SupportResult对象，包含执行结果状态和可能的错误信息
     */
    @Override
    public SupportResult<BaseFaceAuthResult> updateBundleIdId(UpdateBundleIdIdInput input) {
        // 验证输入参数的合法性
        FaceAuthValidationUtils.validateBean(input);
        // 调用内部SDK管理服务进行实际的更新操作
        sdkManagerInnerService.updateBundleIdId(input);
        // 返回成功结果，无需返回具体数据
        return SupportResult.success(null);
    }

}

