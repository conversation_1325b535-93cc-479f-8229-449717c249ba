package com.timevale.faceauth.service.constant;

import java.util.Objects;

/**
 * 数据权限
 * 1,返回状态
 * 2,返回状态+基本信息
 * 3,返回状态+基本信息+照片
 * 4,返回状态+基本信息+企业打款信息
 * 5,返回状态+基本信息+照片+视频
 * 6,返回状态+基本信息+视频
 * <AUTHOR>
 * @since 2022-01-14 15:59
 */
public enum DataPrivilegeEnum {
  DEFAULT(1, false, false,"返回状态"),
  BASE_INFO(2, false, false,"返回状态+基本信息"),
  BASE_INFO_PHOTO(3, true, false,"返回状态+基本信息+照片"),
  BASE_INFO_TRANSFER(4, false,false, "返回状态+基本信息+企业打款信息"),
  BASE_INFO_PHOTO_VIDEO(5, true, true,"返回状态+基本信息+照片+视频"),
  BASE_INFO_VIDEO(6, false, true,"返回状态+基本信息+视频"),
  BASE_INFO_PHOTO_IDNO(7, true, false,"返回状态+基本信息+照片+身份证"),
  ;
  private int code;
  private boolean ifHasPhoto;
  private boolean ifHasVideo;
  private String desc;

  DataPrivilegeEnum(int code, boolean ifHasPhoto, boolean ifHasVideo, String desc) {
    this.code = code;
    this.ifHasPhoto = ifHasPhoto;
    this.ifHasVideo = ifHasVideo;
    this.desc = desc;
  }

  public int getCode() {
    return code;
  }

  public boolean ifHasPhoto() {
    return ifHasPhoto;
  }

  public boolean ifHasVideo() {
    return ifHasVideo;
  }

  public static DataPrivilegeEnum codeOf(Integer dataPrivilege) {
    for (DataPrivilegeEnum privilegeEnum : DataPrivilegeEnum.values()) {
      if (Objects.equals(privilegeEnum.code,dataPrivilege)) {
        return privilegeEnum;
      }
    }

    return DEFAULT;
  }
}
