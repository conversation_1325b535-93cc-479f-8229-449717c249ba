package com.timevale.faceauth.service.convertor;

import com.timevale.faceauth.dal.pfs.dataobject.SdkCustomerChannelMappingDO;
import com.timevale.faceauth.dal.pfs.dataobject.SdkCustomerDO;
import com.timevale.faceauth.dal.pfs.dataobject.SdkCustomerVersionMappingDO;
import com.timevale.faceauth.dal.pfs.dataobject.SdkVersionDO;
import com.timevale.faceauth.dal.pfs.dataobject.query.SdkCustomerDOQuery;
import com.timevale.faceauth.service.domain.sdk.SdkChannelDomain;
import com.timevale.faceauth.service.enums.SdkStatusEnum;
import com.timevale.faceauth.service.input.sdk.QuerySdkCustomerListInput;
import com.timevale.faceauth.service.result.sdk.GetManualConfigResult;
import com.timevale.faceauth.service.result.sdk.GetSdkCustomerDetailResult;
import com.timevale.faceauth.service.result.sdk.QuerySdkCustomerListResult;
import com.timevale.faceauth.service.result.sdk.QuerySdkVersionListResult;
import com.timevale.framework.sands.Sahara;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.DateUtils;
import org.assertj.core.util.Lists;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2024/8/6 15:57
 */
public class SdkCustomerConvert {
    public static final String SEQ = ",";

    public static String getSdkCustomerId() {
        return "isc-" + Sahara.instance.getHexSand();
    }

    public static String getSdkCustomerChannelId() {
        return "iscc-" + Sahara.instance.getHexSand();
    }


    public static String getSdkVersionId() {
        return "isv-" + Sahara.instance.getHexSand();
    }

    public static String getSdkCustomerVersionId() {
        return "iscv-" + Sahara.instance.getHexSand();
    }

    public static QuerySdkVersionListResult convert(Long count, List<SdkVersionDO> sdkVersionDOS) {

        QuerySdkVersionListResult result = new QuerySdkVersionListResult();
        result.setCount(Optional.ofNullable(count).orElse(0L));

        List<QuerySdkVersionListResult.SdkVersionItem> items = Optional.ofNullable(sdkVersionDOS).orElse(Collections.emptyList())
                .stream().map(e -> {
                    QuerySdkVersionListResult.SdkVersionItem sdkVersionItem = new QuerySdkVersionListResult.SdkVersionItem();
                    sdkVersionItem.setSdkVersionId(e.getSdkVersionId());
                    sdkVersionItem.setSdkVersion(e.getSdkVersion());
                    sdkVersionItem.setStatus(e.getStatus());
                    sdkVersionItem.setStatusDesc(SdkStatusEnum.ofDesc(e.getStatus()));
                    sdkVersionItem.setModifyTimeStr(DateUtils.getNoSecond(e.getModifyTime()));
                    sdkVersionItem.setOperator(e.getOperator());
                    return sdkVersionItem;
                }).collect(Collectors.toList());
        result.setSdkList(items);
        return result;
    }

    public static SdkCustomerDOQuery convert(QuerySdkCustomerListInput input) {

        SdkCustomerDOQuery query = new SdkCustomerDOQuery();
        query.setOrgGidList(input.getOrgGidList());
        query.setSdkVersionList(input.getSdkVersionList());
        query.setChannelCodeList(input.getChannelCodeList());
        query.setChannelStatus(CollectionUtils.isEmpty(input.getChannelCodeList()) ? null : SdkStatusEnum.ENABLE.name());
        if (Objects.nonNull(input.getPageNum())) {
            query.setPageNum(input.getPageNum());
        }
        if (Objects.nonNull(input.getPageSize())) {
            query.setPageSize(input.getPageSize());
        }

        return query;
    }

    public static QuerySdkCustomerListResult convertQuerySdkCustomerListResult(List<SdkCustomerDO> customerDOS, List<SdkCustomerChannelMappingDO> channelMappingDOS,
                                                                               List<SdkCustomerVersionMappingDO> versionMappingDOS, Map<String, String> channelDescMap) {


        QuerySdkCustomerListResult result = new QuerySdkCustomerListResult();
        result.setCustomerList(Lists.newArrayList());

        if (CollectionUtils.isEmpty(customerDOS)) {
            return result;
        }
        //
        Map<String, List<SdkCustomerChannelMappingDO>> channelMap = Optional.ofNullable(channelMappingDOS).orElse(Collections.emptyList())
                .stream().collect(Collectors.groupingBy(SdkCustomerChannelMappingDO::getSdkCustomerId));
        Map<String, List<SdkCustomerVersionMappingDO>> versionMap = Optional.ofNullable(versionMappingDOS).orElse(Collections.emptyList())
                .stream().collect(Collectors.groupingBy(SdkCustomerVersionMappingDO::getSdkCustomerId));

        //
        List<QuerySdkCustomerListResult.SdkCustomerItem> items = customerDOS.stream().map(item -> {
            QuerySdkCustomerListResult.SdkCustomerItem sdkCustomerItem = new QuerySdkCustomerListResult.SdkCustomerItem();
            sdkCustomerItem.setCustomerId(item.getSdkCustomerId());
            sdkCustomerItem.setOrgName(item.getOrgName());
            sdkCustomerItem.setOrgGid(item.getOrgGid());
            sdkCustomerItem.setOperator(item.getOperator());
            sdkCustomerItem.setChannelNames(convert(channelDescMap, channelMap.get(item.getSdkCustomerId())));
            sdkCustomerItem.setBundleId(convertBundleId(channelMap.get(item.getSdkCustomerId())));
            sdkCustomerItem.setModifyTimeStr(DateUtils.getNoSecond(item.getModifyTime()));
            sdkCustomerItem.setSdkVersionList(convertSdkVersionList(versionMap.get(item.getSdkCustomerId())));
            return sdkCustomerItem;
        }).collect(Collectors.toList());
        result.setCustomerList(items);
        return result;
    }

    private static String convertBundleId(List<SdkCustomerChannelMappingDO> channelMappingDOS) {
        String bundleIds = Optional.ofNullable(channelMappingDOS).orElse(Collections.emptyList())
                .stream()
                .map(SdkCustomerChannelMappingDO::getBundleId)
                .distinct()
                .collect(Collectors.joining(SEQ));
        return bundleIds;
    }

    private static List<QuerySdkCustomerListResult.SdkCustomerVersionItem> convertSdkVersionList(List<SdkCustomerVersionMappingDO> versionMappingDOS) {
        return Optional.ofNullable(versionMappingDOS).orElse(Collections.emptyList())
                .stream().map(item -> {
                    QuerySdkCustomerListResult.SdkCustomerVersionItem versionItem = new QuerySdkCustomerListResult.SdkCustomerVersionItem();
                    versionItem.setVersion(item.getSdkVersion());
                    versionItem.setLatestUserTime(DateUtils.getNoSecond(item.getModifyTime()));
                    return versionItem;
                }).collect(Collectors.toList());
    }

    private static String convert(Map<String, String> channelDescMap, List<SdkCustomerChannelMappingDO> channelMappingDOS) {
        String channelNames = Optional.ofNullable(channelMappingDOS).orElse(Collections.emptyList())
                .stream().filter(SdkCustomerChannelMappingDO::hasLicense)
                .map(SdkCustomerChannelMappingDO::getChannelCode)
                .map(e -> channelDescMap.getOrDefault(e, e))
                .distinct()
                .collect(Collectors.joining(SEQ));

        return channelNames;
//        String bundleIds = Optional.ofNullable(channelMappingDOS).orElse(Collections.emptyList())
//                .stream()
//                .map(SdkCustomerChannelMappingDO::getBundleId)
//                .distinct()
//                .collect(Collectors.joining(SEQ));
//        return StringUtils.defaultString(bundleIds) + ":" + StringUtils.defaultString(channelNames, "暂未配置");
    }


    public static GetSdkCustomerDetailResult convertGetSdkCustomerDetailResult(SdkCustomerDO sdkCustomerDO, List<SdkCustomerChannelMappingDO> mappingDOS, Map<String, String> channelMap) {
        GetSdkCustomerDetailResult result = new GetSdkCustomerDetailResult();
        if (Objects.isNull(sdkCustomerDO)) {
            return result;
        }

        result.setSdkCustomerId(sdkCustomerDO.getSdkCustomerId());
        result.setOrgGid(sdkCustomerDO.getOrgGid());
        result.setOrgName(sdkCustomerDO.getOrgName());
        result.setStatus(sdkCustomerDO.getStatus());
        result.setOperator(sdkCustomerDO.getOperator());
        //
        final Map<String, SdkCustomerChannelMappingDO> keyAndGroup = mappingDOS.stream()
                .collect(Collectors.toMap(SdkCustomerChannelMappingDO::parseGroupKey, Function.identity(),
                        BinaryOperator.maxBy(Comparator.comparing(SdkCustomerChannelMappingDO::getId))));

        List<GetSdkCustomerDetailResult.SdkCustomerInfoItem> infos = keyAndGroup.values()
                .stream().map(e -> {
                    GetSdkCustomerDetailResult.SdkCustomerInfoItem infoItem = new GetSdkCustomerDetailResult.SdkCustomerInfoItem();
                    infoItem.setCustomerChannelId(e.getMappingId());
                    infoItem.setChannelName(channelMap.get(e.getChannelCode()));

                    infoItem.setChannelCode(e.getChannelCode());
                    infoItem.setChannelLicense(e.getChannelLicense());
                    infoItem.setHarmonyOsLicense(e.getHarmonyOsLicense());

                    // 2024/11/9  新增返回包名
                    infoItem.setBundleId(e.getBundleId());
                    return infoItem;
                }).collect(Collectors.toList());
        result.setCustomerInfoItems(infos);
        //
        return result;
    }

    public static GetManualConfigResult convertSdkCustomerConvert(List<SdkChannelDomain> sdkChannelDomains, List<SdkVersionDO> sdkList) {
        GetManualConfigResult result = new GetManualConfigResult();
        //渠道
        if (CollectionUtils.isNotEmpty(sdkChannelDomains)) {
            result.setChannelList(sdkChannelDomains.stream().map(e -> {
                GetManualConfigResult.ConfigItem configItem = new GetManualConfigResult.ConfigItem();
                configItem.setStatus(e.getStatus());
                configItem.setName(e.getChannelDesc());
                configItem.setCode(e.getChannelCode());
                return configItem;
            }).collect(Collectors.toList()));
        }
        //版本
        if (CollectionUtils.isNotEmpty(sdkList)) {
            result.setSdkVersionList(sdkList.stream().map(e -> {
                GetManualConfigResult.ConfigItem configItem = new GetManualConfigResult.ConfigItem();
                configItem.setStatus(e.getStatus());
                configItem.setName(e.getSdkVersion());
                configItem.setCode(e.getSdkVersion());
                return configItem;
            }).collect(Collectors.toList()));
        }
        return result;
    }
}
