package com.timevale.faceauth.service.domain.support;

/**
 * 调用网关http配置
 * <AUTHOR>
 * @since 2025/1/1 20:47
 */
public interface HttpTemplateConfig {

    int DEFAULT_RETRY_COUNT = 1;
    boolean DEFAULT_RETRY_ENABLED = true;
    int DEFAULT_MANAGER_CONNECTION_TIMEOUT = 4000;
    /**
     * 请求超时时间
     * @return
     */
    Integer connectionTimeout();

    /**
     * 读取超时时间
     * @return
     */
    Integer soTimeout();

    /**
     * 单个ip最大连接数
     * @return
     */
    Integer connectionsPerHost();

    /**
     * 总连接数
     * @return
     */
    Integer connectionsTotal();

    /**
     * 连接池获取请求等待最大时间
     * @return
     */
   default int connectionManagerTimeout(){
       return DEFAULT_MANAGER_CONNECTION_TIMEOUT;
   }


    /**
     * 重试次数
     * @return
     */
    default int retryCount(){
        return DEFAULT_RETRY_COUNT;
    }

    /**
     * 是否重试
     * @return
     */
    default boolean retryEnabled(){
        return DEFAULT_RETRY_ENABLED;
    }

}
