package com.timevale.faceauth.service.localphoto;

import com.timevale.faceauth.service.domain.provider.ConfigurableProviderService;
import lombok.Data;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2024/7/22 08:52
 */
@Data
public class PhotoOriginConfigBean {

    /**
     * 供应商标识
     * @see  ConfigurableProviderService
     */
    private String provider;

    /**
     * 是否采集作为本地缓存来源
     */
    private boolean localPhotoOriginEnable;

    /**
     * 是否使用本地照片数据源
     */
    private boolean useOriginEnable;

}
