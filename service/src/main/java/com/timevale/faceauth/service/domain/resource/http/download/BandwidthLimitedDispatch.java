package com.timevale.faceauth.service.domain.resource.http.download;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
public class BandwidthLimitedDispatch {

    private static final Map<String, Supplier<BandwidthLimiter>> limiterMap = new HashMap<>();

    static {
        register(BandwidthLimiter.NAME_DEFAULT,  () -> new NoneRateLimited());
        register(BandwidthLimiter.NAME_GUAVA_LIMITED,  () -> new GuavaRateLimited());
    }
    private static void register(String name,Supplier<BandwidthLimiter> supplier){
        if(supplier == null){
            return;
        }
        limiterMap.put(name,supplier);
    }

    public static   BandwidthLimiter select(String name){
        Supplier<BandwidthLimiter> supplier = limiterMap.get(name);
        if(supplier != null){
            return supplier.get();
        }
        return new NoneRateLimited();
    }
}
