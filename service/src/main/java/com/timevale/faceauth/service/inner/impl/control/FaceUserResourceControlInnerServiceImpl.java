package com.timevale.faceauth.service.inner.impl.control;

import com.timevale.faceauth.dal.pfs.dao.FaceUserResourceControlRecordDAO;
import com.timevale.faceauth.dal.pfs.dataobject.FaceUserResourceControlRecordDO;
import com.timevale.faceauth.dal.pfs.dataobject.query.FaceUserResourceControlRecordQuery;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.controll.FaceResourceControlStatusEnum;
import com.timevale.faceauth.service.domain.controll.FaceUserResourceControlCheckDTO;
import com.timevale.faceauth.service.domain.repository.FaceInfo;
import com.timevale.faceauth.service.domain.repository.FaceRepository;
import com.timevale.faceauth.service.inner.FaceUserResourceControlInnerService;
import com.timevale.faceauth.service.inner.impl.control.convert.FaceUserResourceControlConvert;
import com.timevale.faceauth.service.input.control.ControlRecordInput;
import com.timevale.faceauth.service.input.control.ControlRecordSaveInput;
import com.timevale.faceauth.service.input.control.FaceUserResourceControlConsumerInput;
import com.timevale.faceauth.service.input.control.SwitchStatusControlRecordInput;
import com.timevale.faceauth.service.result.control.ControlFaceDataPrivilegeResult;
import com.timevale.faceauth.service.result.control.ControlRecordPageResult;
import com.timevale.faceauth.service.result.control.ControlRecordResult;
import com.timevale.faceauth.service.utils.FaceAuthValidationUtils;
import com.timevale.framework.sands.Sahara;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.DateUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2025/7/2 14:49
 */
@Service
@Slf4j
public class FaceUserResourceControlInnerServiceImpl implements FaceUserResourceControlInnerService {
    @Autowired
    private FaceUserResourceControlRecordDAO controlRecordDAO;

    @Autowired
    private FaceRepository faceRepository;

    private String DEFAULT_CONTROL_TYPE = "APPID";

    @Override
    @Transactional
    public void batchSaveControlRecord(ControlRecordSaveInput input) {
        //去重
        List<String> appIds = Optional.ofNullable(input.getAppIds()).orElse(Collections.emptyList()).stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(appIds)) {
            return;
        }

        String proofDocuments = parseProofDocuments(input);
        for (String appId : appIds) {
            FaceUserResourceControlRecordDO recordDO = JsonUtils.obj2pojo(input, FaceUserResourceControlRecordDO.class);
            recordDO.setAppId(appId);
            recordDO.setRecordId(getRecordId());
            recordDO.setProofDocuments(proofDocuments);
            recordDO.setStatus(FaceResourceControlStatusEnum.REVOKE_AGREE.name());
            if (StringUtils.isBlank(recordDO.getControlType())) {
                recordDO.setControlType(DEFAULT_CONTROL_TYPE);
            }
            // 2025/7/7  此处不处理字段
            recordDO.setSourceBizId(StringUtils.EMPTY);
            recordDO.setSourceType(StringUtils.EMPTY);
            controlRecordDAO.insert(recordDO);


            try {
                disEnableRecord(recordDO, FaceResourceControlStatusEnum.REPEAT_AGREE);

            } catch (Exception e) {
                log.warn("disEnableRecord errror ", e);
            }


        }
    }

    private void disEnableRecord(FaceUserResourceControlRecordDO recordDO, FaceResourceControlStatusEnum expectStatus) {
        if (Objects.isNull(recordDO) || Objects.isNull(expectStatus)) {
            return;
        }
        FaceUserResourceControlRecordQuery query = new FaceUserResourceControlRecordQuery();
        query.setAppIds(Lists.newArrayList(recordDO.getAppId()));
        query.setName(recordDO.getName());
        query.setIdNo(recordDO.getIdNo());
        query.setIdType(recordDO.getIdType());
        query.setStatus(expectStatus.name());
        query.setPageNum(1);
        query.setPageSize(100);

        List<FaceUserResourceControlRecordDO> page = controlRecordDAO.page(query);
        if (CollectionUtils.isEmpty(page)) {
            return;
        }
        log.info("disEnableRecord data {}", JsonUtils.obj2json(page));

        for (FaceUserResourceControlRecordDO input : page) {
            String remark = StringUtils.defaultString(recordDO.getRemark()) + " \n 业务触发操作 ： 状态从 " +
                    recordDO.getStatus() + " 到" + input.getStatus() + ", 操作时间" + DateUtils.getYearMonthDayWeekHourMinuteSecond(new Date()) + " ,关联ID" + recordDO.getRecordId();
            controlRecordDAO.modifyStatusControlRecord(input.getRecordId(), FaceResourceControlStatusEnum.DISENABLE.name(), remark);
        }

    }

    public static String parseProofDocuments(ControlRecordInput input) {
        List<ControlRecordInput.ProofDocumentDTO> proofDocumentList = Optional.ofNullable(input.getProofDocumentList()).orElse(Collections.emptyList());
        List<String> fileKeys = proofDocumentList.stream().map(ControlRecordInput.ProofDocumentDTO::getFileKey).collect(Collectors.toList());
        FaceUserResourceControlRecordDO.ProofDocumentDO documentDO =  new FaceUserResourceControlRecordDO.ProofDocumentDO();
        documentDO.setFileKeys(fileKeys);
        return JsonUtils.obj2json(documentDO);
    }

    @Override
    public FaceUserResourceControlRecordDO selectOneByRecordId(String recordId) {
        return controlRecordDAO.selectOneByRecordId(recordId);
    }

    @Override
    public void modifyControlRecord(FaceUserResourceControlRecordDO input) {
        FaceUserResourceControlRecordDO recordDO = this.selectOneByRecordId(input.getRecordId());
        FaceAuthValidationUtils.notNull(recordDO, FaceException.valueOf(FaceStatusCode.NULL_ARGUMENT, "未查询到数据"));
        controlRecordDAO.modifyControlRecord(input);

    }

    @Override
    public ControlRecordPageResult pageControlRecord(FaceUserResourceControlRecordQuery query) {
        Long count = Optional.ofNullable(controlRecordDAO.count(query)).orElse(0L);
        List<FaceUserResourceControlRecordDO> page = controlRecordDAO.page(query);

        List<ControlRecordResult> collect = page.stream().map(e -> FaceUserResourceControlConvert.convert(e,false)).collect(Collectors.toList());

        ControlRecordPageResult pageResult = new ControlRecordPageResult();
        pageResult.setCount(count);
        pageResult.setResults(collect);
        return pageResult;
    }

    @Override
    public void switchStatusControlRecord(SwitchStatusControlRecordInput input) {
        FaceResourceControlStatusEnum requestStatus = FaceResourceControlStatusEnum.of(input.getStatus());
        FaceAuthValidationUtils.notNull(requestStatus, FaceException.valueOf(FaceStatusCode.NULL_ARGUMENT, "status 不合法"));

        FaceUserResourceControlRecordDO recordDO = this.selectOneByRecordId(input.getRecordId());
        FaceAuthValidationUtils.notNull(recordDO, FaceException.valueOf(FaceStatusCode.NULL_ARGUMENT, "未查询到数据"));


        String remark = StringUtils.defaultString(recordDO.getRemark()) + " \n 系统管理员操作 ： 状态从 " +
                recordDO.getStatus() + " 到" + input.getStatus() + ", 操作时间" + DateUtils.getYearMonthDayWeekHourMinuteSecond(new Date());
        controlRecordDAO.modifyStatusControlRecord(input.getRecordId(), input.getStatus(), remark);
    }

    @Override
    public ControlFaceDataPrivilegeResult checkUserResourceControl(FaceUserResourceControlCheckDTO input) {

        ControlFaceDataPrivilegeResult resultDTO = new ControlFaceDataPrivilegeResult();
        if (Objects.isNull(input) || StringUtils.isAnyBlank(input.getFaceId())) {
            return resultDTO;
        }

        FaceInfo faceInfo = faceRepository.getFaceInfoByFaceId(input.getFaceId());

        if (Objects.isNull(faceInfo) || StringUtils.isAnyBlank(faceInfo.getAppId(), faceInfo.getName(), faceInfo.getIdNo())) {
            return resultDTO;
        }

        resultDTO.setName(faceInfo.getName());

        // 2025/7/4 大部分是没有撤回数据的，这里先查询撤回
        FaceUserResourceControlRecordDO query = new FaceUserResourceControlRecordDO();
        query.setAppId(faceInfo.getAppId());
        query.setName(faceInfo.getName());
        query.setIdNo(faceInfo.getIdNo());
        query.setIdType(faceInfo.getIdType());
        query.setControlType(DEFAULT_CONTROL_TYPE);
        query.setStatus(FaceResourceControlStatusEnum.REVOKE_AGREE.name());
        FaceUserResourceControlRecordDO revokeAgreeRecord = controlRecordDAO.selectOne(JsonUtils.obj2pojo(query,FaceUserResourceControlRecordDO.class));
        //不存在撤回，直接返回
        if (revokeAgreeRecord == null) {
            return resultDTO;
        }

        resultDTO.setRefusePhotoAndVideoRecordId((revokeAgreeRecord.getRecordId()));
        resultDTO.setRefusePhotoAndVideoRecordTime(revokeAgreeRecord.getCreateTime());

        // 2025/7/4  在看看再次刷脸
        query.setStatus(FaceResourceControlStatusEnum.REPEAT_AGREE.name());
        FaceUserResourceControlRecordDO disEnableRecord = controlRecordDAO.selectOne(JsonUtils.obj2pojo(query,FaceUserResourceControlRecordDO.class));
        if (disEnableRecord == null) {
            // 2025/7/4  存在撤回 ，但是不存在再次同意
            resultDTO.setRefusePhotoAndVideo(true);
            return resultDTO;
        }

        //存在撤回 ，但是存在再次同意
        Date faceCreateTime = faceInfo.getCreateTime();
        Date repeatAgreeTime = disEnableRecord.getCreateTime();
        if (Objects.nonNull(faceCreateTime) && Objects.nonNull(repeatAgreeTime)) {
            resultDTO.setRefusePhotoAndVideo(repeatAgreeTime.after(faceCreateTime));
        }
        return resultDTO;
    }

    @Override
    public void repeatAgree(FaceUserResourceControlConsumerInput input) {
        FaceUserResourceControlRecordDO query = new FaceUserResourceControlRecordDO();
        query.setAppId(input.getAppId());
        query.setName(input.getName());
        query.setIdNo(input.getIdNo());
        query.setIdType(input.getIdType());
        query.setControlType(DEFAULT_CONTROL_TYPE);
        query.setStatus(FaceResourceControlStatusEnum.REVOKE_AGREE.name());
        FaceUserResourceControlRecordDO revokeAgreeRecord = controlRecordDAO.selectOne(query);

        if (revokeAgreeRecord == null) {
            return;
        }

        // 2025/7/4  在看看再次刷脸
        query.setStatus(FaceResourceControlStatusEnum.REPEAT_AGREE.name());
        FaceUserResourceControlRecordDO disEnableRecord = controlRecordDAO.selectOne(query);
        if (disEnableRecord != null && disEnableRecord.getCreateTime().after(revokeAgreeRecord.getCreateTime())) {
            return;
        }

        String recordId = getRecordId();
        FaceUserResourceControlRecordDO recordDO = JsonUtils.obj2pojo(input, FaceUserResourceControlRecordDO.class);
        recordDO.setRecordId(recordId);
        recordDO.setAppId(input.getAppId());
        recordDO.setName(input.getName());
        recordDO.setIdNo(input.getIdNo());
        recordDO.setIdType(input.getIdType());
        recordDO.setMobileNo(StringUtils.EMPTY);
        recordDO.setRemark(StringUtils.EMPTY);

        recordDO.setProofDocuments(JsonUtils.obj2json(new FaceUserResourceControlRecordDO.ProofDocumentDO()));
        recordDO.setStatus(FaceResourceControlStatusEnum.REPEAT_AGREE.name());
        recordDO.setControlType(DEFAULT_CONTROL_TYPE);

        // 2025/7/7
        recordDO.setSourceType(input.getSourceType());
        recordDO.setSourceBizId(input.getSourceId());
        controlRecordDAO.insert(recordDO);

        log.info("repeatAgree 刷脸合规， {},", JsonUtils.obj2json(recordDO));
    }

    private static String getRecordId() {
        return "cr" + Sahara.instance.getHexSand();
    }
}
