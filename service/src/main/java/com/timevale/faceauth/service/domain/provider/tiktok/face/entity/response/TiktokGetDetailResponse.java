package com.timevale.faceauth.service.domain.provider.tiktok.face.entity.response;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * 在查询认证数据环节新增返回是否计费的字段，具体查询可见文档【内测】实名认证接口查询计费信息
 * 计费逻辑：只要产生调用底层数据源的，都会产生计费；计费仅会产生在票据获取成功&端上调用tt.startFacialRecognitionVerif，发起活体检测动作（即触发数据源比对）后，具体以是否计费字段为准。
 * 如需获取查询计费信息权限，请联系@飞书用户4336
 *
 * <AUTHOR>
 * @DATE 2024/5/20 15:52
 */
@Data
public class TiktokGetDetailResponse extends  TiktokAbstractResponse {
    private int err_no;
    private String err_tips;

    private DETAIL_INFO detail_info;

    @Data
    public static class DETAIL_INFO extends ToString {

        /**
         * 计费次数
         */
        private int charge_num;
    }
}
