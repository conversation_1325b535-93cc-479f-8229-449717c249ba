package com.timevale.faceauth.service.domain.provider.tiktok.face.domain;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;
import org.assertj.core.util.Lists;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE 2024/5/27 10:42
 */
@Data
public class TiktokAppCommonDomain extends ToString {

    /**
     * 此刷脸场景值得是在抖音小程序中刷脸配置上下扫脸还是左右扫脸   key 抖音：1128；抖极：2329；抖火：8663
     */
    public Map<String, String> tiktokChannelSceneMap = new HashMap<>();

    /**
     * 抖音渠道配置
     */
    public Map<String, String> tiktokSourceChannel = new HashMap<>();

    /**
     * 外部传入的错误码，剔除无用的字符串
     */
    private List<String> tiktokOutResultStatusReplaceBlank = Lists.newArrayList();

    /**
     * access_token 缓存key
     */
    private String tiktokAccessTokenTedisKey;

    private String tiktokAuthUrl;

    /**
     * 抖音小程序 access_token 的失效时间
     * 默认3分钟  只有下次修改才能生效
     */
    private Integer accessTokenMinuteExpireTime = 3;



}
