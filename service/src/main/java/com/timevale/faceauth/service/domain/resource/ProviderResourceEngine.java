package com.timevale.faceauth.service.domain.resource;

import com.timevale.faceauth.service.domain.resource.task.ResourceDownloadMsg;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class ProviderResourceEngine {

    protected Map<String, ProviderRemoteResourceLoader> loaderMap = new HashMap<>();



    @Autowired
    public ProviderResourceEngine(List<ProviderRemoteResourceLoader> loaderList) {

        loaderMap.clear();

        for(ProviderRemoteResourceLoader  resourceLoader: loaderList){
            String loaderEquipId = ResourceLoaderSupport.buildResourceLoaderKey(resourceLoader);
            loaderMap.put(loaderEquipId, resourceLoader);
        }
    }

    public ProviderRemoteResourceLoader matchAsync(String provider, FaceResourceType resourceType) {
        String loaderKey = ResourceLoaderSupport.buildResourceLoaderKey(provider, resourceType, ResourceStrategyEnum.ASYNC);
        return loaderMap.get(loaderKey);
    }

    public ProviderRemoteResourceLoader match(ResourceDownloadMsg msg) {
        String loaderKey = ResourceLoaderSupport.buildResourceLoaderKey(msg);
        return loaderMap.get(loaderKey);
    }
}
