package com.timevale.faceauth.service.domain.resource;

import com.timevale.faceauth.service.domain.provider.tencent.TencentCloudCertificationQueryRequest;
import com.timevale.faceauth.service.domain.provider.tencent.TencentWebAppIdVersion;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/12/26 10:09
 */
@Data
public class ProviderTencentCloudQueryContext extends ProviderQueryContext{


    private TencentCloudCertificationQueryRequest queryRequest;
    private TencentWebAppIdVersion appIdVersion;

}
