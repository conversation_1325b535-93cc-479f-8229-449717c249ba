package com.timevale.faceauth.service.domain.provider.bytedance.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/2/21
 */
@Data
public class FaceCertTokenRequest {

    /**
     * 此处请填写cert_token
     */
    @JSONField(name = "req_key")
    private String req_key = "cert_token";

    /**
     * 通过STS接口获取的临时token
     */
    @JSONField(name = "sts_token")
    private String sts_token;

    /**
     * 比对类型。可选类型：
     * 0：无源比对
     * 1：有源比对
     */
    @JSONField(name = "ref_source")
    private String ref_source ;

    /**
     * 端上活体类型，motion（默认）：动作活体
     */
    @JSONField(name = "liveness_type")
    private String liveness_type = "motion"  ;

    /**
     * 在有源比对时必选	身份证姓名
     */
    @JSONField(name = "idcard_name")
    private String idcard_name ;

    /**
     * 在有源比对时必选	身份证号
     */
    @JSONField(name = "idcard_no")
    private String idcard_no ;

    /**
     * 在无源比对时必选	输入图片的base64数组，在无源比对时需要传入1张用户的基准图，有源比对无需传入
     */
    @JSONField(name = "ref_image")
    private String ref_image ;

//    /**
//     * 端上活体超时时长，可选范围： [5, 60]，默认：10
//     */
//    @JSONField(name = "liveness_timeout")
//    private int livenessTimeout ;

//    /**
//     * 可被下发的动作列表，仅在动作活体和炫彩活体生效。可选动作：
//     * 0：眨眼
//     * 1：张嘴
//     * 2：点头
//     * 3：摇头
//     */
//    @JSONField(name = "motion_list")
//    private ArrayList<String> motionList = new ArrayList<>();

//    /**
//     * 固定一定需要下发的动作列表，仅在动作活体和炫彩活体生效。取值同motion_list
//     */
//    @JSONField(name = "fixed_motion_list")
//    private ArrayList<String> fixedMotionList = new ArrayList<>();

//    @JSONField(name = "motion_count")
//    private Integer motionCount ;

//    /**
//     * 端上动作活体最大尝试次数
//     */
//    @JSONField(name = "max_liveness_trial")
//    private Integer maxLivenessTrial ;

//    /**
//     * 回调配置信息
//     */
//    @JSONField(name = "callback_info")
//    private JSONObject callBackInfo;

    /**
     * 配置ID，可在Token接口或H5端传入，以激活配置
     */
    @JSONField(name = "config_id")
    private String config_id;
}
