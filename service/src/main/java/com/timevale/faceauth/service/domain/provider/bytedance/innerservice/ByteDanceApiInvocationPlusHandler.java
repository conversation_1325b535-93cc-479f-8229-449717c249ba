package com.timevale.faceauth.service.domain.provider.bytedance.innerservice;

import com.timevale.faceauth.service.component.OpenPlatformClient;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.domain.FaceAuthorizationPhotoResolver;
import com.timevale.faceauth.service.domain.provider.ConfigurableProviderService;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationData;
import com.timevale.faceauth.service.domain.provider.bytedance.ByteDanceApiInvocationHandler;
import com.timevale.faceauth.service.domain.provider.bytedance.STSServiceExtend;
import com.timevale.faceauth.service.domain.provider.bytedance.VisualServiceExtend;
import com.timevale.faceauth.service.domain.provider.bytedance.domain.FaceCertTokenResponse;
import com.timevale.faceauth.service.domain.provider.bytedance.domain.FaceConfigInitResponse;
import com.timevale.faceauth.service.domain.provider.bytedance.domain.v2.FaceCertH5ConfigInitRequest;
import com.timevale.faceauth.service.domain.provider.bytedance.domain.v2.FaceCertH5TokenRequest;
import com.timevale.faceauth.service.domain.provider.support.FaceAuthorizationInitializingContext;
import com.timevale.faceauth.service.domain.repository.FaceRepository;
import com.timevale.faceauth.service.domain.support.DnsResolver;
import com.timevale.faceauth.service.esignface.config.EsignFaceConfig;
import com.timevale.faceauth.service.integration.grayscale.RpcSaaSGrayConfigAdapt;
import com.timevale.faceauth.service.integration.grayscale.domain.GrayFunctionEnum;
import com.timevale.faceauth.service.utils.UrlUtil;
import com.timevale.faceauth.service.utils.providerLog.ProviderLogService;
import com.timevale.faceauth.service.utils.providerLog.result.ByteDanceInitResult;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.volcengine.model.Credentials;
import com.volcengine.model.request.AssumeRoleRequest;
import com.volcengine.model.response.AssumeRoleResponse;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @DATE 2025/4/1 10:34
 */
@Slf4j
@Component
public class ByteDanceApiInvocationPlusHandler extends ByteDanceApiInvocationHandler {

    @Autowired
    private ConfigurableProperties configurableProperties;
    @Autowired
    private FaceAuthorizationPhotoResolver faceAuthorizationPhotoResolver;
    @Autowired
    private ProviderLogService providerLogService;
    @Autowired
    private DnsResolver dnsResolver;
    @Autowired
    private EsignFaceConfig config;

    @Autowired
    private FaceRepository faceRepository;

    @Autowired
    RpcSaaSGrayConfigAdapt rpcSaaSGrayConfigAdapt;
    @Autowired
    private OpenPlatformClient openPlatformClient;

    public ProviderFaceAuthorizationData startFaceInvoke(FaceAuthorizationInitializingContext context, ConfigurableProviderService service) throws FaceException {

        String appId = context.getRequestContext().getAppId();
        boolean gray = rpcSaaSGrayConfigAdapt.inGray(appId, GrayFunctionEnum.BYTEDANCE_VERSION_UPGRADE);
        log.info("字节增强版本灰度配置 Plus gray appId :{} gray:{} ", appId, gray);
        if (!gray) {
            return super.startFaceInvoke(context, service);
        }

        // 使用的是增强版本字节刷脸
        long startTime = System.currentTimeMillis();
        try {
            //1. 获取临时密钥（STS）
            AssumeRoleResponse resp = getAssumeRoleResponse();
            log.info("Plus ByteDance.startFaceInvoke assumeRole faceId={}, requestId={} clientCost= {}  ms", context.getFaceId(), resp.getResponseMetadata().getRequestId(), (System.currentTimeMillis() - startTime));


            VisualServiceExtend serviceExtend = VisualServiceExtend.getInstance();

            Credentials credentials = new Credentials();
            credentials.setAccessKeyID(resp.getResult().getCredentials().getAccessKeyId());
            credentials.setSecretAccessKey(resp.getResult().getCredentials().getSecretAccessKey());
            credentials.setSessionToken(resp.getResult().getCredentials().getSessionToken());
            credentials.setService(serviceExtend.getServiceInfo().getCredentials().getService());
            credentials.setRegion(serviceExtend.getServiceInfo().getCredentials().getRegion());

            FaceCertH5TokenRequest tokenRequest = new FaceCertH5TokenRequest();
            tokenRequest.setSts_token(resp.getResult().getCredentials().getSessionToken());
            tokenRequest.setIdcard_name(context.getRequest().getName());
            tokenRequest.setIdcard_no(context.getRequest().getIdNo());

            boolean userPhoto = StringUtils.isNotBlank(context.getPhotoKey());

            String configId = getConfigId(userPhoto, credentials, appId, context);
            tokenRequest.setH5_config_id(configId);
            if (userPhoto) {
                tokenRequest.setRef_image(faceAuthorizationPhotoResolver.resolvePhotoData(context.getPhotoKey()));
            }


            // 2025/4/1
            FaceCertTokenResponse tokenResponse = serviceExtend.h5CertToken(tokenRequest, credentials);
            log.info("Plus ByteDance.startFaceInvoke certToken faceId={}, requestId={} sts_token={} , clientCost= {}  ms", context.getFaceId(), resp.getResponseMetadata().getRequestId(), tokenRequest.getSts_token(), (System.currentTimeMillis() - startTime));

            providerLogService.logByteDanceInit(new ByteDanceInitResult(context.getFaceId(), System.currentTimeMillis() - startTime), null, tokenResponse);



            String baseUrl = config.getMappingBytedanceFaceH5V2Url() + "/face-api-v2?";
            String dnsAppId = dnsResolver.resolveDnsAppId();
            if (StringUtils.isNotBlank(dnsAppId)) {
                baseUrl = dnsResolver.deduceActualDns(config.getMappingBytedanceFaceH5V2Url(), "/face-api-v2") + "?";
            }

            baseUrl = UrlUtil.appendParamWithHasVal(baseUrl, PARAM_NAME_ACCESSKEYID, resp.getResult().getCredentials().getAccessKeyId());
            baseUrl = UrlUtil.appendParamWithHasVal(baseUrl, PARAM_NAME_SECRETACCESSKEY, resp.getResult().getCredentials().getSecretAccessKey());
            baseUrl = UrlUtil.appendParamWithHasVal(baseUrl, PARAM_NAME_SESSIONTOKEN, resp.getResult().getCredentials().getSessionToken());
            //增强版本不支持拼接
//            baseUrl = UrlUtil.appendParamWithHasVal(baseUrl, PARAM_NAME_REDIRECTURL, UrlUtil.encode(context.getReturnUrl()));
            baseUrl = UrlUtil.appendParamWithHasVal(baseUrl, PARAM_NAME_BYTEDTOKEN, tokenResponse.getData().getBytedToken());
            baseUrl = UrlUtil.appendParamWithHasVal(baseUrl, PARAM_NAME_CONFIGID, configId);
            log.info("Plus ByteDance.startFaceInvoke faceId={}, baseUrl:{}", context.getFaceId(), baseUrl);

            // authInput 数据将被落库，此时，authInput 上下文中的照片数据是以 base64 形式存在的，这个数据的长度不可控，可能导致长度超出 DB 的长度限制，
            // 为了解决这个问题，将照片保存的 key 设置为当前值，后续根据当前的 key 从数据仓库中获得实际的照片的 base64 数据。
            tokenRequest.setRef_image(context.getPhotoKey());

            // 2025/4/3
            faceRepository.faceApiVersion(context.getFaceId(), ConfigurableProviderService.PROVER_TOW_V);
            return ProviderFaceAuthorizationData.createBuilder()
                    .setProvider(service.getProviderName())
                    .setProviderName(service.getFullName())
                    .setData(baseUrl)
                    .setOrderNo(tokenResponse.getData().getBytedToken())
                    .setExpireMinutes(Long.valueOf(configurableProperties.getByteDanceH5UrlExpire()))
                    .setAuthorizeInput(JsonUtils.obj2json(tokenRequest))
                    // 2025/4/3 设置版本
                    .setProviderVersion(ConfigurableProviderService.PROVER_TOW_V)
                    .build();
        } catch (Exception e) {
            log.error("Plus ByteDance.startFaceInvoke Error, e", e);
            providerLogService.logByteDanceInitWithException(
                    new ByteDanceInitResult(context.getFaceId(), System.currentTimeMillis() - startTime),
                    null,
                    e);
            throw FaceException.valueOf(FaceStatusCode.PROVIDER_FAILED_API);
        }
    }

    private AssumeRoleResponse getAssumeRoleResponse() throws Exception {
        STSServiceExtend stsService = STSServiceExtend.getInstance();
        stsService.setAccessKey(configurableProperties.getByteDanceAccessKey());
        stsService.setSecretKey(configurableProperties.getByteDanceSecretKey());

        AssumeRoleRequest request = new AssumeRoleRequest();
        request.setRoleSessionName(ROLE_SESSION_NAME);
        request.setDurationSeconds(configurableProperties.getByteDanceH5UrlExpire());
        request.setRoleTrn(configurableProperties.getByteDanceRoleTrn());
        AssumeRoleResponse resp = stsService.assumeRole(request);
        return resp;
    }

    public String getConfigId(boolean userPhoto, Credentials credentials, String appId, FaceAuthorizationInitializingContext context) throws Exception {
        //1、
        FaceCertH5ConfigInitRequest.H5Config h5Config = new FaceCertH5ConfigInitRequest.H5Config();
        //OCR上传图片识别、输入身份证号和姓名 这两个步骤都要跳过，直接进行人脸认证流程。
        h5Config.setType("3");
        h5Config.setShow_guide("0");
        h5Config.setShow_result("0");
        //优先使用更高效便捷的实时刷脸方案，当设备不支持该方案时的降级处理策略。
        h5Config.setEnable_record(this.isDegradationEnabled(appId));
        // 2025/4/1      优先级低于连接上拼接的参数。 但此处又是必填的。 复用之前设置的值
        h5Config.setRedirect_url(context.getReturnUrl());
        h5Config.setIgnore_bottom_text(true);
        h5Config.setIgnore_homepage_agreement(true);

        //
        FaceCertH5ConfigInitRequest.LivenessConfig livenessConfig = new FaceCertH5ConfigInitRequest.LivenessConfig();
        livenessConfig.setRef_source(userPhoto ? "0" : "1");
        livenessConfig.setLiveness_type("reflection");
        livenessConfig.setLiveness_timeout(10);
        livenessConfig.setMotion_list(Lists.newArrayList("0"));
        livenessConfig.setFixed_motion_list(Lists.newArrayList("0"));
        livenessConfig.setMotion_count(1);
        livenessConfig.setMax_liveness_trial(5);

        //
        FaceCertH5ConfigInitRequest request = new FaceCertH5ConfigInitRequest();
        request.setH5_config(h5Config);
        request.setLiveness_config(livenessConfig);
        request.setExpire_duration(7200);
        //
        VisualServiceExtend serviceExtend = VisualServiceExtend.getInstance();
        FaceConfigInitResponse response = serviceExtend.h5ConfigInit(request, credentials);
        return response.getData().getConfigId();
    }

    public String isDegradationEnabled(String appId) {
        try {
            /**
             * 优先使用更高效便捷的实时刷脸方案，当设备不支持该方案时的降级处理策略。
             * 选填，默认1
             * 0 当设备不支持实时刷脸能力时，则认定本次认证失败
             * 1 当设备不支持实时刷脸能力时，启用备用认证方案 - 视频录制，该方案能更好的兼容低端设备。
             */
            //选择后H5刷脸方式不会自动降级录制视频
            List<Integer> disableRecordType = Optional.ofNullable(openPlatformClient.getDisableRecordType(appId)).orElse(Collections.emptyList());
            return disableRecordType.contains(2) ? "0" : "1";
        } catch (Exception e) {
            log.warn("e  查询应用配置项信息错误 " + appId, e);
        }
        return "0";
    }

}
