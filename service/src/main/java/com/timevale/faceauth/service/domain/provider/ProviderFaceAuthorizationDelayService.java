package com.timevale.faceauth.service.domain.provider;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.provider.support.FaceAuthorizationInitializingContext;
import com.timevale.faceauth.service.input.HandleFaceAuthorizationReturnInput;
import com.timevale.faceauth.service.input.WakeupFaceInput;
import com.timevale.faceauth.service.result.WakeupFaceResult;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/9/30 15
 */
public interface ProviderFaceAuthorizationDelayService {

  void faceAuthorizationReturned(
      String faceId, HttpServletRequest request, HttpServletResponse response);


    default boolean supportDelayDoInitialize(FaceAuthorizationInitializingContext initializingContext) {
        return false;
    }

    default ProviderFaceAuthorizationData delayDoInitialize(FaceAuthorizationInitializingContext initializingContext) throws FaceException {
        throw FaceException.valueOf(FaceStatusCode.SERVICE_UNSUPPORTED);
    }


    /**
     * @param faceId
     * @param request
     * @return
     */
    default ProviderFaceAuthorizationResult handleFaceAuthorizationResponse(String faceId, HandleFaceAuthorizationReturnInput request) {
        throw FaceException.valueOf(FaceStatusCode.SERVICE_UNSUPPORTED);
    }

    default WakeupFaceResult handleWakeupFace(String faceId, WakeupFaceInput input) {
        throw FaceException.valueOf(FaceStatusCode.SERVICE_UNSUPPORTED);
    }
}
