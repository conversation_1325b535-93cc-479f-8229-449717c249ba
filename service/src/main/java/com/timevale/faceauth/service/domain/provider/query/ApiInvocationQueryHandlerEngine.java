package com.timevale.faceauth.service.domain.provider.query;

import com.timevale.faceauth.service.component.OpenPlatformClient;
import com.timevale.faceauth.service.constant.DataPrivilegeEnum;
import com.timevale.faceauth.service.domain.provider.support.RestTemplateRequestResolver;
import com.timevale.faceauth.service.domain.resource.*;
import com.timevale.faceauth.service.domain.resource.store.ResourceStore;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ApiInvocationQueryHandlerEngine {

    private static final Map<FaceResourceType, ResourceStore>  SOURCE_STORE_MAP = new HashMap<>();

    @Autowired
    private RestTemplateRequestResolver requestResolver;
    @Autowired
    private ProviderResourceEngine providerResourceEngine;


    @Autowired
    private OpenPlatformClient platformClient;

    @Autowired
    public ApiInvocationQueryHandlerEngine(List<ResourceStore> stores) {
        if(stores == null){
            return;
        }
        for(ResourceStore store : stores){
            SOURCE_STORE_MAP.put(store.getResourceType(), store);
        }
    }

    public <T> ProviderResponse<T> invoker(ProviderQueryContext context, ProviderApiInvocationQueryHandler<T> queryHandler) {
        PrepareModel prepareModel = this.prepare(context);;
        Set<FaceResourceType> resourceTypes = prepareModel.resourceTypes;
        context.setResourceTypes(resourceTypes);
        ProviderResponse<T> providerResponse = queryHandler.call(requestResolver, context);
        this.after(context, prepareModel,providerResponse);
        return providerResponse;
    }

    private PrepareModel prepare(ProviderQueryContext context){

        Set<FaceResourceType> resourceTypes = new HashSet<>();
        resourceTypes.add(FaceResourceType.BASE);
        boolean photoAsyncStrategy =  ifAsyncPhotoStrategy(context);
        boolean videoAsyncStrategy = ifAsyncVideoStrategy(context);
        if(!photoAsyncStrategy){
            resourceTypes.add(FaceResourceType.PHOTO);
        }
        if(!videoAsyncStrategy){
            resourceTypes.add(FaceResourceType.VIDEO);
        }
        PrepareModel prepareModel = PrepareModel.builder()
                .resourceTypes(resourceTypes)
                .photoAsyncStrategy(photoAsyncStrategy)
                .videoAsyncStrategy(videoAsyncStrategy)
                .build();
        return prepareModel;

}

    private void after(ProviderQueryContext context, PrepareModel prepareModel,ProviderResponse providerResponse){
        mapperOssKey(FaceResourceType.PHOTO, context, providerResponse);
        mapperOssKey(FaceResourceType.VIDEO, context, providerResponse);

        if(prepareModel.photoAsyncStrategy && providerResponse.isCompleted()){
            asyncResourceLoad(context, FaceResourceType.PHOTO);
        }
        if(prepareModel.videoAsyncStrategy && providerResponse.isSuccess()){
            asyncResourceLoad(context, FaceResourceType.VIDEO);
        }
    }

    private void asyncResourceLoad(ProviderQueryContext context, FaceResourceType resourceType){
        String provider = context.getFaceInfo().getProvider();
        ProviderRemoteResourceLoader resourceLoader =
                providerResourceEngine.matchAsync(provider, resourceType);
        if(resourceLoader != null){
            resourceLoader.execute(context);
        }
    }

    private void mapperOssKey(FaceResourceType resourceType , ProviderQueryContext context,
                              ProviderResponse providerResponse) {
        ResourceStore resourceStore = SOURCE_STORE_MAP.get(resourceType);
        if(resourceStore == null){
            log.error("can not found resourceType {} store", resourceType);
            return;
        }

        resourceStore.done(context, providerResponse);
    }

    private boolean ifAsyncPhotoStrategy(ProviderQueryContext context){
        String provider = context.getFaceInfo().getProvider();
        String photoStrategy =
                ResourceSystemHolder.getInstance().getResourceStrategy(provider,
                        FaceResourceType.PHOTO);

        if(Objects.equals(photoStrategy, ResourceStrategyEnum.ASYNC.getCode())){
            return true;
        }
        return false;
    }

    private boolean ifAsyncVideoStrategy(ProviderQueryContext context){
        String provider = context.getFaceInfo().getProvider();
        String videoStrategy =
                ResourceSystemHolder.getInstance().getResourceStrategy(provider,
                        FaceResourceType.VIDEO);

        if(Objects.equals(videoStrategy, ResourceStrategyEnum.ASYNC.getCode())){
            return true;
        }

        if(Objects.equals(videoStrategy, ResourceStrategyEnum.SYNC.getCode())){
            return false;
        }
        String appId = context.getFaceInfo().getAppId();
        DataPrivilegeEnum privilegeEnum =  platformClient.dataPrivilege(appId);
        if(privilegeEnum.ifHasVideo()){
            return false;
        }
        return true;
    }

    @Builder
    private static class PrepareModel {
        private Set<FaceResourceType> resourceTypes;
        private boolean photoAsyncStrategy;
        private boolean videoAsyncStrategy;
    }
}
