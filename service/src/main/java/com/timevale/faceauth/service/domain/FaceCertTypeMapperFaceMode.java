package com.timevale.faceauth.service.domain;

import lombok.Data;

import java.util.Set;

/**
 * 刷脸证件类型支持刷脸类型
 * <AUTHOR>
 * @since 2024/10/28 15:14
 */
@Data
public class FaceCertTypeMapperFaceMode {

    /**
     * 证件类型
     * @see com.timevale.faceauth.service.enums.FaceCertTypeEnum.openCertTypeEnum
     */
    private String certType;

//    /**
//     * 证件类型名称
//     */
//    private String certName;

    /**
     * 刷脸类型
     * @see com.timevale.faceauth.service.enums.FaceAuthModeEnum.name
     */
    private Set<String> faceModes;

}
