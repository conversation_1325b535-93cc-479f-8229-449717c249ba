package com.timevale.faceauth.service.input.sdk;

import com.timevale.faceauth.service.input.BaseInput;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @DATE 2024/8/6 11:27
 */
@Data
public class VerifyAndQueryChannelSdkInput extends BaseInput {

    @NotBlank(message = "企业GID不能为空")
    private String orgGid;

    @NotBlank(message = "sdk版本不能为空")
    private String sdkVersion;

    /**
     * 包ID，用于唯一标识一个包
     */
    private String bundleId;

    /**
     * 平台类型，用于标识当前环境或目标平台
     */
    private String platform;

    /**
     * 设备ID
     */
    private String deviceId;


    /**
     * oid
     */
    private String oid;
}
