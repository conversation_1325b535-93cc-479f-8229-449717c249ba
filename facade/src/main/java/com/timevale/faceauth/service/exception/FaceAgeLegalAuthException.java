package com.timevale.faceauth.service.exception;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;

/**
 * <AUTHOR>
 * @DATE 2025/6/26 10:25
 */
public class FaceAgeLegalAuthException extends FaceException {

    public FaceAgeLegalAuthException(int code, String msg) {
        super(code, msg);
    }

    public FaceAgeLegalAuthException(FaceStatusCode status) {
        super(status);
    }

    public FaceAgeLegalAuthException(FaceStatusCode status, String msg) {
        super(status, msg);
    }
}
