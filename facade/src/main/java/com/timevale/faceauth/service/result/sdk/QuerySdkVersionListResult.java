package com.timevale.faceauth.service.result.sdk;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2024/8/6 15:23
 */
@Data
public class QuerySdkVersionListResult extends ToString {

    private Long count;

    private List<SdkVersionItem> sdkList;

    @Data
    public static class SdkVersionItem extends ToString {
        private String sdkVersionId;
        private String sdkVersion;
        private String status;
        private String statusDesc;
        private String modifyTimeStr;
        private String operator;
    }
}
