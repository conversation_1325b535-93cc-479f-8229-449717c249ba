package com.timevale.faceauth.service.input.control;

import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE 2025/7/7 10:46
 */
@Data
public class ControlFaceDataPrivilegeInput extends ToString {


    /**
     * 照片资源
     */
    private String photoDataPrivilege = PrivilegeRangeEnum.DEFAULT.name();
    /**
     * 视频资源
     */
    private String videoDataPrivilege = PrivilegeRangeEnum.DEFAULT.name();


    public static enum PrivilegeRangeEnum {

        /**
         * 根据默认规则走
         */
        DEFAULT,
        /**
         * 特权, 可以直接返回数据
         */
        PRIVILEGE,
//        /**
//         * 恒不返回数据
//         */
//        NEVER_RETURN
        ;
    }


    public static boolean privilege(String  rangeEnum) {
        return StringUtils.equals(PrivilegeRangeEnum.PRIVILEGE.name(), rangeEnum);
    }

}
