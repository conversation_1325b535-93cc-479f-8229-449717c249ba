package com.timevale.faceauth.service.result.sdk;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @DATE 2024/8/6 15:36
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BatchSaveCustomerChannelResult extends ToString {

    private boolean success = true;

    private String sdkCustomerId;
}
