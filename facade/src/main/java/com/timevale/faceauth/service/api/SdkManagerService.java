package com.timevale.faceauth.service.api;

import com.timevale.faceauth.service.input.sdk.BatchSaveCustomerChannelInput;
import com.timevale.faceauth.service.input.sdk.GetManualConfigInput;
import com.timevale.faceauth.service.input.sdk.GetSdkCustomerDetailInput;
import com.timevale.faceauth.service.input.sdk.QuerySdkCustomerListInput;
import com.timevale.faceauth.service.input.sdk.QuerySdkVersionListInput;
import com.timevale.faceauth.service.input.sdk.SaveSdkVersionInput;
import com.timevale.faceauth.service.input.sdk.UpdateBundleIdIdInput;
import com.timevale.faceauth.service.input.sdk.UpdateSdkVersionStatusInput;
import com.timevale.faceauth.service.input.sdk.VerifyAndQueryChannelSdkInput;
import com.timevale.faceauth.service.result.BaseFaceAuthResult;
import com.timevale.faceauth.service.result.SupportResult;
import com.timevale.faceauth.service.result.sdk.BatchSaveCustomerChannelResult;
import com.timevale.faceauth.service.result.sdk.GetManualConfigResult;
import com.timevale.faceauth.service.result.sdk.GetSdkCustomerDetailResult;
import com.timevale.faceauth.service.result.sdk.QuerySdkCustomerListResult;
import com.timevale.faceauth.service.result.sdk.QuerySdkVersionListResult;
import com.timevale.faceauth.service.result.sdk.SaveSdkVersionResult;
import com.timevale.faceauth.service.result.sdk.UpdateSdkVersionStatusResult;
import com.timevale.mandarin.common.annotation.RestClient;

/**
 * <AUTHOR>
 * @DATE 2024/8/6 11:24
 */
@RestClient(serviceId = "faceauth-service")
public interface SdkManagerService {

    /**
     * 校验版本和获取数据
     * @param input
     * @return
     */
    SupportResult<String> verifyAndQueryChannelList(VerifyAndQueryChannelSdkInput input);


    /**
     * 保存SDK版本
     *
     * @return
     */
    SupportResult<SaveSdkVersionResult> saveSdkVersion(SaveSdkVersionInput input);


    /**
     * 查询SDK版本列表
     *
     * @return
     */
    SupportResult<QuerySdkVersionListResult> querySdkVersionList(QuerySdkVersionListInput input);


    /**
     * 更新SDK的状态 ，敏感接口  停用后直接导致SDK不可用 慎用
     *
     * @param input
     * @return
     */
    SupportResult<UpdateSdkVersionStatusResult> updateSdkVersionStatus(UpdateSdkVersionStatusInput input);



    /**
     * 查询集成SDK的客户列表
     *
     * @param input
     * @return
     */
    SupportResult<QuerySdkCustomerListResult> querySdkCustomerList(QuerySdkCustomerListInput input);


    /**
     * 批量新增客户SDK
     *
     * @param input
     * @return
     */
    SupportResult<BatchSaveCustomerChannelResult> batchSaveCustomerChannel(BatchSaveCustomerChannelInput input);


    /**
     * 查询集成SDK的客户详情
     *
     * @param input
     * @return
     */
    SupportResult<GetSdkCustomerDetailResult> getSdkCustomerDetail(GetSdkCustomerDetailInput input);


    /**
     * 给后台查询的配置项接口
     *
     * @param input
     * @return
     */
    SupportResult<GetManualConfigResult> getManualConfig(GetManualConfigInput input);


    /**
     * 更新 bundleId
     *
     * @param input
     * @return
     */
    SupportResult<BaseFaceAuthResult> updateBundleIdId(UpdateBundleIdIdInput input);


}
