package com.timevale.faceauth.service.input.control;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2025/7/2 11:38
 */
@Data
public class ControlRecordPageInput extends ToString {
    /**
     * 查询开始时间（创建时间大于等于该值）
     */
    private Date startTime;

    /**
     * 查询结束时间（创建时间小于等于该值）
     */
    private Date endTime;

    /**
     * 应用 ID 列表，用于筛选属于特定应用的记录
     */
    private List<String> appIds;

    /**
     * 用户名称，用于精确匹配查询
     */
    private String name;

    /**
     * 用户证件号（如身份证号），需加密传输或存储
     */
    private String idNo;

    /**
     * 证件类型，如身份证、护照等
     */
    private String idType;

    /**
     * 当前页码，用于分页查询
     */
    @NotNull(message = "pageNum 不能为空")
    @Max(value = 100 , message = "超出查询范围，请检查后在重试")
    private Integer pageNum;


    /**
     * 每页记录数，用于分页查询
     */
    @NotNull(message = "pageSize 不能为空")
    @Max(value = 100 , message = "超出查询范围，请检查后在重试")
    private Integer pageSize;
}
