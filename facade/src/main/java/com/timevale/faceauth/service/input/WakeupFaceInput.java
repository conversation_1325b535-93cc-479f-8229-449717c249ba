package com.timevale.faceauth.service.input;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE 2024/5/13 16:12
 */
@Data
public class WakeupFaceInput extends ToString {

    public interface QueryContextConstant {
        //抖音刷脸传参
        String tiktokMiniAuthCode = "tiktokMiniAuthCode";
        String tiktokSourceChannel = "tiktokSourceChannel";
        //-- 刷脸SDK传参
        /**
         * 天谷APPsdk版本值
         */
        String esignAppSdkVersion = "esignAppSdkVersion";
    }

    /**
     *  请参考{@link com.timevale.faceauth.service.enums.FaceResourceFaceAuthModeEnum}
     */
    @NotNull(message = "faceAuthModeValue 不能为空")
    private Integer faceAuthModeValue;

    /**
     * 刷脸标识
     */
    @NotBlank(message = "faceAuthCode 不能为空")
    private String faceAuthCode;

    /**
     * 不通用的参数可放里面
     */
    private Map<String, String> queryContext;

}
