package com.timevale.faceauth.service.result.sdk;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2024/8/7 18:23
 */
@Data
public class GetManualConfigResult extends ToString {


    private List<ConfigItem> sdkVersionList;
    private List<ConfigItem> channelList;

    @Data
    public static class ConfigItem extends ToString {
        private String status;
        private String name;
        private String code;
    }
}
