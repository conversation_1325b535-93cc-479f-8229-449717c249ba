package com.timevale.faceauth.service.input.sdk;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @DATE 2024/8/6 15:24
 */
@Data
public class UpdateSdkVersionStatusInput extends ToString {

    @NotBlank(message = "sdkVersionId 不能为空")
    private String sdkVersionId;

    @NotBlank(message = "status 状态不能为空")
    private String status;

    @NotBlank(message = "operator 不能为空")
    private String operator;
}
