package com.timevale.faceauth.service.result.sdk;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2024/8/6 15:32
 */
@Data
public class QuerySdkCustomerListResult extends ToString {
    private Long count = 0L;

    private List<SdkCustomerItem> customerList;

    @Data
    public static class SdkCustomerItem extends ToString {
        private String customerId;
        private String orgName;
        private String orgGid;
        private String channelNames;
        private String operator;
        private String modifyTimeStr;
        private String bundleId;
        private List<SdkCustomerVersionItem> sdkVersionList;
    }

    @Data
    public static class SdkCustomerVersionItem extends ToString {
        private String version;
        private String latestUserTime;
    }
}
