package com.timevale.faceauth.service.input.sdk;

import com.timevale.faceauth.service.input.BaseInput;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @DATE 2024/8/14 17:10
 */
@Data
public class SaveSdkVersionInput extends BaseInput {


    @NotBlank(message = "sdkVersion 不能为空")
    private String sdkVersion;

    @NotBlank(message = "status 状态不能为空")
    private String status;

    @NotBlank(message = "operator 不能为空")
    private String operator;

}
