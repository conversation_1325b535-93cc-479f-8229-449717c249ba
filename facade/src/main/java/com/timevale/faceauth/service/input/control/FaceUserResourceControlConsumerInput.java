package com.timevale.faceauth.service.input.control;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * topic = TOPIC_FACE_RESOURCE_CONTROL
 *
 * <AUTHOR>
 * @DATE 2025/7/7 14:31
 */
/**
 * FaceUserResourceControlConsumerInput类用于封装人脸识别服务的消费者输入参数
 * 它继承自ToString类，自动获取对象的字符串表示，便于调试和日志记录
 */
@Data
public class FaceUserResourceControlConsumerInput extends ToString {

    /**
     * 应用ID，用于标识调用者或数据所属的应用
     */
    private String appId;

    /**
     * 用户姓名，用于与用户身份信息关联
     */
    private String name;

    /**
     * 用户身份证号码，用于精确识别用户身份
     */
    private String idNo;

    /**
     * 证件类型，指示用户提供的身份证明类型
     */
    private String idType;

    /**
     * 来源类型，标识用户数据的来源或服务调用的上下文
     */
    private String sourceType;

    /**
     * 来源ID，与sourceType一起使用，具体标识数据来源
     */
    private String sourceId;
}
