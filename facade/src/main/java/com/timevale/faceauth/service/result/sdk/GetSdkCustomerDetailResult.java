package com.timevale.faceauth.service.result.sdk;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2024/8/7 18:08
 */
@Data
public class GetSdkCustomerDetailResult extends ToString {

    private String sdkCustomerId;
    private String orgGid;
    private String orgName;
    private String status;
    private String operator;

    private List<SdkCustomerInfoItem> customerInfoItems;

    @Data
    public static class SdkCustomerInfoItem extends ToString {
        private String customerChannelId;
        private String channelName;
        private String channelCode;
        /**
         * 允许支持更新为null
         */
        private String channelLicense;

        /**
         * 包名 2024/11/9
         */
        private String bundleId;
        /**
         // 2025/5/7
         * 鸿蒙
         */
        private String harmonyOsLicense;
    }
}
