package com.timevale.faceauth.service.input.control;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @DATE 2025/7/4 18:15
 */
@Data
public class SwitchStatusControlRecordInput extends ToString {
    @NotBlank(message = "status 不能为空")
    private String status;
    /**
     * 记录唯一标识
     */
    @NotBlank(message = "recordId 不能为空")
    private String recordId;
}
