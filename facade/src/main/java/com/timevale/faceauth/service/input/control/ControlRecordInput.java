package com.timevale.faceauth.service.input.control;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * 人脸用户资源控制记录数据对象类
 * <p>
 * 用于表示人脸用户资源控制相关的记录信息，
 * 包含控制类型、状态、用户信息及操作备注等字段。
 */
@Data
public class ControlRecordInput extends ToString {



    /**
     * 记录唯一标识
     */
    private String recordId;

    /**
     * 控制类型
     */
    private String controlType;

    /**
     * 当前状态
     */
    private String status;

    /**
     * 应用 ID，用于标识所属应用系统
     */
    private String appId;

    /**
     * 用户名称
     */
    @NotBlank(message = "名称不能为空")
    private String name;

    /**
     * 用户证件号码（如身份证号）
     */
    @NotBlank(message = "证件号不能为空")
    private String idNo;

    /**
     * 证件类型（如：身份证、护照等）
     */
    private String idType;

    /**
     * 手机号码
     */
    @NotBlank(message = "手机号码不能为空")
    private String mobileNo;

    /**
     * 备注信息，用于记录额外说明内容
     */
    private String remark;

    /**
     * 证明材料，用于存储相关凭证信息（如文件路径或 Base64 编码数据）
     */
    private List<ProofDocumentDTO> proofDocumentList;

    /**
     * 备注信息，用于记录额外说明内容
     */
    private String operator;


    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 编辑时间
     */
    private Date modifyTime;

    @Data
    public static class ProofDocumentDTO extends ToString{

        private String fileKey;

        @Deprecated
        private String downloadUrl;
    }

}
