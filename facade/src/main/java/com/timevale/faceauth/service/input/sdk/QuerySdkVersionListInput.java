package com.timevale.faceauth.service.input.sdk;

import com.timevale.faceauth.service.input.BaseInput;
import lombok.Data;

import java.util.Optional;

/**
 * <AUTHOR>
 * @DATE 2024/8/6 15:22
 */
@Data
public class QuerySdkVersionListInput extends BaseInput {
    public static int DEFAULT_NUM = 1;
    public static int DEFAULT_SIZE = 10;

    private Integer pageNum;
    private Integer pageSize;

    public Integer getPageNum() {
        return Optional.ofNullable(pageNum).orElse(DEFAULT_NUM);
    }

    public Integer getPageSize() {
        return Optional.ofNullable(pageSize).orElse(DEFAULT_SIZE);
    }
}
