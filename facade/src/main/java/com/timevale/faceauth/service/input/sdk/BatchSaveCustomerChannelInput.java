package com.timevale.faceauth.service.input.sdk;

import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @DATE 2024/8/6 15:35
 */
@Data
public class BatchSaveCustomerChannelInput extends ToString {

    @NotBlank(message = "企业GID不能为空")
    private String orgGid;

    @NotBlank(message = "企业名称不能为空")
    private String orgName;

    @NotBlank(message = "operator 操作人不能为空")
    private String operator;

    @NotEmpty(message = "customerChannelList 客户渠道列表不能为空")
    private List<BatchSaveCustomerChannelItem> customerChannelList;

    @Data
    public static class BatchSaveCustomerChannelItem extends ToString {
        private String customerChannelId;
        @NotBlank(message = "channelCode 不能为空")
        private String channelCode;
        /**
         * 允许支持更新为null
         */
        private String channelLicense;

        /**
         * 包名
         */
        @NotBlank(message = "bundleId 不能为空")
        private String bundleId;

        /**
         * 允许支持更新为null 鸿蒙的License
         */
        private String harmonyOsLicense;
    }


    public String findFirstBundleId() {
        return Optional.ofNullable(this.getCustomerChannelList()).orElse(Collections.emptyList())
                .stream()
                .filter(e -> StringUtils.isNotBlank(e.getBundleId()))
                .map(BatchSaveCustomerChannelItem::getBundleId)
                .findFirst().orElse(null);
    }


}
