package com.timevale.faceauth.service.input.sdk;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @DATE 2024/8/6 15:22
 */
@Data
public class QuerySdkCustomerListInput extends ToString {
    public static int DEFAULT_NUM = 1;
    public static int DEFAULT_SIZE = 10;
    /**
     * 企业GID
     */
    private  List<String> orgGidList;

    private List<String> sdkVersionList;
    private List<String> channelCodeList;

    private Integer pageNum;
    private Integer pageSize;

    public Integer getPageNum() {
        return Optional.ofNullable(pageNum).orElse(DEFAULT_NUM);
    }

    public Integer getPageSize() {
        return Optional.ofNullable(pageSize).orElse(DEFAULT_SIZE);
    }
}
