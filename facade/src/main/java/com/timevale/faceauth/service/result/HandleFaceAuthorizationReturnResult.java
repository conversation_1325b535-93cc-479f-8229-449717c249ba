package com.timevale.faceauth.service.result;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * <AUTHOR>
 * @DATE 2024/5/21 15:11
 */
@Data
@Accessors(chain = true)
public class HandleFaceAuthorizationReturnResult extends ToString {


    public interface ExtendedMapConstant{
        String tiktokReturnType = "tiktokReturnType";
        String tiktokTipsStatus = "tiktokTipsStatus";
    }

    // 刷脸认证错误码
    private String errCode;

    /**
     * 返回结果描述
     */
    private String msg;

    /**
     * 人脸认证完成状态
     */
    private boolean completed;

    /**
     * 是否通过
     * passed 直接代表验证结果
     */
    private Boolean passed;


    /**
     * 返回扩展字段
     */
    private Map<String, Object> extendedMap;

}
