package com.timevale.faceauth.service.result;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * <AUTHOR>
 * @DATE 2024/5/13 16:20
 */
@Data
@Accessors(chain = true)
public class WakeupFaceResult extends ToString {

    /**
     *
     */
    private String faceAuthMode;


    /**
     * 抖音票据
     */
    private String ticket;

    /**
     * 认证人姓名
     */
    private String identityName;

//    /**
//     * 是否降级
//     */
//    private boolean downgrade;


    /**
     * 刷脸H5
     */
    private String identityUrl;



    /**
     * 刷脸上下文
     */
    private Map<String, String> faceContext;

    /**
     * 配置数据
     */
    private WakeupFaceConfigDomain config;


    @Data
    public static class WakeupFaceConfigDomain extends ToString{
        private String resultH5Url;
    }
}
