package com.timevale.faceauth.service.enums;

import com.timevale.mandarin.base.util.StringUtils;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @DATE 2024/8/6 16:02
 */
@Getter
public enum SdkStatusEnum {
    ENABLE("ENABLE","启用"),
    DISENABLE("DISENABLE","停用")
    ;

    SdkStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private String code;
    private String desc;

    public static String ofDesc(String status) {
        SdkStatusEnum of = of(status);
        return Objects.isNull(of) ? null : of.getDesc();
    }

    public static SdkStatusEnum of(String status) {
        for (SdkStatusEnum value : values()) {
            if (StringUtils.equals(status, value.getCode())) {
                return value;
            }
        }
        return null;
    }
}
