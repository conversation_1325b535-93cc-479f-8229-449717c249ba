package com.timevale.faceauth.service.result.control;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @DATE 2025/7/7 11:16
 */
@Data
public class ControlFaceDataPrivilegeResult extends ToString {

    private boolean refusePhotoAndVideo;

    private String refusePhotoAndVideoRecordId;

    private Date refusePhotoAndVideoRecordTime;


    private String name;

}
