package com.timevale.faceauth.service.input.sdk;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @DATE 2024/8/7 18:07
 */
@Data
public class GetSdkCustomerDetailInput extends ToString {

//    @NotBlank(message = "企业GID不能为空")
    private String orgGid;

    @NotBlank(message = "sdkCustomerId不能为空")
    private String sdkCustomerId;
}
