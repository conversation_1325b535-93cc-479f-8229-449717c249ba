package com.timevale.faceauth.service.input;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE 2024/5/15 17:33
 */
@Data
@Accessors(chain = true)
public class HandleFaceAuthorizationReturnInput extends ToString {

    public interface ExtendedMapConstant{
        String errCode = "errCode";
        String errMsg = "errMsg";
        String errNo = "errNo";
        String originalReturnData = "originalReturnData";
    }

    /**
     *  请参考{@link com.timevale.faceauth.service.enums.FaceResourceFaceAuthModeEnum}
     */
    @NotNull(message = "faceAuthModeValue 不能为空")
    private Integer faceAuthModeValue;
    /**
     * 刷脸标识
     */
    @NotBlank(message = "faceAuthCode 不能为空")
    private String faceAuthCode;


    /**
     * 扩展信息
     * key errCode
     * key2 errMsg
     */
    private Map<String, String> extendedMap;

}
