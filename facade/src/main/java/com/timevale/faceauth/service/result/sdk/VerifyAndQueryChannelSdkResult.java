package com.timevale.faceauth.service.result.sdk;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2024/8/6 11:29
 */
@Data
public class VerifyAndQueryChannelSdkResult extends ToString {

    /**
     * 请求接口后端接口的一级域名
     */
    private String urlDomain;

    /**
     *
     */
    private List<AppSdkEntity> inlayAppSdkLicenseList;


    @Data
    public static class  AppSdkEntity extends ToString{
        private String sdkChannel;
        private String appSdkLicense;
        private String wbAppId;
        // 2025/5/7
        private String harmonyOsLicense;
    }


}
