package com.timevale.faceauth.service.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @DATE 2024/8/13 11:09
 */
@Getter
public enum FaceResourceFaceAuthModeEnum {

    FACE_TIKTOK_MINI(FaceAuthModeEnum.FACE_TIKTOK_MINI.getMode()),
    APP_FACE_SDK(18),
    ;


    private Integer mode;

    FaceResourceFaceAuthModeEnum(Integer mode) {
        this.mode = mode;
    }

    public static FaceResourceFaceAuthModeEnum nameOf(String name) {
        for (FaceResourceFaceAuthModeEnum authModeEnum : FaceResourceFaceAuthModeEnum.values()) {
            if (authModeEnum.name().equals(name)) {
                return authModeEnum;
            }
        }
        return null;
    }
}
