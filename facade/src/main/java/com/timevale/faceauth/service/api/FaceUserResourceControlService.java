package com.timevale.faceauth.service.api;

import com.timevale.faceauth.service.input.control.ControlRecordInput;
import com.timevale.faceauth.service.input.control.ControlRecordPageInput;
import com.timevale.faceauth.service.input.control.ControlRecordQueryDetailInput;
import com.timevale.faceauth.service.input.control.ControlRecordSaveInput;
import com.timevale.faceauth.service.input.control.SwitchStatusControlRecordInput;
import com.timevale.faceauth.service.result.BaseFaceAuthResult;
import com.timevale.faceauth.service.result.SupportResult;
import com.timevale.faceauth.service.result.control.ControlRecordPageResult;
import com.timevale.faceauth.service.result.control.ControlRecordResult;
import com.timevale.mandarin.common.annotation.RestClient;

/**
 * <AUTHOR>
 * @DATE 2025/7/1 17:57
 */

/**
 * 人脸用户资源控制服务接口
 * <p>
 * 提供对人脸用户资源控制记录的管理操作，包括新增、查询详情、修改及分页查询等功能。
 */
@RestClient(serviceId = "faceauth-service")
public interface FaceUserResourceControlService {

    /**
     * 批量保存控制记录
     *
     * @param input 控制记录保存请求参数
     * @return 操作结果封装对象，包含基础人脸认证结果
     */
    SupportResult<BaseFaceAuthResult> batchSaveControlRecord(ControlRecordSaveInput input);

    /**
     * 查询控制记录详情
     *
     * @param input 控制记录详情查询参数
     * @return 操作结果封装对象，包含控制记录详细信息
     */
    SupportResult<ControlRecordResult> detailControlRecord(ControlRecordQueryDetailInput input);

    /**
     * 修改控制记录
     *
     * @param input 控制记录修改请求参数
     * @return 操作结果封装对象，包含更新后的基础人脸认证结果
     */
    SupportResult<BaseFaceAuthResult> modifyControlRecord(ControlRecordInput input);

    /**
     * 分页查询控制记录
     *
     * @param input 分页查询请求参数
     * @return 操作结果封装对象，包含分页返回数据及控制记录列表
     */
    SupportResult<ControlRecordPageResult> pageControlRecord(ControlRecordPageInput input);


    /**
     * 切换状态
     *
     * @param input 分页查询请求参数
     * @return 操作结果封装对象，包含分页返回数据及控制记录列表
     */
    SupportResult<BaseFaceAuthResult> switchStatusControlRecord(SwitchStatusControlRecordInput input);
}
