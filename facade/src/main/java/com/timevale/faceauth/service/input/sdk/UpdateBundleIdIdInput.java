package com.timevale.faceauth.service.input.sdk;

import com.timevale.faceauth.service.input.BaseInput;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @DATE 2024/11/12 10:01
 */
@Data
public class UpdateBundleIdIdInput extends BaseInput {


    @NotBlank(message = "sdkCustomerId不能为空")
    private String sdkCustomerId;
    /**
     * 包名 2024/11/9
     */
    @NotBlank(message = "bundleId不能为空")
    private String bundleId;


}
