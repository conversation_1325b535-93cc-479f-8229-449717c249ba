package com.timevale.faceauth.dal.pfs.face.support;

import com.timevale.faceauth.dal.pfs.support.DbFieldInfo;
import com.timevale.faceauth.dal.pfs.support.DbTableName;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/10 17
 */
@DbTableName("tb_provider_face")
public class ProviderFaceCompletionDO extends FaceIdDO {

  // 当前供应商刷脸完成状态。（刷脸完成：1）
  @DbFieldInfo(value = "is_done", isRequired = true)
  public byte isDone = 0x00;

  @DbFieldInfo("err_msg")
  public String errMsg;

  @DbFieldInfo("err_code")
  public String errCode;

  // 完成时间
  @DbFieldInfo(value = "done_time", isRequired = true)
  public long doneTime = 0L;
}
