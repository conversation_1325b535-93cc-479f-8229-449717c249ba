package com.timevale.faceauth.dal.pfs.liveness;

import com.timevale.faceauth.dal.pfs.support.DbFieldInfo;
import com.timevale.faceauth.dal.pfs.support.DbTableName;

/**
 * 第三方服务商活体行为指定实体
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/6/24
 */
@DbTableName("tb_liveness_provider_order")
public class ProviderLivenessSpecifyActionsEntity {

    /** 活体认证 ID */
    @DbFieldInfo(value = "liveness_id", isPrimary = true)
    public String livenessId;

    /** 描述一组活体行为 */
    @DbFieldInfo("actions")
    public String actions;
}
