package com.timevale.faceauth.dal.pfs.dao;

import com.timevale.faceauth.dal.pfs.dataobject.FaceauthAliTencentDo;
import com.timevale.faceauth.dal.pfs.provider.FaceauthAliTencentProvider;
import com.timevale.faceauth.dal.pfs.face.support.FaceAuthQueryRequestDO;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;

import java.util.List;

/** <AUTHOR> MASTER ! */
public interface FaceauthAliTencentDAO {

  /**
   * desc:插入表:faceauth_ali_tencent.<br>
   *
   * @param entity entity
   * @return int
   */
  @InsertProvider(type = FaceauthAliTencentProvider.class, method = "insert")
  int insert(FaceauthAliTencentDo entity);

  /**
   * 主键查询
   *
   * @param faceAuthId
   */
  @SelectProvider(type = FaceauthAliTencentProvider.class, method = "findById")
  FaceauthAliTencentDo getById(String faceAuthId);

  /**
   * 更新
   *
   * @param faceauthAliTencentDo
   */
  @UpdateProvider(type = FaceauthAliTencentProvider.class, method = "update")
  void update(FaceauthAliTencentDo faceauthAliTencentDo);

  /**
   * desc:专为快捷签出证查询使用
   *
   * @param faceAuthQueryRequestDO
   * @return List<InfoServiceBankthreeDO>
   */
  @Select({
          "<script>",
          "select * from faceauth_ali_tencent ",
          "where  1=1 ",
          "<if test='idno != null'> and id_no = #{idno} </if>",
          "<if test='status != null'> and status = #{status} </if>",
          "<if test='modifyTime != null'> and update_time between 0 and #{modifyTime} </if>",
          "order by update_time desc limit #{currIndex},#{pageSize}",
          "</script>"})
  List<FaceauthAliTencentDo> queryByUpdateTimeAndIdNo(FaceAuthQueryRequestDO faceAuthQueryRequestDO);

  @Select({
          "<script>",
          "select * from faceauth_ali_tencent fat inner join faceauth fu on fat.id = fu.id ",
          "where  1=1 ",
          "<if test='idno != null'> and fat.id_no = #{idno} </if>",
          "<if test='status != null'> and fat.status = #{status} </if>",
          "<if test='modifyTime != null'> and fat.update_time between 0 and #{modifyTime} </if>",
          "order by fat.update_time desc limit #{currIndex},#{pageSize}",
          "</script>"})
  List<FaceauthAliTencentDo> quickSignQueryListByALitencent(FaceAuthQueryRequestDO faceAuthQueryRequestDO);
}
