package com.timevale.faceauth.dal.pfs.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * The table. 供应商错误码枚举
 *
 * <AUTHOR> Kunpeng
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProviderErrCodeEnumDO {

  /** id 自增主键. */
  private Long id;
  /** pCodeId 业务主键. */
  private String pCodeId;
  /** provider 供应商名称. */
  private String providerId;
  /** bizCodeId 适配错误. */
  private String providerKey;
  /** 标记状态. */
  private int markStatus;
  /** pCode 供应商错误码. */
  private String pCode;
  /** pMsg */
  private String pMsg;
  /** bizMd5 provider_id+code+msg. */
  private String bizMd5;
  /** createTime 记录创建时间. */
  private Date createTime;
  /** modifyTime 记录更新时间. */
  private Date modifyTime;
}
