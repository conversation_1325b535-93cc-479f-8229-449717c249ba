package com.timevale.faceauth.dal.pfs.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * The table. 指标配置
 *
 * <AUTHOR> Kunpeng
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndexMetaDO {

  /** 自增主键. */
  private Long id;
  /** 指标唯一标识. */
  private String indexKey;
  /** 指标名称. */
  private String indexName;
  /** 指标分组 1-稳定性规则指标，2-稳定性统计指标 */
  private Integer indexType;
  /** createTime 记录创建时间. */
  private Date createTime;
  /** modifyTime 记录修改时间. */
  private Date modifyTime;
}
