package com.timevale.faceauth.dal.pfs.liveness;

import com.timevale.faceauth.dal.pfs.support.AutoIncrementIdentityStandardDO;
import com.timevale.faceauth.dal.pfs.support.DbFieldInfo;
import com.timevale.faceauth.dal.pfs.support.DbTableName;

/**
 * 第三方供应商调用过程实体
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/2/20
 */
@DbTableName("tb_provider_invocation")
public class ProviderInvocationEntity extends AutoIncrementIdentityStandardDO {

  /** 业务调用 ID */
  @DbFieldInfo(value = "invoke_id", isRequired = true)
  public String invokeId;

  /** 业务类型编码 */
  @DbFieldInfo(value = "biz_code", isRequired = true)
  public String bizCode;

  /** 业务类型 ID */
  @DbFieldInfo(value = "biz_id", isRequired = true)
  public String bizId;

  /** 供应商编码 */
  @DbFieldInfo(value = "provider_code", isRequired = true)
  public String providerCode;

  /** 当前调用发生错误状态：发生错误（1）；未发生错误（0） */
  @DbFieldInfo("err")
  public byte err = 0x00;

  /** 当前调用发生的错误消息 */
  @DbFieldInfo(value = "err_msg")
  public String errMsg = "";

  /** 请求内容，详细数据结合 content_location 字段获得 */
  @DbFieldInfo(value = "request_id")
  public String requestId;

  /** 调用方式 */
  @DbFieldInfo(value = "request_type")
  public String requestType;

  /** 调用 API */
  @DbFieldInfo(value = "request_api")
  public String requestApi;

  /** 调用方法 */
  @DbFieldInfo(value = "request_method")
  public String requestMethod;

  /** 请求内容，详细数据结合 content_location 字段获得 */
  @DbFieldInfo(value = "request", isRequired = true)
  public String request;

  /** 请求时间 */
  @DbFieldInfo("request_time")
  public long requestTime = System.currentTimeMillis();

  /** 当前调用的响应状态：有响应（1）；无响应（0） */
  @DbFieldInfo("responsed")
  public byte responsed = 0x00;

  /** 响应数据内容，详细数据结合 content_location 字段获得 */
  @DbFieldInfo(value = "response")
  public String response = "";

  /** 响应时间 */
  @DbFieldInfo("responsed_time")
  public long responsedTime = 0L;

  /** 当前调用发生的数据内容存放的位置 */
  @DbFieldInfo(value = "content_location", isRequired = true)
  public String contentLocation;
}
