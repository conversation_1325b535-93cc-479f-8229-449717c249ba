package com.timevale.faceauth.dal.pfs.dataobject;

import java.util.Date;

/**
 * The table. faceauth_tencent_h5
 *
 * <AUTHOR> Kunpeng
 */
public class FaceauthTencentH5DO {

  /** createTime 记录创建时间. */
  private Date createTime;
  /** updateTime 记录更新时间. */
  private Date updateTime;
  /** id ID.主键，为主表faceauth的主键，也为人脸识别业务id */
  private String id;
  /** code 人脸验证结果的返回码，0 表示人脸验证成功，其他错误码表示失败. */
  private String code;
  /** idNo 用户输入身份证号. */
  private String idNo;
  /** name 用户输入姓名. */
  private String name;
  /** idType 证件类型. */
  private String idType;
  /** userId 业务方用户ID. */
  private String userId;
  /** orderNo 人脸认证系统生成的订单号. */
  private String orderNo;
  /** bizSeqNo 腾讯云业务流水号. */
  private String bizSeqNo;
  /** liveRate 活体检测得分. */
  private String liveRate;
  /** faceAuthId 人脸认证业务ID. */
  private String h5FaceId;
  /** photoInput 比对源照片OSS Key. */
  private String photoInput;
  /** similarity 人脸比对得分. */
  private String similarity;
  /** callbackUrl 业务方回调通知地址. */
  private String callbackUrl;
  /** photoType 比对源照片类型，1-水纹正脸照；2-高清正脸照. */
  private Integer photoType;

  /** authPhoto 刷脸认证的照片 */
  private String authPhoto;

  /** 刷脸的时间 */
  private String occurredTime;

  /** 是否是小程序 */
  private Integer xcx;

  /**
   * Get createTime 记录创建时间.
   *
   * @return the string
   */
  public Date getCreateTime() {
    return createTime;
  }

  /** Set createTime 记录创建时间. */
  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  /**
   * Get updateTime 记录更新时间.
   *
   * @return the string
   */
  public Date getUpdateTime() {
    return updateTime;
  }

  /** Set updateTime 记录更新时间. */
  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  /**
   * Get id ID.
   *
   * @return the string
   */
  public String getId() {
    return id;
  }

  /** Set id ID. */
  public void setId(String id) {
    this.id = id;
  }

  /**
   * Get code 人脸验证结果的返回码，0 表示人脸验证成功，其他错误码表示失败.
   *
   * @return the string
   */
  public String getCode() {
    return code;
  }

  /** Set code 人脸验证结果的返回码，0 表示人脸验证成功，其他错误码表示失败. */
  public void setCode(String code) {
    this.code = code;
  }

  /**
   * Get idNo 用户输入身份证号.
   *
   * @return the string
   */
  public String getIdNo() {
    return idNo;
  }

  /** Set idNo 用户输入身份证号. */
  public void setIdNo(String idNo) {
    this.idNo = idNo;
  }

  /**
   * Get name 用户输入姓名.
   *
   * @return the string
   */
  public String getName() {
    return name;
  }

  /** Set name 用户输入姓名. */
  public void setName(String name) {
    this.name = name;
  }

  /**
   * Get idType 证件类型.
   *
   * @return the string
   */
  public String getIdType() {
    return idType;
  }

  /** Set idType 证件类型. */
  public void setIdType(String idType) {
    this.idType = idType;
  }

  /**
   * Get userId 业务方用户ID.
   *
   * @return the string
   */
  public String getUserId() {
    return userId;
  }

  /** Set userId 业务方用户ID. */
  public void setUserId(String userId) {
    this.userId = userId;
  }

  /**
   * Get orderNo 人脸认证系统生成的订单号.
   *
   * @return the string
   */
  public String getOrderNo() {
    return orderNo;
  }

  /** Set orderNo 人脸认证系统生成的订单号. */
  public void setOrderNo(String orderNo) {
    this.orderNo = orderNo;
  }

  /**
   * Get bizSeqNo 腾讯云业务流水号.
   *
   * @return the string
   */
  public String getBizSeqNo() {
    return bizSeqNo;
  }

  /** Set bizSeqNo 腾讯云业务流水号. */
  public void setBizSeqNo(String bizSeqNo) {
    this.bizSeqNo = bizSeqNo;
  }

  /**
   * Get liveRate 活体检测得分.
   *
   * @return the string
   */
  public String getLiveRate() {
    return liveRate;
  }

  /** Set liveRate 活体检测得分. */
  public void setLiveRate(String liveRate) {
    this.liveRate = liveRate;
  }

  /** Set faceAuthId 腾讯云h5人脸识别id. */
  public String getH5FaceId() {
    return h5FaceId;
  }

  /**
   * Get faceAuthId 腾讯云h5人脸识别id.
   *
   * @return the string
   */
  public void setH5FaceId(String h5FaceId) {
    this.h5FaceId = h5FaceId;
  }

  /**
   * Get photoInput 比对源照片OSS Key.
   *
   * @return the string
   */
  public String getPhotoInput() {
    return photoInput;
  }

  /** Set photoInput 比对源照片OSS Key. */
  public void setPhotoInput(String photoInput) {
    this.photoInput = photoInput;
  }

  /**
   * Get similarity 人脸比对得分.
   *
   * @return the string
   */
  public String getSimilarity() {
    return similarity;
  }

  /** Set similarity 人脸比对得分. */
  public void setSimilarity(String similarity) {
    this.similarity = similarity;
  }

  /**
   * Get callbackUrl 业务方回调通知地址.
   *
   * @return the string
   */
  public String getCallbackUrl() {
    return callbackUrl;
  }

  /** Set callbackUrl 业务方回调通知地址. */
  public void setCallbackUrl(String callbackUrl) {
    this.callbackUrl = callbackUrl;
  }

  /**
   * Get photoType 比对源照片类型，1-水纹正脸照；2-高清正脸照.
   *
   * @return the string
   */
  public Integer getPhotoType() {
    return photoType;
  }

  /** Set photoType 比对源照片类型，1-水纹正脸照；2-高清正脸照. */
  public void setPhotoType(Integer photoType) {
    this.photoType = photoType;
  }

  public String getAuthPhoto() {
    return authPhoto;
  }

  public void setAuthPhoto(String authPhoto) {
    this.authPhoto = authPhoto;
  }

  public String getOccurredTime() {
    return occurredTime;
  }

  public void setOccurredTime(String occurredTime) {
    this.occurredTime = occurredTime;
  }

  public Integer getXcx() {
    return xcx;
  }

  public void setXcx(Integer xcx) {
    this.xcx = xcx;
  }

  @Override
  public String toString() {
    return super.toString();
  }
}
