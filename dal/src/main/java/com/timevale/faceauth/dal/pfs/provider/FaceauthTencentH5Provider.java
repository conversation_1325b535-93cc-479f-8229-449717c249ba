package com.timevale.faceauth.dal.pfs.provider;

import com.timevale.faceauth.dal.pfs.dataobject.FaceauthTencentH5DO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.jdbc.SQL;

/**
 * <AUTHOR> on 2018/7/23
 *     <p>人脸识别腾讯h5副表provider
 */
public class FaceauthTencentH5Provider {

  private static final String TB_NAME = "faceauth_tencent_h5";

  /**
   * 插入
   *
   * @param entity
   * @return
   */
  public String insert(FaceauthTencentH5DO entity) {
    return new SQL() {
      {
        INSERT_INTO(TB_NAME);

        VALUES("id", "#{id}");
        VALUES("h5_face_id", "#{h5FaceId}");
        VALUES("name", "#{name}");
        VALUES("id_no", "#{idNo}");
        VALUES("photo_input", "#{photoInput}");
        VALUES("photo_type", "#{photoType}");
        VALUES("user_id", "#{userId}");
        VALUES("order_no", "#{orderNo}");
        VALUES("callback_url", "#{callbackUrl}");
        VALUES("biz_seq_no", "#{bizSeqNo}");
        VALUES("code", "#{code}");
        VALUES("id_type", "#{idType}");
        VALUES("live_rate", "#{liveRate}");
        VALUES("similarity", "#{similarity}");
        VALUES("xcx", "#{xcx}");
        VALUES("create_time", "now()");
        VALUES("update_time", "now()");
      }
    }.toString();
  }

  /**
   * 根据id查询
   *
   * @param id
   * @return
   */
  public String findById(@Param("id") String id) {
    return new SQL() {
      {
        SELECT("*");
        FROM(TB_NAME);
        WHERE("id=#{id}");
      }
    }.toString();
  }

  /**
   * @param entity
   * @return
   */
  public String update(FaceauthTencentH5DO entity) {
    return new SQL() {
      {
        UPDATE(TB_NAME);

        SET("code=#{code}");
        SET("id_type=#{idType}");
        SET("live_rate=#{liveRate}");
        SET("similarity=#{similarity}");
        SET("auth_photo=#{authPhoto}");
        SET("occurred_time=#{occurredTime}");
        SET("update_time=now()");

        WHERE("id=#{id}");
      }
    }.toString();
  }
}
