package com.timevale.faceauth.dal.pfs.manager;

import com.timevale.faceauth.dal.pfs.dataobject.DegradationConfigDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/** 供应商降级配置 */
public interface DegradationConfigDAO {

  @Select({
    "<script>",
    "SELECT `id`,`config_id`,`provider_id`,`switch_enabled`,`fire_alert_ids`,`auto_recovery_enabled`,`recovery_count_down`,`standby_provider_id`,`create_time`,`modify_time` FROM `provider_degradation_config`",
    " WHERE `switch_enabled`=#{enabled} LIMIT 20",
    "</script>",
  })
  List<DegradationConfigDO> getAllSwitchEnabled(Integer enabled);

  @Select({
    "<script>",
    "SELECT `id`,`config_id`,`provider_id`,`switch_enabled`,`fire_alert_ids`,`auto_recovery_enabled`,`recovery_count_down`,`standby_provider_id`,`create_time`,`modify_time` FROM `provider_degradation_config`",
    " WHERE `provider_id`=#{providerId} LIMIT 1",
    "</script>",
  })
  DegradationConfigDO getByProviderId(String providerId);

  @Update(
      "UPDATE provider_degradation_config SET `switch_enabled`=#{enabled} where `provider_id`=#{providerId}")
  void updateSwitchEnabled(
      @Param("providerId") String providerId, @Param("enabled") Integer enabled);

  @Update(
      "UPDATE provider_degradation_config SET `auto_recovery_enabled`=#{autoRecoveryEnabled} where `provider_id`=#{providerId}")
  void updateAutoRecoveryEnabled(
      @Param("providerId") String providerId, @Param("autoRecoveryEnabled") Integer autoRecoveryEnabled);

  @Update(
      "UPDATE provider_degradation_config SET `recovery_count_down`=#{autoRecoveryCountDown} where `provider_id`=#{providerId}")
  void updateAutoRecoveryCountDown(
      @Param("providerId") String providerId,
      @Param("autoRecoveryCountDown") Integer autoRecoveryCountDown);
}
