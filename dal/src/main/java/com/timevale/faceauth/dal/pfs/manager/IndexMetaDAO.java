package com.timevale.faceauth.dal.pfs.manager;

import com.timevale.faceauth.dal.pfs.dataobject.IndexMetaDO;
import org.apache.ibatis.annotations.Select;

/** 指标配置 */
public interface IndexMetaDAO {

  @Select({
    "<script>",
    "SELECT `id`,`index_key`,`index_name`,`index_type`,`extend` FROM `provider_index_meta`",
    " WHERE `index_key`=#{indexKey} LIMIT 1",
    "</script>",
  })
  IndexMetaDO getByIndexKey(String indexKey);
}
