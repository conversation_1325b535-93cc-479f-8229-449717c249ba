package com.timevale.faceauth.dal.pfs.dataobject;

import com.timevale.kmssdk.encryption.Encryption;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 人脸用户资源控制记录数据对象类
 * <p>
 * 用于表示人脸用户资源控制相关的记录信息，
 * 包含控制类型、状态、用户信息及操作备注等字段。
 */
@Data
@Slf4j
public class FaceUserResourceControlRecordDO extends ToString {

    /**
     * 主键 ID
     */
    private Long id;

    /**
     * 记录唯一标识，用于唯一标识一条控制记录
     */
    private String recordId;

    /**
     * 控制类型，
     */
    private String controlType;

    /**
     * 当前状态，如：
     * @see com.timevale.faceauth.service.domain.controll.FaceResourceControlStatusEnum
     */
    private String status;

    /**
     * 应用 ID，用于标识该记录所属的应用系统
     */
    private String appId;

    /**
     * 用户名称，被控制用户的姓名
     */
    private String name;

    /**
     * 用户证件号码（如身份证号），需加密存储或传输
     */
    @Encryption
    private String idNo;

    /**
     * 证件类型，如：身份证、护照等
     */
    private String idType;

    /**
     * 手机号码，用户联系方式，需加密存储或传输
     */
    @Encryption
    private String mobileNo;

    /**
     * 备注信息，用于记录额外说明内容
     */
    private String remark;

    private String operator;

    /**
     * 证明材料，用于存储相关凭证信息（如文件路径或 Base64 编码数据）
     */
    private String proofDocuments;

    /**
     * The type of the source. This field is used to describe the source type of the data.
     */
    private String sourceType = StringUtils.EMPTY;

    /**
     * The  business identifier of the source. This field is used to uniquely identify a piece of business data.
     */
    private String sourceBizId = StringUtils.EMPTY;

    /**
     * 创建时间，记录该条控制信息的创建时间
     */
    private Date createTime;

    /**
     * 修改时间，记录该条控制信息最后一次修改的时间
     */
    private Date modifyTime;


    private ProofDocumentDO proofDocumentDO;


    public ProofDocumentDO getProofDocumentDO() {
        if (Objects.nonNull(proofDocumentDO)) {
            return proofDocumentDO;
        }
        try {
            return proofDocumentDO = JsonUtils.json2pojo(proofDocuments, ProofDocumentDO.class);
        } catch (Exception e) {
            log.warn("Fail to parse proof documents: " + proofDocuments, e);
            return new ProofDocumentDO();
        }
    }

    @Data
    public static class ProofDocumentDO extends ToString {
        private List<String> fileKeys;
    }


}
