package com.timevale.faceauth.dal.pfs.liveness;

import com.timevale.faceauth.dal.pfs.liveness.support.ProviderLivenessEntitySqlProvider;
import org.apache.ibatis.annotations.*;

/**
 * 供应商活体认证库
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/2/20
 */
public interface ProviderLivenessEntityRepository {

  @InsertProvider(type = ProviderLivenessEntitySqlProvider.class, method = "insert")
  @SelectKey(
      statement = "select last_insert_id()",
      keyProperty = "e.id",
      before = false,
      resultType = long.class)
  int insert(@Param("e") ProviderLivenessEntity entity);

  @SelectProvider(type = ProviderLivenessEntitySqlProvider.class, method = "getByLivenessId")
  ProviderLivenessEntity getByLivenessId(@Param("livenessId") String livenessId);

  @UpdateProvider(
      type = ProviderLivenessEntitySqlProvider.class,
      method = "completedProviderLiveness")
  int completedProviderLiveness(ProviderLivenessCompletedEntity entity);

  @UpdateProvider(
      type = ProviderLivenessEntitySqlProvider.class,
      method = "specifyLivenessActions")
  int specifyLivenessActions(ProviderLivenessSpecifyActionsEntity entity);
}
