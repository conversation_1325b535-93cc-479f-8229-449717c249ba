package com.timevale.faceauth.dal.pfs.dataobject;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * The table. 供应商元数据
 *
 * <AUTHOR> Kunpeng
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProviderMetaDO extends ToString {

  /** 自增主键. */
  private Long id;
  /** 供应商ID. */
  private String providerId;
  /** 所属空间下的唯一标识. */
  private String providerKey;
  /** 供应商名称. */
  private String providerName;
  /** 所属空间ID. */
  private String spaceId;
  /** createTime 记录创建时间. */
  private Date createTime;
  /** modifyTime 记录修改时间. */
  private Date modifyTime;
}
