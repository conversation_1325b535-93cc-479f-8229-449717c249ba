package com.timevale.faceauth.dal.pfs.liveness;

import com.timevale.faceauth.dal.pfs.liveness.support.ProviderLivenessVideoEntitySqlProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;

/**
 * 供应商活体认证视频库
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/2/20
 */
public interface ProviderLivenessVideoEntityRepository {

  @InsertProvider(type = ProviderLivenessVideoEntitySqlProvider.class, method = "insert")
  @SelectKey(
      statement = "select last_insert_id()",
      keyProperty = "e.id",
      before = false,
      resultType = long.class)
  int insert(@Param("e") ProviderLivenessVideoEntity entity);

  @SelectProvider(type = ProviderLivenessVideoEntitySqlProvider.class, method = "getVideos")
  List<ProviderLivenessVideoEntity> getVideos(@Param("livenessId") String livenessId);
}
