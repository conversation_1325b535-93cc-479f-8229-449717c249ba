package com.timevale.faceauth.dal.pfs.dao;

import com.timevale.faceauth.dal.pfs.dataobject.FaceauthDO;
import com.timevale.faceauth.dal.pfs.provider.FaceauthProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;

import java.util.List;

/**
 * The Table faceauth. faceauth
 *
 * <AUTHOR> Kunpeng
 */
public interface FaceauthDAO {

  /**
   * desc:插入表:faceauth.<br>
   *
   * @param entity entity
   * @return int
   */
  @InsertProvider(type = FaceauthProvider.class, method = "insert")
  int insert(FaceauthDO entity);

  /**
   * 修改
   *
   * @param entity
   */
  @UpdateProvider(type = FaceauthProvider.class, method = "update")
  void update(FaceauthDO entity);

  /**
   * desc:批量插入表:faceauth.<br>
   *
   * @param list list
   * @return int
   */
  int insertBatch(List<FaceauthDO> list);

  /**
   * desc:根据主键删除数据:faceauth.<br>
   *
   * @param id id
   * @return int
   */
  int deleteById(String id);

  /**
   * desc:根据主键获取数据:faceauth.<br>
   *
   * @param id id
   * @return FaceauthDO
   */
  @SelectProvider(type = FaceauthProvider.class, method = "findById")
  FaceauthDO getById(String id);
}
