package com.timevale.faceauth.dal.pfs.liveness;

import com.timevale.faceauth.dal.pfs.liveness.support.LivenessUserPhotoEntitySqlProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;

/**
 * 活体认证用户有效照片库
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/2/20
 */
public interface LivenessUserPhotoEntityRepository {

  @InsertProvider(type = LivenessUserPhotoEntitySqlProvider.class, method = "insert")
  @SelectKey(
      statement = "select last_insert_id()",
      keyProperty = "e.id",
      before = false,
      resultType = long.class)
  int insert(@Param("e") LivenessUserPhotoEntity entity);

  @SelectProvider(type = LivenessUserPhotoEntitySqlProvider.class, method = "getByHash")
  LivenessUserPhotoEntity getByHash(@Param("hash") String hash);
}
