package com.timevale.faceauth.dal.pfs.dataobject;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version $ Id: FaceSwitchDO.java, v0.1 2020年11月12日 17:42 WangYuWu $
 */
@Data
public class FaceSwitchDO {

  /** 主键ID */
  private String  id;

  /** 刷脸ID */
  private String  faceId;

  /** 证件号码 */
  private String  idNo;

  /** 姓名 */
  private String  name;

  /** 刷脸方式 */
  private String  faceMode;

  /** 错误信息：目前只存错误码 */
  private String  errMsg;

  /**
   * 是否已经切换过无源比对
   * 0-未切换，1-已切换
   */
  private Integer isSwitched;

  /** 预留字段 */
  private String  extendedInfo;

  /** 记录创建时间 */
  private Date    createTime;

  /** 记录修改时间 */
  private Date    updateTime;

}
