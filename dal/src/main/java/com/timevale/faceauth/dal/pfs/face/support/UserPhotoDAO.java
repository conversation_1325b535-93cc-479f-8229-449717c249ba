package com.timevale.faceauth.dal.pfs.face.support;

import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/28 12
 */
@Repository
public interface UserPhotoDAO {

    @InsertProvider(type = UserPhotoDAOSqlProvider.class, method = "insert")
    @SelectKey(
            statement = "select last_insert_id()",
            keyProperty = "e.id",
            before = false,
            resultType = long.class)
    int insert(@Param("e") UserPhotoDO e);

    @SelectProvider(type = UserPhotoDAOSqlProvider.class, method = "getLatestPhoto")
    UserPhotoDO getLatestPhoto(@Param("idNo") String idNo);

    @SelectProvider(type = UserPhotoDAOSqlProvider.class, method = "getLatestPhotoByType")
    UserPhotoDO getLatestPhotoByType(@Param("idNo") String idNo, @Param("type") String photoType);

    @SelectProvider(type = UserPhotoDAOSqlProvider.class, method = "getLatestPhotoByFactor2")
    UserPhotoDO getLatestPhotoByFactor2(@Param("hashFactor2") String hashFactor2);

    @SelectProvider(type = UserPhotoDAOSqlProvider.class, method = "getLatestPhotoByFactor2")
    UserPhotoDO getLatestPhotoByFactor2AndType(@Param("hashFactor2") String hashFactor2, @Param("type") String photoType);

    @Delete("delete  from tb_user_photo where id_no = #{idNo} ")
    int delUserPhoto(@Param("idNo") String idNo);

}
