package com.timevale.faceauth.dal.pfs.face.support;

import com.timevale.faceauth.dal.pfs.support.SqlFactory;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @copyright 2019
 *     <p>刷脸请求 sql 语句提供器å
 * @date 2019/9/20 16
 */
public class FaceDAOSqlProvider {

  public String insert(@Param("e") FaceDO e) {
    return SqlFactory.sqlInsert("insert", FaceDO.class, "e");
  }

  public String getByFaceId(@Param("faceId") String faceId) {
    return SqlFactory.sqlSelect(
        "getByFaceId",
        FaceDO.class,
        () ->
            new SqlFactory.WhereCondition[] {
              SqlFactory.column("face_id").equals(SqlFactory.parameterName("faceId", null))
            });
  }

  public String getByBizId(@Param("bizCode") String bizCode, @Param("bizId") String bizId) {
    return SqlFactory.sqlSelect(
        "getByBizId",
        FaceDO.class,
        () ->
            new SqlFactory.WhereCondition[] {
              SqlFactory.column("biz_code").equals(SqlFactory.parameterName("bizCode", null)),
              SqlFactory.column("biz_id").equals(SqlFactory.parameterName("bizId", null))
            });
  }

  public String completedFace(FaceCompletionDO entity) {
    return SqlFactory.sqlUpdate("completedFace", FaceCompletionDO.class);
  }

  public String errorResultFace(FaceErrorResultDO entity) {
    return SqlFactory.sqlUpdate("errorResultFace", FaceErrorResultDO.class);
  }


  public String getFaceCallback(@Param("faceId") String faceId) {
    return SqlFactory.sqlSelect(
        "getFaceCallback",
        FaceCallbackDO.class,
        () ->
            new SqlFactory.WhereCondition[] {
              SqlFactory.column("face_id").equals(SqlFactory.parameterName("faceId", null))
            });
  }

  public String initializeFaceCallback(FaceCallbackInitializationDO entity) {
    return SqlFactory.sqlUpdate("initializeFaceCallback", FaceCallbackInitializationDO.class);
  }

  public String completedFaceCallback(FaceCallbackCompletionDO entity) {
    return SqlFactory.sqlUpdate("completedFaceCallback", FaceCallbackCompletionDO.class);
  }

  public String getFaceReturn(@Param("faceId") String faceId) {
    return SqlFactory.sqlSelect(
        "getFaceReturn",
        FaceReturnDO.class,
        () ->
            new SqlFactory.WhereCondition[] {
              SqlFactory.column("face_id").equals(SqlFactory.parameterName("faceId", null))
            });
  }

  public String initializeFaceReturn(FaceReturnInitializationDO entity) {
    return SqlFactory.sqlUpdate("initializeFaceReturn", FaceReturnInitializationDO.class);
  }

  public String failedFaceReturn(FaceReturnFailDO entity) {
    return SqlFactory.sqlUpdate("failedFaceReturn", FaceReturnFailDO.class);
  }

  public String faceApiVersion(FaceApiVersionDO entity) {
    return SqlFactory.sqlUpdate("faceApiVersion", FaceApiVersionDO.class);
  }

  public String clearFacePhoto(FacePhotoDO entity) {
    return SqlFactory.sqlUpdate("clearFacePhoto", FacePhotoDO.class);
  }

}
