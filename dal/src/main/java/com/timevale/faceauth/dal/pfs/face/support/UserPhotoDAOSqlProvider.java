package com.timevale.faceauth.dal.pfs.face.support;

import com.timevale.faceauth.dal.pfs.support.SqlFactory;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/28 11
 */
public class UserPhotoDAOSqlProvider {

  public String insert(@Param("e") UserPhotoDO e) {
    return SqlFactory.sqlInsert("insert", UserPhotoDO.class, "e");
  }

  public String getLatestPhoto(@Param("idNo") String idNo) {
    return SqlFactory.sqlSelectOne(
        "getLatestPhoto",
        UserPhotoDO.class,
        () ->
            (new SqlFactory.WhereCondition[] {
              SqlFactory.column("id_no").equals(SqlFactory.parameterName("idNo", null))
            }),
        SqlFactory.Sorted.desc("id"));
  }

  public String getLatestPhotoByType(@Param("idNo") String idNo, @Param("type") String photoType) {
    return SqlFactory.sqlSelectOne(
        "getLatestPhoto",
        UserPhotoDO.class,
        () ->
            (new SqlFactory.WhereCondition[] {
              SqlFactory.column("id_no").equals(SqlFactory.parameterName("idNo", null)),
              SqlFactory.column("photo_type").equals(SqlFactory.parameterName("type", null))
            }),
        SqlFactory.Sorted.desc("id"));
  }

  public String getLatestPhotoByFactor2(@Param("hashFactor2") String hashFactor2) {
    return SqlFactory.sqlSelectOne(
        "getLatestPhotoByFactor2",
        UserPhotoDO.class,
        () ->
            (new SqlFactory.WhereCondition[] {
              SqlFactory.column("hash_factor2").equals(SqlFactory.parameterName("hashFactor2", null))
            }),
        SqlFactory.Sorted.desc("id"));
  }

  public String getLatestPhotoByFactor2AndType(@Param("hashFactor2") String hashFactor2, @Param("type") String photoType) {
    return SqlFactory.sqlSelectOne(
        "getLatestPhotoByFactor2AndType",
        UserPhotoDO.class,
        () ->
            (new SqlFactory.WhereCondition[] {
              SqlFactory.column("hash_factor2").equals(SqlFactory.parameterName("hashFactor2", null)),
              SqlFactory.column("photo_type").equals(SqlFactory.parameterName("type", null))
            }),
        SqlFactory.Sorted.desc("id"));
  }
}
