package com.timevale.faceauth.dal.pfs.laudiovideodual;

import com.timevale.faceauth.dal.pfs.laudiovideodual.support.AudioAndVideoDualFileEntitySqlProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectKey;

import java.util.List;

/**
 * 活体认证实体仓库
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/2/20
 */
public interface AudioAndVideoDuaFilelRepository {

  @InsertProvider(type = AudioAndVideoDualFileEntitySqlProvider.class, method = "insert")
  @SelectKey(
      statement = "select last_insert_id()",
      keyProperty = "e.id",
      before = false,
      resultType = long.class)
  int insert(@Param("e") AudioAndVideoDualFileEntity e);

  @Select("select * from tb_audiovideodual_file  where certify_id = #{certifyId} order by id desc limit 10")
  List<AudioAndVideoDualFileEntity> getByCertifyId(@Param("certifyId") String certifyId);

  @Select("select * from tb_audiovideodual_file where type = #{type} and  certify_id = #{certifyId} order by id desc limit 10")
  List<AudioAndVideoDualFileEntity> getByCertifyIdAndType(@Param("type") String type, @Param("certifyId") String certifyId);


}
