package com.timevale.faceauth.dal.pfs.provider;

import com.timevale.faceauth.dal.pfs.dataobject.FaceauthAliTencentDo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.jdbc.SQL;

/** <AUTHOR> MASTER */
public class FaceauthAliTencentProvider {

  private static final String TB_NAME = "faceauth_ali_tencent";

  /**
   * 插入
   *
   * @param entity
   * @return
   */
  public String insert(FaceauthAliTencentDo entity) {
    return new SQL() {
      {
        INSERT_INTO(TB_NAME);

        VALUES("id", "#{id}");
        VALUES("user_id", "#{userId}");
        VALUES("verify_token", "#{verifyToken}");
        VALUES("id_no", "#{idNo}");
        VALUES("name", "#{name}");
        VALUES("url", "#{url}");
        VALUES("redirect_url", "#{redirectUrl}");
        VALUES("status", "#{status}");
        VALUES("biz_type", "#{bizType}");
      }
    }.toString();
  }

  /**
   * 根据id查询
   *
   * @param id
   * @return
   */
  public String findById(@Param("id") String id) {
    return new SQL() {
      {
        SELECT("*");
        FROM(TB_NAME);
        WHERE("id=#{id}");
      }
    }.toString();
  }

  /**
   * @param entity
   * @return
   */
  public String update(FaceauthAliTencentDo entity) {
    return new SQL() {
      {
        UPDATE(TB_NAME);
        SET("status=#{status}");
        WHERE("id=#{id}");
      }
    }.toString();
  }
}
