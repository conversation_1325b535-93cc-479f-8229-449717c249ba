package com.timevale.faceauth.dal.pfs.face.support;

import com.timevale.faceauth.dal.pfs.support.AutoIncrementIdentityStandardDO;
import com.timevale.faceauth.dal.pfs.support.DbFieldInfo;
import com.timevale.faceauth.dal.pfs.support.DbTableName;

/**
 * <AUTHOR>
 * @copyright 2019
 *     <p>供应商刷脸返回数据实体模型
 * @date 2019/9/19 17
 */
@DbTableName("tb_provider_return")
public class ProviderReturnDO extends AutoIncrementIdentityStandardDO {

  // 刷脸id
  @DbFieldInfo(value = "face_id", isRequired = true)
  public String faceId;

  // 供应商
  @DbFieldInfo(value = "provider", isRequired = true)
  public String provider;

  // 供应商数据返回方式
  @DbFieldInfo(value = "return_type", isRequired = true)
  public String returnType;

  // 供应商返回数据正文
  @DbFieldInfo("data")
  public String data;

  @DbFieldInfo(value = "is_ok", isRequired = true)
  public byte isOk = (byte) 0x00;

  @DbFieldInfo("err_msg")
  public String errMsg;

  @DbFieldInfo("err_code")
  public String errCode;
}
