package com.timevale.faceauth.dal.pfs.dataobject.query;

import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.query.QueryBase;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2024/8/6 17:05
 */
@Data
public class SdkCustomerDOQuery extends QueryBase {


    private List<String> orgGidList;

    private List<String> sdkVersionList;
    private List<String> channelCodeList;

    private String channelStatus;

    /**
     * 是否需要渠道sql
     *
     * @return
     */
    public Boolean getNeedChannelSql() {
        return CollectionUtils.isNotEmpty(channelCodeList) || StringUtils.isNotBlank(channelStatus);
    }

    /**
     * 是否需要sdk sql
     *
     * @return
     */
    public Boolean getNeedSdkSql() {
        return CollectionUtils.isNotEmpty(sdkVersionList);
    }

}
