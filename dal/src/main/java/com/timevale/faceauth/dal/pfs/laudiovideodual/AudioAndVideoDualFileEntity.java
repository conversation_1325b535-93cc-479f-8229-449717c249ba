package com.timevale.faceauth.dal.pfs.laudiovideodual;

import com.timevale.faceauth.dal.pfs.support.AutoIncrementIdentityStandardDO;
import com.timevale.faceauth.dal.pfs.support.DbFieldInfo;
import com.timevale.faceauth.dal.pfs.support.DbTableName;

/**
 * 智能视频认证文件实体
 *
 * <AUTHOR>
 */
@DbTableName("tb_audiovideodual_file")
public class AudioAndVideoDualFileEntity extends AutoIncrementIdentityStandardDO {


  /** 认证 ID */
  @DbFieldInfo(value = "certify_id", isRequired = true)
  public String certifyId;

  /** 文件类型：video , image */
  @DbFieldInfo(value = "status")
  public Integer status;

  /** 文件类型：video , image */
  @DbFieldInfo(value = "type", isRequired = true)
  public String type;

  /** 文件存储格式 byte base64 */
  @DbFieldInfo(value = "encryption", isRequired = true)
  public String encryption;

  /** 视频，或者存储视频的位置键值（key） */
  @DbFieldInfo(value = "file_id")
  public String fileId;

  /** base64格式文件id */
  @DbFieldInfo(value = "file_base64_id")
  public String fileBase64Id;

  /** 视频位置，或者视频存储方式 */
  @DbFieldInfo(value = "file_location", isRequired = true)
  public String fileLocation;


}
