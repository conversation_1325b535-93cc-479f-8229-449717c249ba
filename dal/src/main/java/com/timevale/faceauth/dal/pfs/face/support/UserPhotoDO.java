package com.timevale.faceauth.dal.pfs.face.support;

import com.timevale.faceauth.dal.pfs.support.AutoIncrementIdentityStandardDO;
import com.timevale.faceauth.dal.pfs.support.DbFieldInfo;
import com.timevale.faceauth.dal.pfs.support.DbTableName;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/28 11
 */
@DbTableName("tb_user_photo")
public class UserPhotoDO extends AutoIncrementIdentityStandardDO {

    @DbFieldInfo(value = "id_no", isRequired = true)
    public String idNo;

    @DbFieldInfo(value = "name", isRequired = true)
    public String name;

    @DbFieldInfo(value = "hash_factor2", isRequired = true)
    public String hashFactor2;

    @DbFieldInfo(value = "photo", isRequired = true)
    public String photo;

    @DbFieldInfo(value = "photo_type", isRequired = true)
    public String photoType;
}
