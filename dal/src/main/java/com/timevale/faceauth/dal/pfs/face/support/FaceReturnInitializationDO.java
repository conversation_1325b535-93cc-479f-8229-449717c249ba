package com.timevale.faceauth.dal.pfs.face.support;

import com.timevale.faceauth.dal.pfs.support.DbFieldInfo;
import com.timevale.faceauth.dal.pfs.support.DbTableName;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/10 15
 */
@DbTableName("tb_face")
public class FaceReturnInitializationDO extends FaceIdDO {

  // 当前刷脸完成时回跳时间
  @DbFieldInfo(value = "return_time", isRequired = true)
  public long returnTime = 0L;
}
