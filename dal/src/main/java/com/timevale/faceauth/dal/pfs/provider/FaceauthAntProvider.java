package com.timevale.faceauth.dal.pfs.provider;

import com.timevale.faceauth.dal.pfs.dataobject.FaceauthAntDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.jdbc.SQL;

/** <AUTHOR> MASTER */
public class FaceauthAntProvider {

  private static final String TB_NAME = "faceauth_ant";

  /**
   * 插入
   *
   * @param entity
   * @return
   */
  public String insert(FaceauthAntDO entity) {
    return new SQL() {
      {
        INSERT_INTO(TB_NAME);

        VALUES("id", "#{id}");
        VALUES("certify_id", "#{certifyId}");
        VALUES("provider_api_version", "#{providerApiVersion}");
        VALUES("id_no", "#{idNo}");
        VALUES("name", "#{name}");
        VALUES("photo_input", "#{photoInput}");
        VALUES("photo_type", "#{photoType}");
        VALUES("user_id", "#{userId}");
        VALUES("callback_url", "#{callbackUrl}");
        VALUES("success", "#{success}");
        VALUES("err_message", "#{errMessage}");
        VALUES("code", "#{code}");
        VALUES("create_time", "now()");
        VALUES("update_time", "now()");
      }
    }.toString();
  }

  /**
   * 根据id查询
   *
   * @param id
   * @return
   */
  public String findById(@Param("id") String id) {
    return new SQL() {
      {
        SELECT("*");
        FROM(TB_NAME);
        WHERE("id=#{id}");
      }
    }.toString();
  }

  /**
   * @param entity
   * @return
   */
  public String update(FaceauthAntDO entity) {
    return new SQL() {
      {
        UPDATE(TB_NAME);

        SET("err_message=#{errMessage}");
        SET("status=#{status}");
        SET("update_time=now()");

        WHERE("id=#{id}");
      }
    }.toString();
  }
}
