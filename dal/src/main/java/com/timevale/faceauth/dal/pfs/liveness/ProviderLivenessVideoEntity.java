package com.timevale.faceauth.dal.pfs.liveness;

import com.timevale.faceauth.dal.pfs.support.AutoIncrementIdentityStandardDO;
import com.timevale.faceauth.dal.pfs.support.DbFieldInfo;
import com.timevale.faceauth.dal.pfs.support.DbTableName;

/**
 * 供应商活体认证视频实体对象
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/2/20
 */
@DbTableName("tb_liveness_provider_video")
public class ProviderLivenessVideoEntity extends AutoIncrementIdentityStandardDO {

  /** 活体认证 ID */
  @DbFieldInfo(value = "liveness_id", isRequired = true)
  public String livenessId;

  /** 供应商代码 */
  @DbFieldInfo(value = "provider_code", isRequired = true)
  public String providerCode;

  /** 描述当前视频的活体行为方式 */
  @DbFieldInfo(value = "actions", isRequired = true)
  public String actions;

  /** 视频，或者存储视频的位置键值（key） */
  @DbFieldInfo(value = "video", isRequired = true)
  public String video;

  /** 视频位置，或者视频存储方式 */
  @DbFieldInfo(value = "video_location", isRequired = true)
  public String videoLocation;
}
