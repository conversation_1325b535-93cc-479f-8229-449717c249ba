package com.timevale.faceauth.dal.pfs.dataobject.query;

import com.timevale.kmssdk.encryption.Encryption;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2025/7/1 17:29
 */

/**
 * 人脸用户资源控制记录查询参数类
 * <p>
 * 用于封装分页查询人脸用户资源控制记录所需的条件参数，
 * 包含时间范围、应用 ID 列表、名称、证件号、证件类型等信息。
 */
@Data
public class FaceUserResourceControlRecordQuery extends ToString {

    /**
     * 查询开始时间（创建时间大于等于该值）
     */
    private Date startTime;

    /**
     * 查询结束时间（创建时间小于等于该值）
     */
    private Date endTime;

    /**
     * 应用 ID 列表，用于筛选属于特定应用的记录
     */
    private List<String> appIds;

    /**
     * 用户名称，用于精确匹配查询
     */
    private String name;

    /**
     * 用户证件号（如身份证号），需加密传输或存储
     */
    @Encryption
    private String idNo;

    /**
     * 证件类型，如身份证、护照等
     */
    private String idType;

    /**
     * 当前页码，用于分页查询
     */
    private Integer pageNum;

    /**
     * 每页记录数，用于分页查询
     */
    private Integer pageSize;


    /**
     * 当前状态，如：
     * @see com.timevale.faceauth.service.domain.controll.FaceResourceControlStatusEnum
     */
    private String status;
}

