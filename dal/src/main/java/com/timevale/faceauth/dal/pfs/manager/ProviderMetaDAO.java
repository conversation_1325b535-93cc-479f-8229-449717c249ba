package com.timevale.faceauth.dal.pfs.manager;

import com.timevale.faceauth.dal.pfs.dataobject.ProviderMetaDO;
import org.apache.ibatis.annotations.Select;

/** 供应商元数据 */
public interface ProviderMetaDAO {

  @Select({
    "<script>",
    "SELECT `id`,`provider_id`,`provider_key`,`provider_name`,`space_id`,`create_time`,`modify_time` FROM `provider_meta`",
    " WHERE `provider_id`=#{providerId} LIMIT 1",
    "</script>",
  })
  ProviderMetaDO getByProviderId(String providerId);
}
