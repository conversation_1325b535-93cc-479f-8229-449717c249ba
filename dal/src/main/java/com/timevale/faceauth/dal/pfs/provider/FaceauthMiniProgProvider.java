package com.timevale.faceauth.dal.pfs.provider;

import com.timevale.faceauth.dal.pfs.dataobject.FaceauthMiniProgDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.jdbc.SQL;

/** <AUTHOR> MASTER */
public class FaceauthMiniProgProvider {

  private static final String TB_NAME = "faceauth_mini_prog";

  /**
   * 插入
   *
   * @param entity
   * @return
   */
  public String insert(FaceauthMiniProgDO entity) {
    return new SQL() {
      {
        INSERT_INTO(TB_NAME);

        VALUES("id", "#{id}");
        VALUES("id_no", "#{idNo}");
        VALUES("name", "#{name}");
        VALUES("photo_input", "#{photoInput}");
        VALUES("photo_type", "#{photoType}");
        VALUES("user_id", "#{userId}");
        VALUES("status", "#{status}");
      }
    }.toString();
  }

  /**
   * 根据id查询
   *
   * @param id
   * @return
   */
  public String findById(@Param("id") String id) {
    return new SQL() {
      {
        SELECT("*");
        FROM(TB_NAME);
        WHERE("id=#{id}");
      }
    }.toString();
  }

  /**
   * @param entity
   * @return
   */
  public String update(FaceauthMiniProgDO entity) {
    return new SQL() {
      {
        UPDATE(TB_NAME);

        SET("status=#{status}");

        WHERE("id=#{id}");
      }
    }.toString();
  }
}
