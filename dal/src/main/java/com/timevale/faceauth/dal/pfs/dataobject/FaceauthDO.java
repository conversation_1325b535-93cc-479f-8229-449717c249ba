package com.timevale.faceauth.dal.pfs.dataobject;

import java.util.Date;

/**
 * The table. faceauth
 *
 * <AUTHOR> Kunpeng
 */
public class FaceauthDO {

  /** createTime 创建时间. */
  private Date createTime;
  /** updateTime 更新时间. */
  private Date updateTime;
  /** id ID. */
  private String id;
  /** type 认证类型:1-腾讯云人脸识别 2-芝麻. */
  private Integer type;
  /** authStatus 认证状态，0-未完成 1-认证完成 */
  private Integer authStatus;

  /** authMsg 认证结果描述 */
  private String authMsg;

  /** bizType 业务类型:1-实名认证 2-意愿认证 */
  private Integer bizType;

  private String bizAppId;

  /**
   * Get createTime 创建时间.
   *
   * @return the string
   */
  public Date getCreateTime() {
    return createTime;
  }

  /** Set createTime 创建时间. */
  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  /**
   * Get updateTime 更新时间.
   *
   * @return the string
   */
  public Date getUpdateTime() {
    return updateTime;
  }

  /** Set updateTime 更新时间. */
  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  /**
   * Get id ID.
   *
   * @return the string
   */
  public String getId() {
    return id;
  }

  /** Set id ID. */
  public void setId(String id) {
    this.id = id;
  }

  /**
   * Get type 认证类型:1-腾讯云人脸识别 2-芝麻.
   *
   * @return the string
   */
  public Integer getType() {
    return type;
  }

  /** Set type 认证类型:1-腾讯云人脸识别 2-芝麻. */
  public void setType(Integer type) {
    this.type = type;
  }

  /**
   * Get authStatus 认证状态，0-未完成 1-认证完成
   *
   * @return the string
   */
  public Integer getAuthStatus() {
    return authStatus;
  }

  /** Set authStatus 认证状态，0-未完成 1-认证完成 */
  public void setAuthStatus(Integer authStatus) {
    this.authStatus = authStatus;
  }

  public String getAuthMsg() {
    return authMsg;
  }

  public void setAuthMsg(String authMsg) {
    this.authMsg = authMsg;
  }

  public Integer getBizType() {
    return bizType;
  }

  public void setBizType(Integer bizType) {
    this.bizType = bizType;
  }

  public String getBizAppId() {
    return bizAppId;
  }

  public void setBizAppId(String bizAppId) {
    this.bizAppId = bizAppId;
  }

  @Override
  public String toString() {
    return "FaceauthDO{}";
  }
}
