package com.timevale.faceauth.dal.pfs.dataobject;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * The table. 供应商通知配置
 *
 * <AUTHOR> Kunpeng
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProviderNotifyConfigDO extends ToString {

  /** 自增主键. */
  private Long id;
  /** 通知配置ID. */
  private String configId;
  /** 一级空间ID. */
  private String firstSpaceId;
  /** 短信通知开关[0关闭,1开启]. */
  private Integer smsEnabled;
  /** 钉钉工作通知开关[0关闭,1开启]. */
  private Integer dingCropEnabled;
  /** 钉钉机器人通知开关[0关闭,1开启]. */
  private Integer dingRobotEnabled;
  /** 钉钉机器人token. */
  private String dingRobotToken;
  /** 推送用户. */
  private String notifyUsernames;
  /** createTime 记录创建时间. */
  private Date createTime;
  /** modifyTime 记录修改时间. */
  private Date modifyTime;
}
