package com.timevale.faceauth.dal.pfs.provider;

import com.timevale.faceauth.dal.pfs.dataobject.FaceauthDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.jdbc.SQL;

/**
 * <AUTHOR> on 2018/7/23
 *     <p>人脸识别主表provider
 */
public class FaceauthProvider {

  private static final String TB_NAME = "faceauth";

  /**
   * 插入
   *
   * @param entity
   * @return
   */
  public String insert(FaceauthDO entity) {
    return new SQL() {
      {
        INSERT_INTO(TB_NAME);

        VALUES("id", "#{id}");
        VALUES("type", "#{type}");
        VALUES("auth_status", "#{authStatus}");
        VALUES("auth_msg", "#{authMsg}");
        VALUES("biz_type", "#{bizType}");
        VALUES("biz_app_id", "#{bizAppId}");
        VALUES("create_time", "now()");
        VALUES("update_time", "now()");
      }
    }.toString();
  }

  /**
   * 根据id查询
   *
   * @param id
   * @return
   */
  public String findById(@Param("id") String id) {
    return new SQL() {
      {
        SELECT("*");
        FROM(TB_NAME);
        WHERE("id=#{id}");
      }
    }.toString();
  }

  /**
   * 更新
   *
   * @return
   */
  public String update(FaceauthDO entity) {
    return new SQL() {
      {
        UPDATE(TB_NAME);
        SET("auth_msg=#{authMsg}");
        SET("auth_status=#{authStatus}");

        SET("update_time=now()");

        WHERE("id=#{id}");
      }
    }.toString();
  }
}
