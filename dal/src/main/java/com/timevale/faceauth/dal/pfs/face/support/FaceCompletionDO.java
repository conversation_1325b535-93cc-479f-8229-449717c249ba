package com.timevale.faceauth.dal.pfs.face.support;

import com.timevale.faceauth.dal.pfs.support.DbFieldInfo;
import com.timevale.faceauth.dal.pfs.support.DbTableName;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/10 15
 */
@DbTableName("tb_face")
public class FaceCompletionDO extends FaceIdDO {

  // 当前刷脸成功状态。（已成功：1）
  @DbFieldInfo(value = "is_ok", isRequired = true)
  public byte isOk = 0x00;
}
