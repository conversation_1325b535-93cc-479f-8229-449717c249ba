package com.timevale.faceauth.dal.pfs.liveness;

import com.timevale.faceauth.dal.pfs.support.AutoIncrementIdentityStandardDO;
import com.timevale.faceauth.dal.pfs.support.DbFieldInfo;
import com.timevale.faceauth.dal.pfs.support.DbTableName;

/**
 * 供应商活体认证实体
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/2/20
 */
@DbTableName("tb_liveness_provider_order")
public class ProviderLivenessEntity extends AutoIncrementIdentityStandardDO {

  /** 活体认证 ID */
  @DbFieldInfo(value = "liveness_id", isRequired = true)
  public String livenessId;

  /** 供应商代码 */
  @DbFieldInfo(value = "provider_code", isRequired = true)
  public String providerCode;

  /** 供应商名称 */
  @DbFieldInfo(value = "provider_name", isRequired = true)
  public String providerName;

  /** 订单号 */
  @DbFieldInfo(value = "order_no")
  public String orderNo;

  /** 描述一组活体行为 */
  @DbFieldInfo("actions")
  public String actions;

  /** 过期状态：过期（1）；未过期（0） */
  @DbFieldInfo("expired")
  public byte expired = 0x00;

  /** 过期时间 */
  @DbFieldInfo("expired_time")
  public long expiredTime = 0L;

  /** 完成状态：完成（1）；未完成（0） */
  @DbFieldInfo("completed")
  public byte completed = 0x00;

  /** 完成时间 */
  @DbFieldInfo("completed_time")
  public long completedTime = 0L;

  /** 源照片或者存储源照片的键值（key） */
  @DbFieldInfo("photo_src")
  public String photoSrc = "";

  /** 认证完成照片或者存储认证完成照片的键值（key） */
  @DbFieldInfo("photo")
  public String photo = "";

  /** 照片位置，或者照片存储的方式 */
  @DbFieldInfo(value = "photo_location", isRequired = true)
  public String photoLocation;

  /** 照片类型 */
  @DbFieldInfo(value = "photo_type", isRequired = true)
  public String photoType;

  /** 活体率分值 */
  @DbFieldInfo("liveness_rate")
  public float livenessRate = -1F;

  /** 相似度分值 */
  @DbFieldInfo("sim")
  public float sim = -1F;

  /** 收费状态：收费（1）；不收费（0） */
  @DbFieldInfo("fee")
  public byte fee = 0x00;

  /** 收费方式 */
  @DbFieldInfo(value = "fee_type", isRequired = true)
  public String feeType;

  /** 消息 */
  @DbFieldInfo("msg")
  public String message;
}
