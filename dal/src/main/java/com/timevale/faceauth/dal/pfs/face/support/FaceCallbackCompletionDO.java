package com.timevale.faceauth.dal.pfs.face.support;

import com.timevale.faceauth.dal.pfs.support.DbFieldInfo;
import com.timevale.faceauth.dal.pfs.support.DbTableName;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/10 15
 */
@DbTableName("tb_face")
public class FaceCallbackCompletionDO extends FaceIdDO {

  // 当前刷脸完成时上游业务回调完成状态。（已完成：1）
  @DbFieldInfo(value = "callback_is_done", isRequired = true)
  public byte callbackIsDone = 0x00;

  // 当前刷脸完成时回调上游业务完成时间
  @DbFieldInfo(value = "callback_done_time", isRequired = true)
  public long callbackDoneTime = 0L;

  // 当前刷脸完成时回调上游业务成功状态。（成功：1）
  @DbFieldInfo(value = "callback_is_ok", isRequired = true)
  public byte callbackIsOk = 0x00;

  // 当前刷脸完成时回调上游业务失败原因
  @DbFieldInfo("callback_err_msg")
  public String callbackErrMsg;
}
