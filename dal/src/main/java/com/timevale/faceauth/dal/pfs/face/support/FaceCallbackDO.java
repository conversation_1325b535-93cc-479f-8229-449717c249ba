package com.timevale.faceauth.dal.pfs.face.support;

import com.timevale.faceauth.dal.pfs.support.DbFieldInfo;
import com.timevale.faceauth.dal.pfs.support.DbTableName;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/10 16
 */
@DbTableName("tb_face")
public class FaceCallbackDO extends FaceIdDO {

  // 上游业务Id
  @DbFieldInfo(value = "biz_id", isRequired = true)
  public String bizId;

  // 业务编码
  @DbFieldInfo(value = "biz_code", isRequired = true)
  public String bizCode;

  // 当前刷脸上游回调地址
  @DbFieldInfo("callback_url")
  public String callbackUrl;

  // 当前刷脸完成时回调上游业务开始时间
  @DbFieldInfo(value = "callback_time", isRequired = true)
  public long callbackTime = 0L;

  // 当前刷脸完成时上游业务回调完成状态。（已完成：1）
  @DbFieldInfo(value = "callback_is_done", isRequired = true)
  public byte callbackIsDone = 0x00;

  // 当前刷脸完成时回调上游业务完成时间
  @DbFieldInfo(value = "callback_done_time", isRequired = true)
  public long callbackDoneTime = 0L;

  // 当前刷脸完成时回调上游业务成功状态。（成功：1）
  @DbFieldInfo(value = "callback_is_ok", isRequired = true)
  public byte callbackIsOk = 0x00;
}
