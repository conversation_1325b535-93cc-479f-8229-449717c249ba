package com.timevale.faceauth.dal.pfs.support;

import org.springframework.util.StringUtils;

import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @copyright 2019
 *     <p>数据字段模型
 * @date 2019/9/20 13
 */
public class DbField {

  private final String dbName;
  private final String dbType;
  private final String name;
  private final Class<?> type;
  private final boolean primary;
  private final boolean required;
  private final boolean autoIncrement;
  private final boolean ignoreUpdate;

  private DbField(
      String dbName,
      String dbType,
      String name,
      Class<?> type,
      boolean primary,
      boolean required,
      boolean autoIncrement,
      boolean ignoreUpdate) {
    this.dbName = dbName;
    this.dbType = dbType;
    this.name = name;
    this.type = type;
    this.primary = primary;
    this.required = required;
    this.autoIncrement = autoIncrement;
    this.ignoreUpdate = ignoreUpdate;
  }

  public String getDbName() {
    return dbName;
  }

  public String getDbType() {
    return dbType;
  }

  public String getName() {
    return name;
  }

  public Class<?> getType() {
    return type;
  }

  public boolean isPrimary() {
    return primary;
  }

  public boolean isRequired() {
    return required;
  }

  public boolean isAutoIncrement() {
    return autoIncrement;
  }

  public boolean isIgnoreUpdate() {
    return ignoreUpdate;
  }

  static DbField valueOf(Field field) {
    DbFieldInfo fieldInfo = field.getDeclaredAnnotation(DbFieldInfo.class);
    final String name = field.getName();
    if (null == fieldInfo) {
      return (new DbField(name, null, name, field.getType(), false, false, false, false));
    }

    // 字段 DbFieldInfo 注解存在，那么使用注解的内容
    String dbName = fieldInfo.value();
    boolean primary = fieldInfo.isPrimary();
    boolean required = fieldInfo.isRequired();
    return (new DbField(
        (StringUtils.hasLength(dbName) ? dbName : name),
        fieldInfo.dbType(),
        name,
        field.getType(),
        primary,
        primary || required,
        fieldInfo.isAutoIncrement(),
        fieldInfo.ignoreUpdate()));
  }
}
