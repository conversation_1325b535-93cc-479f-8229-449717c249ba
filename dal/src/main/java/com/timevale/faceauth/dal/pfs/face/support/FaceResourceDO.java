package com.timevale.faceauth.dal.pfs.face.support;

import com.timevale.faceauth.dal.pfs.support.AutoIncrementIdentityStandardDO;
import com.timevale.faceauth.dal.pfs.support.DbFieldInfo;
import com.timevale.faceauth.dal.pfs.support.DbTableName;

/**
 * 刷脸资源
 *
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/28 11
 */
@DbTableName("tb_face_resources")
public class FaceResourceDO extends AutoIncrementIdentityStandardDO {

    @DbFieldInfo(value = "face_id", isRequired = true)
    public String faceId;

    @DbFieldInfo(value = "content", isRequired = true)
    public String content;

    @DbFieldInfo(value = "resource_type", isRequired = true)
    public String resourceType;

    @DbFieldInfo(value = "save_type", isRequired = true)
    public String saveType;
}
