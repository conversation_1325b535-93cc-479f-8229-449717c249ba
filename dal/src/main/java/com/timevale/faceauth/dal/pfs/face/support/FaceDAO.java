package com.timevale.faceauth.dal.pfs.face.support;

import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @copyright 2019
 *     <p>刷脸请求数据实体模型仓库
 * @date 2019/9/19 17
 */
@Repository
public interface FaceDAO {

  /**
   * 持久化指定数据到 db，并返回自增的 id 值
   *
   * <AUTHOR>
   * @date 2019/9/20
   */
  @InsertProvider(type = FaceDAOSqlProvider.class, method = "insert")
  @SelectKey(
      statement = "select last_insert_id()",
      keyProperty = "e.id",
      before = false,
      resultType = long.class)
  int insert(@Param("e") FaceDO e);

  /**
   * 获取指定 face_id 的刷脸数据模型
   *
   * <AUTHOR>
   * @date 2019/9/20
   */
  @SelectProvider(type = FaceDAOSqlProvider.class, method = "getByFaceId")
  FaceDO getByFaceId(@Param("faceId") String faceId);

  /**
   * 获取指定业务 id 的刷脸数据模型
   *
   * <AUTHOR>
   * @date 2019/9/20
   */
  @SelectProvider(type = FaceDAOSqlProvider.class, method = "getByBizId")
  FaceDO getByBizId(@Param("bizCode") String bizCode, @Param("bizId") String bizId);

  @UpdateProvider(type = FaceDAOSqlProvider.class, method = "completedFace")
  int completedFace(FaceCompletionDO entity);

  @UpdateProvider(type = FaceDAOSqlProvider.class, method = "errorResultFace")
  int errorResultFace(FaceErrorResultDO entity);

  @SelectProvider(type = FaceDAOSqlProvider.class, method = "getFaceCallback")
  FaceCallbackDO getFaceCallback(@Param("faceId") String faceId);

  @UpdateProvider(type = FaceDAOSqlProvider.class, method = "initializeFaceCallback")
  int initializeFaceCallback(FaceCallbackInitializationDO entity);

  @UpdateProvider(type = FaceDAOSqlProvider.class, method = "completedFaceCallback")
  int completedFaceCallback(FaceCallbackCompletionDO entity);

  @SelectProvider(type = FaceDAOSqlProvider.class, method = "getFaceReturn")
  FaceReturnDO getFaceReturn(@Param("faceId") String faceId);

  @UpdateProvider(type = FaceDAOSqlProvider.class, method = "initializeFaceReturn")
  int initializeFaceReturn(FaceReturnInitializationDO entity);

  @UpdateProvider(type = FaceDAOSqlProvider.class, method = "failedFaceReturn")
  int failedFaceReturn(FaceReturnFailDO entity);


  @UpdateProvider(type = FaceDAOSqlProvider.class, method = "faceApiVersion")
  int faceApiVersion(FaceApiVersionDO entity);

  @UpdateProvider(type = FaceDAOSqlProvider.class, method = "clearFacePhoto")
  int clearFacePhoto(FacePhotoDO entity);
}
