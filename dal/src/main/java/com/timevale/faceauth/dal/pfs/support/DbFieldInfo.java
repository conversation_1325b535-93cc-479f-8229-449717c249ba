package com.timevale.faceauth.dal.pfs.support;

import java.lang.annotation.*;

/**
 * 数据库字段信息
 *
 * <AUTHOR>
 * @date 2019/9/19
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DbFieldInfo {

  String value();

  String dbType() default "";

  boolean isRequired() default false;

  boolean isPrimary() default false;

  boolean isAutoIncrement() default false;

  boolean ignoreUpdate() default false;
}
