package com.timevale.faceauth.dal.pfs.manager;

import com.timevale.faceauth.dal.pfs.dataobject.NameSpaceMetaDO;
import org.apache.ibatis.annotations.Select;

/** 供应商管理系统空间元数据 */
public interface NameSpaceMetaDAO {

  @Select({
    "<script>",
    "SELECT `id`,`space_id`,`space_name`,`level`,`parent_id`,`create_time`,`modify_time` FROM `name_space_meta`",
    " WHERE `space_id`=#{spaceId} LIMIT 1",
    "</script>",
  })
  NameSpaceMetaDO getBySpaceId(String spaceId);
}
