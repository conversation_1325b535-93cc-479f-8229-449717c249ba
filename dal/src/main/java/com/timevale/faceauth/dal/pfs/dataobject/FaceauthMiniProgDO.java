package com.timevale.faceauth.dal.pfs.dataobject;

import java.util.Date;

public class FaceauthMiniProgDO {
  private String id;

  private String idNo;

  private String name;

  private String photoInput;

  private Integer photoType;

  private String userId;

  private Integer status;

  private String code;

  private Date createTime;

  private Date updateTime;

  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id == null ? null : id.trim();
  }

  public String getIdNo() {
    return idNo;
  }

  public void setIdNo(String idNo) {
    this.idNo = idNo == null ? null : idNo.trim();
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name == null ? null : name.trim();
  }

  public Integer getPhotoType() {
    return photoType;
  }

  public void setPhotoType(Integer photoType) {
    this.photoType = photoType;
  }

  public String getUserId() {
    return userId;
  }

  public void setUserId(String userId) {
    this.userId = userId == null ? null : userId.trim();
  }

  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code == null ? null : code.trim();
  }

  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  public String getPhotoInput() {
    return photoInput;
  }

  public void setPhotoInput(String photoInput) {
    this.photoInput = photoInput;
  }
}
