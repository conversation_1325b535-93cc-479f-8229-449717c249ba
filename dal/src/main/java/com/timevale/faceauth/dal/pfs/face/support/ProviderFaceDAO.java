package com.timevale.faceauth.dal.pfs.face.support;

import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.UpdateProvider;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/10 17
 */
@Repository
public interface ProviderFaceDAO {

  @SelectKey(
      statement = "select last_insert_id()",
      keyProperty = "e.id",
      before = false,
      resultType = long.class)
  @InsertProvider(type = ProviderFaceDAOSqlProvider.class, method = "insert")
  int insert(@Param("e") ProviderFaceDO entity);

  @SelectProvider(type = ProviderFaceDAOSqlProvider.class, method = "getByFaceId")
  ProviderFaceDO getByFaceId(@Param("faceId") String faceId);

  @SelectProvider(type = ProviderFaceDAOSqlProvider.class, method = "getByProviderOrder")
  ProviderFaceDO getByProviderOrder(@Param("provider") String provider, @Param("order") String order);

  @UpdateProvider(type = ProviderFaceDAOSqlProvider.class, method = "completedProviderFace")
  int completedProviderFace(ProviderFaceCompletionDO entity);

  @Update("update tb_provider_face set thirdpart_id = #{thirdpartId} , face_input = #{faceInput}   where face_id = #{faceId}")
  void updateProviderFaceThirdpartId(@Param("thirdpartId") String thirdpartId, @Param("faceInput") String faceInput, @Param("faceId") String faceId);

  @Select("select * from  tb_provider_face  where order_no = #{order}  order by id desc limit 1")
  ProviderFaceDO selectByProviderOrder( @Param("order") String order);



  @Update("update tb_provider_face set order_no = #{orderNo} , face_data = #{faceData}  , face_input = #{faceInput}  where face_id = #{faceId}")
  int updateByFaceId(@Param("orderNo") String orderNo, @Param("faceData") String faceData, @Param("faceInput") String faceInput, @Param("faceId") String faceId);
}
