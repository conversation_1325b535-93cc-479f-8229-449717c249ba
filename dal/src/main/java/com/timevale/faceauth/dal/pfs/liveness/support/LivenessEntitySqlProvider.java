package com.timevale.faceauth.dal.pfs.liveness.support;

import com.timevale.faceauth.dal.pfs.liveness.LivenessCancelledEntity;
import com.timevale.faceauth.dal.pfs.liveness.LivenessCompletedEntity;
import com.timevale.faceauth.dal.pfs.liveness.LivenessEntity;
import com.timevale.faceauth.dal.pfs.liveness.LivenessExpiredEntity;
import com.timevale.faceauth.dal.pfs.support.SqlFactory;
import org.apache.ibatis.annotations.Param;

/**
 * 活体认证命令供应器
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/2/20
 */
public class LivenessEntitySqlProvider {

  public String insert(@Param("e") LivenessEntity e) {
    return SqlFactory.sqlInsert((String) "insert", LivenessEntity.class, (String) "e");
  }

  public String getByLivenessId(@Param("livenessId") String livenessId) {
    return SqlFactory.sqlSelect(
        (String) "getByLivenessId",
        LivenessEntity.class,
        () ->
            (new SqlFactory.WhereCondition[] {
              SqlFactory.column((String) "liveness_id")
                  .equals(SqlFactory.parameterName((String) "livenessId", (String) null))
            }));
  }

  public String getByBizId(@Param("bizCode") String bizCode, @Param("bizId") String bizId) {
    return SqlFactory.sqlSelect(
        (String) "getByBizId",
        LivenessEntity.class,
        () ->
            new SqlFactory.WhereCondition[] {
              SqlFactory.column((String) "biz_code")
                  .equals(SqlFactory.parameterName((String) "bizCode", (String) null)),
              SqlFactory.column((String) "biz_id")
                  .equals(SqlFactory.parameterName((String) "bizId", (String) null))
            });
  }

  public String completedLiveness(LivenessCompletedEntity entity) {
    return SqlFactory.sqlUpdate((String) "completedLiveness", LivenessCompletedEntity.class);
  }

  public String cancelledLiveness(LivenessCancelledEntity entity) {
    return SqlFactory.sqlUpdate((String) "cancelledLiveness", LivenessCancelledEntity.class);
  }

  public String expiredLiveness(LivenessExpiredEntity entity) {
    return SqlFactory.sqlUpdate((String) "expiredLiveness", LivenessExpiredEntity.class);
  }
}
