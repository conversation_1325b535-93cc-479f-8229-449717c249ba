package com.timevale.faceauth.dal.pfs.liveness;

import com.timevale.faceauth.dal.pfs.support.DbFieldInfo;
import com.timevale.faceauth.dal.pfs.support.DbTableName;

/**
 * 活体认证完成对象
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/2/20
 */
@DbTableName("tb_liveness")
public class LivenessCompletedEntity {

  /** 活体认证 ID */
  @DbFieldInfo(value = "liveness_id", isPrimary = true)
  public String livenessId;

  /** 完成状态：完成（1）；未完成（0） */
  @DbFieldInfo("completed")
  public byte completed = 0x01;

  @DbFieldInfo("result_msg")
  public String resultMsg;

  @DbFieldInfo("result_code")
  public String resultCode;

  /** 完成时间 */
  @DbFieldInfo("completed_time")
  public long completedTime = System.currentTimeMillis();
}
