package com.timevale.faceauth.dal.pfs.laudiovideodual.support;

import com.timevale.faceauth.dal.pfs.laudiovideodual.AudioAndVideoDualFileEntity;
import com.timevale.faceauth.dal.pfs.support.SqlFactory;
import org.apache.ibatis.annotations.Param;

/**
 * 智能视频认证命令供应器
 *
 * <AUTHOR>
 */
public class AudioAndVideoDualFileEntitySqlProvider {

  public String insert(@Param("e") AudioAndVideoDualFileEntity e) {
    return SqlFactory.sqlInsert((String) "insert", AudioAndVideoDualFileEntity.class, (String) "e");
  }





}
