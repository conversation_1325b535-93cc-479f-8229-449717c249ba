package com.timevale.faceauth.dal.pfs.dao;

import com.timevale.faceauth.dal.pfs.dataobject.FaceauthTencentH5DO;
import com.timevale.faceauth.dal.pfs.provider.FaceauthTencentH5Provider;
import com.timevale.faceauth.dal.pfs.face.support.FaceAuthQueryRequestDO;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;

import java.util.List;

/**
 * The Table faceauth_tencent_h5. faceauth_tencent_h5
 *
 * <AUTHOR> Kunpeng
 */
public interface FaceauthTencentH5DAO {

  /**
   * desc:插入表:faceauth_tencent_h5.<br>
   *
   * @param entity entity
   * @return int
   */
  @InsertProvider(type = FaceauthTencentH5Provider.class, method = "insert")
  int insert(FaceauthTencentH5DO entity);

  /**
   * desc:根据主键获取数据:faceauth_tencent_h5.<br>
   *
   * @param id id
   * @return FaceauthTencentH5DO
   */
  @SelectProvider(type = FaceauthTencentH5Provider.class, method = "findById")
  FaceauthTencentH5DO getById(String id);

  /**
   * 更新
   *
   * @param entity
   */
  @UpdateProvider(type = FaceauthTencentH5Provider.class, method = "update")
  void update(FaceauthTencentH5DO entity);

  /**
   * desc:专为快捷签出证查询使用
   *
   * @param faceAuthQueryRequestDO
   * @return List<InfoServiceBankthreeDO>
   */
  @Select({
          "<script>",
          "select * from faceauth_tencent_h5 ",
          "where  1=1 ",
          "<if test='idno != null'> and id_no = #{idno} </if>",
          "<if test='code != null'> and code = #{code} </if>",
          "<if test='modifyTime != null'> and update_time between 0 and #{modifyTime} </if>",
          "order by update_time desc limit #{currIndex},#{pageSize}",
          "</script>"})
  List<FaceauthTencentH5DO> queryByUpdateTimeAndIdNo(FaceAuthQueryRequestDO faceAuthQueryRequestDO);

  /**
   * 快捷签查询个人腾讯h5刷脸记录
   * @param authQueryRequestDO 请求参数
   * @return 个人腾讯H5刷脸记录
   */
  @Select({
          "<script>",
          "select * from faceauth_tencent_h5 th inner join faceauth fu on th.id = fu.id ",
          "where  1=1 ",
          "<if test='idno != null'> and th.id_no = #{idno} </if>",
          "<if test='code != null'> and th.code = #{code} </if>",
          "<if test='modifyTime != null'> and th.update_time between 0 and #{modifyTime} </if>",
          "order by th.update_time desc limit #{currIndex},#{pageSize}",
          "</script>"})
  List<FaceauthTencentH5DO> quickSignQueryListByTencentH5(FaceAuthQueryRequestDO authQueryRequestDO);
}
