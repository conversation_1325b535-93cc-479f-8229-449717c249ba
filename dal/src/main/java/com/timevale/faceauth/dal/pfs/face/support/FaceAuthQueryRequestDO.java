package com.timevale.faceauth.dal.pfs.face.support;

import com.timevale.faceauth.service.input.BaseInput;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class FaceAuthQueryRequestDO extends BaseInput {
  String tid;
  String idno;
  String faceId;
  String bizId;
  String appId;
  String isOk;
  String status;
  String code;
  String passed;
  String bizCode;
  String bizScene;
  Date updateTime;
  Timestamp modifyTime;
  Timestamp createTime;
  Integer currIndex, pageSize;
}
