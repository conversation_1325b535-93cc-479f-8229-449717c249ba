package com.timevale.faceauth.dal.pfs.dao;

import com.timevale.faceauth.dal.pfs.dataobject.SdkCustomerChannelMappingDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2024/8/6 14:46
 */
public interface SdkCustomerChannelMappingDAO {

    @Insert("INSERT INTO tb_sdk_customer_channel_mapping (mapping_id, sdk_customer_id, channel_code, channel_license, status, operator,bundle_id,harmony_os_license) "
            + "VALUES (#{mappingId}, #{sdkCustomerId}, #{channelCode}, #{channelLicense}, #{status}, #{operator}, #{bundleId},#{harmonyOsLicense})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(SdkCustomerChannelMappingDO mapping);




    @Update("UPDATE tb_sdk_customer_channel_mapping SET mapping_id = #{mappingId}, sdk_customer_id = #{sdkCustomerId}, "
            + "channel_code = #{channelCode}, channel_license = #{channelLicense}, status = #{status}, operator = #{operator}, "
            + "modify_time = #{modifyTime} WHERE id = #{id}")
    int update(SdkCustomerChannelMappingDO mapping);


    @Select({
            "<script>",
            "select * from tb_sdk_customer_channel_mapping ",
            "where ",
            //sdkCustomerIds
            " sdk_customer_id IN ",
            "<foreach item='item' collection='sdkCustomerIds' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            //
            "order by id desc limit 200",
            "</script>"})
    List<SdkCustomerChannelMappingDO> listChannelBySdkCustomerIds(@Param("sdkCustomerIds") List<String> sdkCustomerIds);

    @Update({
            "<script>",
            "update tb_sdk_customer_channel_mapping ",
            "set channel_license = #{channelLicense} ,harmony_os_license = #{harmonyOsLicense} , operator = #{operator} ,status = #{status}",
            "where ",
            //sdkCustomerIds
            "id = #{id}",
            "</script>"})
    int updateChannelLicense(@Param("id") Long id, @Param("channelLicense") String channelLicense,
                             @Param("harmonyOsLicense") String harmonyOsLicense, @Param("operator")String operator, @Param("status") String status);


    @Update({
            "<script>",
            "update tb_sdk_customer_channel_mapping ",
            "set bundle_id = #{bundleId} ",
            "where ",
            "sdk_customer_id = #{sdkCustomerId}",
            "</script>"})
    int updateBundleId(@Param("sdkCustomerId") String sdkCustomerId, @Param("bundleId") String bundleId);
}
