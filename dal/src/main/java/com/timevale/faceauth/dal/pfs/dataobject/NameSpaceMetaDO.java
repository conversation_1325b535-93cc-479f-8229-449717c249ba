package com.timevale.faceauth.dal.pfs.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * The table. 空间元数据
 *
 * <AUTHOR> Kunpeng
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NameSpaceMetaDO {

  /** id 自增主键. */
  private Long id;
  /** 空间ID. */
  private String spaceId;
  /** 空间名称. */
  private String spaceName;
  /** 空间级别[1一级空间,2二级空间,3三级空间]. */
  private String level;
  /** 父空间ID. */
  private String parentId;
}
