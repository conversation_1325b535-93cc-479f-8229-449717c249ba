package com.timevale.faceauth.dal.pfs.face.support;

import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * 刷脸缓存照片仓库调用
 * <AUTHOR>
 * @since 2024/7/11 17:07
 */
@Repository
public interface FaceLocalLibraryPhotoDAO {


    @InsertProvider(type = FaceLocalLibraryPhotoDAOSqlProvider.class, method = "insert")
    @SelectKey(
            statement = "select last_insert_id()",
            keyProperty = "e.id",
            before = false,
            resultType = long.class)
    int insert(@Param("e") FaceLocalLibraryPhotoDO e);

    @Select("select * from tb_face_local_library_photo where switch_enabled = 1 " +
            " and hash_factor2 = #{hashFactor2}   order by id  desc limit 10 ")
    List<FaceLocalLibraryPhotoDO> getLatestPhotoByFactor2(@Param("hashFactor2") String hashFactor2);


    @Update("update tb_face_local_library_photo set switch_enabled = 0  where switch_enabled = 1 and hash_factor2 = #{hashFactor2} ")
    int delUserPhoto(@Param("hashFactor2") String hashFactor2);

    @Update("update tb_face_local_library_photo set switch_enabled = 0  where switch_enabled = 1 and hash_factor2 = #{hashFactor2} and provider = #{provider} ")
    int delUserPhotoByProvider(@Param("hashFactor2") String hashFactor2, @Param("provider") String provider);
}
