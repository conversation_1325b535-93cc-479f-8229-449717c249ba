package com.timevale.faceauth.dal.pfs.manager;

import com.timevale.faceauth.dal.pfs.dataobject.AlertConfigDO;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/** 供应商告警配置 */
public interface AlertConfigDAO {

  @Select({
    "<script>",
    "SELECT `id`,`alert_id`,`target_type`,`target_id`,`expression`,`extend`,`create_time`,`modify_time` FROM `provider_alert_config`",
    " WHERE `target_id`=#{providerId} LIMIT 20",
    "</script>",
  })
  List<AlertConfigDO> getByProviderId(String providerId);

  @Select({
          "<script>",
          "SELECT `id`,`alert_id`,`target_type`,`target_id`,`expression`,`extend`,`create_time`,`modify_time` FROM `provider_alert_config`",
          " WHERE `alert_id`=#{alertId} LIMIT 20",
          "</script>",
  })
  AlertConfigDO getByAlertId(String alertId);
}
