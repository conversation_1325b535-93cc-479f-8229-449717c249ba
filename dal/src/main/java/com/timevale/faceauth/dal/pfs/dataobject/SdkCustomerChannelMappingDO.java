package com.timevale.faceauth.dal.pfs.dataobject;

import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Date;

@Data
public class SdkCustomerChannelMappingDO extends ToString {
    private Long id;
    private String mappingId;
    private String sdkCustomerId;
    private String channelCode;
    private String channelLicense;
    private String status;
    private String operator;
    private Date createTime;
    private Date modifyTime;

    /**
     * 包名
     */
    private String bundleId;
    // 2025/5/7
    private String harmonyOsLicense;

    // Constructors, Getters and Setters

    public String parseGroupKey() {
        return StringUtils.defaultString(sdkCustomerId) + StringUtils.defaultString(channelCode);
    }

    /**
     * true 有配置过渠道授权
     *
     * @return
     */
    public boolean hasLicense() {
        return !StringUtils.isAllBlank(getChannelLicense(), getHarmonyOsLicense());
    }
}