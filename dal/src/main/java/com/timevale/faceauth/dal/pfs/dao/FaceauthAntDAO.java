package com.timevale.faceauth.dal.pfs.dao;

import com.timevale.faceauth.dal.pfs.dataobject.FaceauthAntDO;
import com.timevale.faceauth.dal.pfs.provider.FaceauthAntProvider;
import com.timevale.faceauth.dal.pfs.face.support.FaceAuthQueryRequestDO;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;

import java.util.List;

/** <AUTHOR> MASTER ! */
public interface FaceauthAntDAO {

  /**
   * desc:插入表:faceauth_ant.<br>
   *
   * @param entity entity
   * @return int
   */
  @InsertProvider(type = FaceauthAntProvider.class, method = "insert")
  int insert(FaceauthAntDO entity);

  /**
   * 主键查询
   *
   * @param faceAuthId
   */
  @SelectProvider(type = FaceauthAntProvider.class, method = "findById")
  FaceauthAntDO getById(String faceAuthId);

  /**
   * 更新
   *
   * @param antDO
   */
  @UpdateProvider(type = FaceauthAntProvider.class, method = "update")
  void update(FaceauthAntDO antDO);

  /**
   * desc:专为快捷签出证查询使用
   *
   * @param faceAuthQueryRequestDO
   * @return List<InfoServiceBankthreeDO>
   */
  @Select({
          "<script>",
          "select * from faceauth_ant ",
          "where  1=1 ",
          "<if test='idno != null'> and id_no = #{idno} </if>",
          "<if test='status != null'> and status = #{status} </if>",
          "<if test='modifyTime != null'> and update_time between 0 and #{modifyTime} </if>",
          "order by update_time desc limit #{currIndex},#{pageSize}",
          "</script>"})
  List<FaceauthAntDO> queryByUpdateTimeAndIdNo(FaceAuthQueryRequestDO faceAuthQueryRequestDO);

  /**
   * 查询蚂蚁刷脸记录
   * @param faceAuthQueryRequestDO 查询参数
   * @return 刷脸记录
   */
  @Select({
          "<script>",
          "select * from faceauth_ant fa inner join faceauth fu on fa.id = fu.id ",
          "where  1=1 ",
          "<if test='idno != null'> and fa.id_no = #{idno} </if>",
          "<if test='status != null'> and fa.status = #{status} </if>",
          "<if test='modifyTime != null'> and fa.update_time between 0 and #{modifyTime} </if>",
          "order by fa.update_time desc limit #{currIndex},#{pageSize}",
          "</script>"})
  List<FaceauthAntDO> quickSignQueryListByAnt(FaceAuthQueryRequestDO faceAuthQueryRequestDO);
}
