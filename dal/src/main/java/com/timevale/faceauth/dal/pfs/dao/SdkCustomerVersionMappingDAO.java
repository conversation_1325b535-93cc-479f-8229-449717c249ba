package com.timevale.faceauth.dal.pfs.dao;

import com.timevale.faceauth.dal.pfs.dataobject.SdkCustomerVersionMappingDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2024/8/6 14:46
 */
public interface SdkCustomerVersionMappingDAO {


    @Insert("INSERT INTO tb_sdk_customer_version_mapping (mapping_id, sdk_customer_id, sdk_version, status,  operator) "
            + "VALUES (#{mappingId}, #{sdkCustomerId}, #{sdkVersion}, #{status},  #{operator})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(SdkCustomerVersionMappingDO mapping);



    @Select({
            "<script>",
            "select * from tb_sdk_customer_version_mapping ",
            "where ",
            //sdkCustomerIds
            " sdk_customer_id IN ",
            "<foreach item='item' collection='sdkCustomerIds' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            //
            "order by id desc limit 200",
            "</script>"})
    List<SdkCustomerVersionMappingDO> listVersionBySdkCustomerIds(@Param("sdkCustomerIds") List<String> sdkCustomerIds);


    @Select({
            "<script>",
            "select * from tb_sdk_customer_version_mapping ",
            "where ",
            " sdk_customer_id = #{sdkCustomerId}  and  sdk_version = #{sdkVersion}",
            //
            "order by id desc  limit 1",
            "</script>"})
    SdkCustomerVersionMappingDO selectByCIdAndVersion(@Param("sdkVersion") String sdkVersion, @Param("sdkCustomerId") String sdkCustomerId);

    @Update({
            "<script>",
            " update tb_sdk_customer_version_mapping ",
            "set  ",
            "latest_activate_time = #{latestActivateTime} ",
            " where ",
            " mapping_id = #{mappingId}  ",
            "</script>"})
    int updateLatestActivateTime( @Param("mappingId") String mappingId, @Param("latestActivateTime") Date latestActivateTime);
}
