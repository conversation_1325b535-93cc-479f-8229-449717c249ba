package com.timevale.faceauth.dal.pfs.liveness;

import com.timevale.faceauth.dal.pfs.liveness.support.LivenessVideoEntitySqlProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;

/**
 * 活体认证视频库
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/2/20
 */
public interface LivenessVideoEntityRepository {

  @InsertProvider(type = LivenessVideoEntitySqlProvider.class, method = "insert")
  @SelectKey(
      statement = "select last_insert_id()",
      keyProperty = "e.id",
      before = false,
      resultType = long.class)
  int insert(@Param("e") LivenessVideoEntity entity);

  @SelectProvider(type = LivenessVideoEntitySqlProvider.class, method = "getVideos")
  List<LivenessVideoEntity> getVideos(@Param("livenessId") String livenessId);

  @SelectProvider(type = LivenessVideoEntitySqlProvider.class, method = "pageVideos")
  List<LivenessVideoEntity> pageVideos(@Param("page") int page, @Param("size") int size);

  @SelectProvider(type = LivenessVideoEntitySqlProvider.class, method = "count")
  long count();
}
