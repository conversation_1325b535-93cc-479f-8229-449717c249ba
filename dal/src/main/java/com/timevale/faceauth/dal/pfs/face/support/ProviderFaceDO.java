package com.timevale.faceauth.dal.pfs.face.support;

import com.timevale.faceauth.dal.pfs.support.AutoIncrementIdentityStandardDO;
import com.timevale.faceauth.dal.pfs.support.DbFieldInfo;
import com.timevale.faceauth.dal.pfs.support.DbTableName;

/**
 * <AUTHOR>
 * @copyright 2019
 *     <p>供应商刷脸数据实体模型
 * @date 2019/9/19 17
 */
@DbTableName("tb_provider_face")
public class ProviderFaceDO extends AutoIncrementIdentityStandardDO {

  // 刷脸Id
  @DbFieldInfo(value = "face_id", isRequired = true)
  public String faceId;

  // 供应商
  @DbFieldInfo(value = "provider", isRequired = true)
  public String provider;

  // 供应商全称
  @DbFieldInfo(value = "full_name", isRequired = true)
  public String fullName;

  // 供应商刷脸入参数据正文
  @DbFieldInfo("face_input")
  public String faceInput;

  // 供应商刷脸地址
  @DbFieldInfo(value = "face_data", isRequired = true)
  public String faceData;

  // 供应商订单号
  @DbFieldInfo(value = "order_no", isRequired = true)
  public String orderNo;

  // 当前供应商刷脸完成状态。（刷脸完成：1）
  @DbFieldInfo(value = "is_done", isRequired = true)
  public byte isDone = 0x00;

  // 完成时间
  @DbFieldInfo(value = "done_time", isRequired = true)
  public long doneTime = 0L;

  // 供应商业务标识，当前字段用来持久化除额外订单号（order_no）的之外的其他业务标识
  @DbFieldInfo("thirdpart_id")
  public String thirdpartId;

  @DbFieldInfo("err_msg")
  public String errMsg;

  @DbFieldInfo("err_code")
  public String errCode;
}
