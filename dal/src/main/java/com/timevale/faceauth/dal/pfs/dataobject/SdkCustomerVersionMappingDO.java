package com.timevale.faceauth.dal.pfs.dataobject;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Date;

@Data
public class SdkCustomerVersionMappingDO extends ToString {
    private Long id;
    private String mappingId;
    private String sdkCustomerId;
    private String sdkVersion;
    private String status;
    private Date latestActivateTime;
    private String operator;
    private Date createTime;
    private Date modifyTime;
}




