package com.timevale.faceauth.dal.pfs.support;

import org.apache.ibatis.jdbc.SQL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @copyright 2019
 *     <p>SQL 命令语句生成器
 * @date 2019/9/19 21
 */
public abstract class SqlFactory {

  private SqlFactory() {}

  private static final Logger LOGGER = LoggerFactory.getLogger(SqlFactory.class);

  private static final Map<String, String> T_SQL_MAP = (new ConcurrentHashMap<>());

  private static String deduceTableName(Class<?> clazz) {
    DbTableName tableAnnotation = clazz.getDeclaredAnnotation(DbTableName.class);
    if (null == tableAnnotation) {
      throw new IllegalArgumentException(
          "Annotation[" + DbTableName.class + "] not found on type[" + clazz + "] .");
    }
    String tableName =
        (tableAnnotation.prefix() + tableAnnotation.value() + tableAnnotation.suffix());
    Assert.hasLength(tableName, "'tableName' is undefined .");
    return "`" + tableName + "`";
  }

  private static void detectDeclareFieldsAndAppendList(
      Class<?> declareType, final List<DbField> list) {
    for (Field field : declareType.getDeclaredFields()) {
      if (isColumnField(field)) {
        LOGGER.info("Find column field '" + field.getName() + "' .");
        list.add(DbField.valueOf(field));
      }
    }
  }

  private static boolean isColumnField(Field field) {
    Annotation[] annotations = field.getDeclaredAnnotations();
    if (null == annotations || 0 == annotations.length) {
      return false;
    }

    for (Annotation ann : annotations) {
      if (ann.annotationType().equals(DbFieldInfo.class)) {
        return true;
      }
    }

    return false;
  }

  private static List<DbField> detectDeclareFields(Class<?> clazz) {
    final Class<?> objClass = Object.class;
    if (objClass.equals(clazz)) {
      return Collections.emptyList();
    }

    final List<DbField> fields = new LinkedList<>();
    Class<?> declareType = clazz;
    do {
      detectDeclareFieldsAndAppendList(declareType, fields);
    } while (!(objClass.equals((declareType = declareType.getSuperclass()))));
    return fields;
  }

  private static String buildCmd(SQL sql) {
    String cmd = sql.toString();
    LOGGER.info("T-SQL[" + cmd + "]");
    return cmd;
  }

  private static String getTSql(
      String methodName, Class<?> entityType, Supplier<String> buildIfAbsent) {
    String sqlName = (entityType.getSimpleName() + "@" + methodName);
    String sql = T_SQL_MAP.get(sqlName);
    if (StringUtils.hasLength(sql)) {
      return sql;
    }

    sql = buildIfAbsent.get();
    T_SQL_MAP.put(sqlName, sql);
    return sql;
  }

  public static String sqlSelect(
      String methodName,
      Class<?> entityType,
      Supplier<WhereCondition[]> predicate,
      Sorted... sortedPredicate) {
    return sqlSelect0(methodName, entityType, predicate, sortedPredicate, (Integer) null);
  }

  public static String sqlSelectOne(
      String methodName,
      Class<?> entityType,
      Supplier<WhereCondition[]> predicate,
      Sorted... sortedPredicate) {
    return sqlSelect0(methodName, entityType, predicate, sortedPredicate, 1);
  }

  private static String sqlSelect0(
      String methodName,
      Class<?> entityType,
      Supplier<WhereCondition[]> predicate,
      Sorted[] sortedPredicate,
      Integer limit) {
    return getTSql(
        methodName,
        entityType,
        () -> {
          String tableName = deduceTableName(entityType);
          String[] fields =
              detectDeclareFields(entityType).stream()
                  .map(f -> columnName(f.getDbName()))
                  .toArray(String[]::new);
          String columns = String.join(",", fields);
          SQL sql = (new SQL()).SELECT(columns).FROM(tableName);
          configurationWherePredicate(sql, predicate);
          configurationSortedPredicate(sql, sortedPredicate);
          String tSql = buildCmd(sql);
          return (null == limit ? tSql : tSql + " limit " + limit);
        });
  }

  public static String sqlCount(Class<?> entityType, Supplier<WhereCondition[]> predicate) {
    String tableName = deduceTableName(entityType);
    String columns = "count(0)";
    SQL sql = (new SQL()).SELECT(columns).FROM(tableName);
    configurationWherePredicate(sql, predicate);
    return buildCmd(sql);
  }

  public static String sqlPage(
      Class<?> entityType,
      Supplier<WhereCondition[]> predicate,
      Sorted[] sortedPredicate,
      int page,
      int size) {
    int p = (Math.max(1, page));
    if (0 >= size) {
      throw new IndexOutOfBoundsException("size");
    }
    String tableName = deduceTableName(entityType);
    String[] fields =
        detectDeclareFields(entityType).stream()
            .map(f -> columnName(f.getDbName()))
            .toArray(String[]::new);
    String columns = String.join(",", fields);
    SQL sql = (new SQL()).SELECT(columns).FROM(tableName);
    configurationWherePredicate(sql, predicate);
    configurationSortedPredicate(sql, sortedPredicate);
    return (buildCmd(sql) + " limit " + ((p - 1) * size) + ", " + size);
  }

  private static void configurationWherePredicate(
      final SQL sql, final Supplier<WhereCondition[]> predicate) {
    if (null == predicate) return;
    WhereCondition[] conditions = predicate.get();
    if (null == conditions || 0 == conditions.length) return;
    String[] wheres =
        Arrays.stream(conditions)
            .filter(Objects::nonNull)
            .map(WhereCondition::toString)
            .toArray(String[]::new);
    if (0 == wheres.length) return;
    String whereStr = String.join(" and ", wheres);
    sql.WHERE(whereStr);
  }

  private static void configurationSortedPredicate(final SQL sql, Sorted... sortedPredicate) {
    if (0 == sortedPredicate.length) {
      return;
    }

    for (Sorted sorted : sortedPredicate) {
      if (null == sorted) {
        continue;
      }
      sql.ORDER_BY(sorted.toString());
    }
  }

  private static String columnName(String fieldName) {
    return "`" + fieldName + "`";
  }

  public static String sqlInsert(String methodName, Class<?> entityType, String parameterPrefix) {
    return getTSql(
        methodName,
        entityType,
        () -> {
          String tableName = deduceTableName(entityType);
          SQL sql = (new SQL()).INSERT_INTO(tableName);
          for (DbField field : detectDeclareFields(entityType)) {
            // 跳过自增量
            if (field.isAutoIncrement()) continue;
            sql =
                sql.VALUES(
                    columnName(field.getDbName()), parameterName(field.getName(), parameterPrefix));
          }
          return buildCmd(sql);
        });
  }

  public static String parameterName(String fieldName, String parameterPrefix) {
    String prefix = (StringUtils.hasLength(parameterPrefix) ? (parameterPrefix + ".") : "");
    return "#{" + prefix + fieldName + "}";
  }

  private static String setColumnData(String column, String field) {
    return (columnName(column) + "=" + parameterName(field, null));
  }

  public static String sqlUpdate(String methodName, Class<?> entityType) {
    return getTSql(
        methodName,
        entityType,
        () -> {
          String tableName = deduceTableName(entityType);
          List<DbField> fields = detectDeclareFields(entityType);
          DbField id = null;
          Set<String> updateSets = (new HashSet<>());
          for (DbField field : fields) {
            // 主键不设置
            if (field.isPrimary()) {
              id = field;
              continue;
            }
            // 排除忽略的更新
            if (field.isIgnoreUpdate()) continue;
            updateSets.add(setColumnData(field.getDbName(), field.getName()));
          }
          if (null == id) {
            throw new IllegalArgumentException("Not found column id on type[" + entityType + "].");
          }
          String setString = String.join(",", updateSets.toArray(new String[updateSets.size()]));
          SQL sql =
              (new SQL())
                  .UPDATE(tableName)
                  .SET(setString)
                  .WHERE(setColumnData(id.getDbName(), id.getName()));
          return buildCmd(sql);
        });
  }

  public static Column column(String name) {
    Assert.hasLength(name, "Column 'name' must not be null .");
    return (new Column(columnName(name)));
  }

  public static final class Column {

    private final String name;

    private Column(String name) {
      this.name = name;
    }

    public WhereCondition equals(String value) {
      return (new WhereCondition(name, value, "="));
    }
  }

  public static final class WhereCondition {

    // 列明
    private final String column;
    // 值
    private final String value;
    // 运算符
    private final String symbol;

    private WhereCondition(String column, String value, String symbol) {
      this.column = column;
      this.value = value;
      this.symbol = symbol;
    }

    @Override
    public String toString() {
      return "(" + column + symbol + value + ")";
    }
  }

  public static final class Sorted {

    private static final String TYPE_ASC = "ASC";
    private static final String TYPE_DESC = "DESC";

    private final String column;
    private final String sortedType;

    private Sorted(String column, String sortedType) {
      this.column = column;
      this.sortedType = sortedType;
    }

    public static Sorted asc(String column) {
      return (new Sorted(columnName(column), TYPE_ASC));
    }

    public static Sorted desc(String column) {
      return (new Sorted(columnName(column), TYPE_DESC));
    }

    @Override
    public String toString() {
      return column + " " + sortedType;
    }
  }
}
