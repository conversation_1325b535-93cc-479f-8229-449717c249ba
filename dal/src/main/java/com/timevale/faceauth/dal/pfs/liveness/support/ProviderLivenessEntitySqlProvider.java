package com.timevale.faceauth.dal.pfs.liveness.support;

import com.timevale.faceauth.dal.pfs.liveness.ProviderLivenessCompletedEntity;
import com.timevale.faceauth.dal.pfs.liveness.ProviderLivenessEntity;
import com.timevale.faceauth.dal.pfs.liveness.ProviderLivenessSpecifyActionsEntity;
import com.timevale.faceauth.dal.pfs.support.SqlFactory;
import org.apache.ibatis.annotations.Param;

/**
 * 第三方供应商活体认证库命令供应者
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/2/21
 */
public class ProviderLivenessEntitySqlProvider {

  public String insert(@Param("e") ProviderLivenessEntity entity) {
    return SqlFactory.sqlInsert((String) "insert", ProviderLivenessEntity.class, (String) "e");
  }

  public String getByLivenessId(@Param("livenessId") String livenessId) {
    return SqlFactory.sqlSelectOne(
        (String) "getByLivenessId",
        ProviderLivenessEntity.class,
        () ->
            (new SqlFactory.WhereCondition[] {
              SqlFactory.column((String) "liveness_id")
                  .equals(SqlFactory.parameterName((String) "livenessId", (String) null))
            }));
  }

  public String completedProviderLiveness(ProviderLivenessCompletedEntity entity) {
    return SqlFactory.sqlUpdate(
        (String) "completedProviderLiveness", ProviderLivenessCompletedEntity.class);
  }

  public String specifyLivenessActions(ProviderLivenessSpecifyActionsEntity entity) {
    return SqlFactory.sqlUpdate(
        (String) "specifyLivenessActions", ProviderLivenessSpecifyActionsEntity.class);
  }
}
