package com.timevale.faceauth.dal.pfs.face.support;

import com.timevale.faceauth.dal.pfs.support.AutoIncrementIdentityStandardDO;
import com.timevale.faceauth.dal.pfs.support.DbFieldInfo;
import com.timevale.faceauth.dal.pfs.support.DbTableName;

/**
 * <AUTHOR>
 * @copyright 2019
 *     <p>刷脸请求数据实体模型
 * @date 2019/9/19 16
 */
@DbTableName("tb_face")
public class FaceDO extends AutoIncrementIdentityStandardDO {

  @DbFieldInfo("tid")
  public String tid;

  // appId
  @DbFieldInfo(value = "app_id", isRequired = true)
  public String appId;

  // faceId
  @DbFieldInfo(value = "face_id", isRequired = true)
  public String faceId;

  // 账户id
  @DbFieldInfo(value = "oid", isRequired = true)
  public String oid;

  // 上游业务Id
  @DbFieldInfo(value = "biz_id", isRequired = true)
  public String bizId;

  // 业务编码
  @DbFieldInfo(value = "biz_code", isRequired = true)
  public String bizCode;

  // 业务场景
  @DbFieldInfo(value = "biz_scene")
  public String bizScene;

  // 刷脸接入端类型
  @DbFieldInfo(value = "client_type", isRequired = true)
  public String clientType;

  // 姓名
  @DbFieldInfo(value = "name", isRequired = true)
  public String name;

  // 证件号码
  @DbFieldInfo(value = "id_no", isRequired = true)
  public String idNo;

  // 证件类型
  @DbFieldInfo(value = "id_type", isRequired = true)
  public String idType;

  // 比对照片
  @DbFieldInfo(value = "photo", isRequired = true)
  public String photo;

  // 比对照片类型
  @DbFieldInfo(value = "photo_type", isRequired = true)
  public String photoType;

  // 供应商
  @DbFieldInfo(value = "provider", isRequired = true)
  public String provider;

  //供应商接口版本
  @DbFieldInfo(value = "provider_api_version")
  public String providerApiVersion;

  // 当前刷脸输入上下文
  @DbFieldInfo(value = "input")
  public String input;

  // 当前刷脸成功状态。（已成功：1）
  @DbFieldInfo(value = "is_ok", isRequired = true)
  public byte isOk = 0x00;

  @DbFieldInfo("result_msg")
  public String resultMsg;

  @DbFieldInfo("result_code")
  public String resultCode;

  // 当前刷脸成功回跳地址
  @DbFieldInfo("return_url")
  public String returnUrl;

  // 当前刷脸完成时回跳时间
  @DbFieldInfo(value = "return_time", isRequired = true)
  public long returnTime = 0L;

  // 当前刷脸完成时回跳失败原因
  @DbFieldInfo("return_err_msg")
  public String returnErrMsg;

  // 当前刷脸上游回调地址
  @DbFieldInfo("callback_url")
  public String callbackUrl;

  // 当前刷脸完成时回调上游业务开始时间
  @DbFieldInfo(value = "callback_time", isRequired = true)
  public long callbackTime = 0L;

  // 当前刷脸完成时上游业务回调完成状态。（已完成：1）
  @DbFieldInfo(value = "callback_is_done", isRequired = true)
  public byte callbackIsDone = 0x00;

  // 当前刷脸完成时回调上游业务完成时间
  @DbFieldInfo(value = "callback_done_time", isRequired = true)
  public long callbackDoneTime = 0L;

  // 当前刷脸完成时回调上游业务成功状态。（成功：1）
  @DbFieldInfo(value = "callback_is_ok", isRequired = true)
  public byte callbackIsOk = 0x00;

  // 当前刷脸完成时回调上游业务失败原因
  @DbFieldInfo("callback_err_msg")
  public String callbackErrMsg;
}
