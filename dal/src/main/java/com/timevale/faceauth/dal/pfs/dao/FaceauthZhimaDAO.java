package com.timevale.faceauth.dal.pfs.dao;

import com.timevale.faceauth.dal.pfs.dataobject.FaceauthZhimaDO;
import com.timevale.faceauth.dal.pfs.provider.FaceauthZhiMaProvider;
import com.timevale.faceauth.dal.pfs.face.support.FaceAuthQueryRequestDO;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;

import java.util.List;

/**
 * The Table faceauth_zhima. faceauth_zhima
 *
 * <AUTHOR> Kunpeng
 */
public interface FaceauthZhimaDAO {

  /**
   * desc:插入表:faceauth_zhima.<br>
   *
   * @param entity entity
   * @return int
   */
  @InsertProvider(type = FaceauthZhiMaProvider.class, method = "insert")
  int insert(FaceauthZhimaDO entity);

  /**
   * 根据id查询
   *
   * @param id
   * @return
   */
  @SelectProvider(type = FaceauthZhiMaProvider.class, method = "findById")
  FaceauthZhimaDO getById(String id);

  /**
   * 更新
   *
   * @param entity
   */
  @UpdateProvider(type = FaceauthZhiMaProvider.class, method = "update")
  void update(FaceauthZhimaDO entity);

  /**
   * desc:专为快捷签出证查询使用
   *
   * @param faceAuthQueryRequestDO
   * @return List<InfoServiceBankthreeDO>
   */
  @Select({
          "<script>",
          "select * from faceauth_zhima ",
          "where  1=1 ",
          "<if test='idno != null'> and id_no = #{idno} </if>",
          "<if test='passed != null'> and passed = #{passed} </if>",
          "<if test='modifyTime != null'> and update_time between 0 and #{modifyTime} </if>",
          "order by update_time desc limit #{currIndex},#{pageSize}",
          "</script>"})
  List<FaceauthZhimaDO> queryByUpdateTimeAndIdNo(FaceAuthQueryRequestDO faceAuthQueryRequestDO);

  @Select({
          "<script>",
          "select * from faceauth_zhima zm inner join faceauth fu on zm.id = fu.id",
          "where  1=1 ",
          "<if test='idno != null'> and zm.id_no = #{idno} </if>",
          "<if test='passed != null'> and zm.passed = #{passed} </if>",
          "<if test='updateTime != null'> and zm.update_time between 0 and #{updateTime} </if>",
          "order by zm.update_time desc limit #{currIndex},#{pageSize}",
          "</script>"})
  List<FaceauthZhimaDO> quickSignQueryListByZhiMa(FaceAuthQueryRequestDO faceAuthQueryRequestDO);
}
