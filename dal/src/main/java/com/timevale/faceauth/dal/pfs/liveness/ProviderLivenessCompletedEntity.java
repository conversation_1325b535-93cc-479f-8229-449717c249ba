package com.timevale.faceauth.dal.pfs.liveness;

import com.timevale.faceauth.dal.pfs.support.DbFieldInfo;
import com.timevale.faceauth.dal.pfs.support.DbTableName;

/**
 * 第三方供应商活体认证完成对象
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/2/21
 */
@DbTableName("tb_liveness_provider_order")
public class ProviderLivenessCompletedEntity {

    /** 活体认证 ID */
    @DbFieldInfo(value = "liveness_id", isPrimary = true)
    public String livenessId;

    /** 过期状态：过期（1）；未过期（0） */
    @DbFieldInfo("expired")
    public byte expired = 0x00;

    /** 完成状态：完成（1）；未完成（0） */
    @DbFieldInfo("completed")
    public byte completed = 0x01;

    /** 完成时间 */
    @DbFieldInfo("completed_time")
    public long completedTime = System.currentTimeMillis();

    /** 认证完成照片或者存储认证完成照片的键值（key） */
    @DbFieldInfo("photo")
    public String photo = "";

    /** 活体率分值 */
    @DbFieldInfo(value = "liveness_rate", isRequired = true)
    public Float livenessRate;

    /** 相似度分值 */
    @DbFieldInfo(value = "sim", isRequired = true)
    public Float sim;

    /** 收费状态：收费（1）；不收费（0） */
    @DbFieldInfo("fee")
    public byte fee = 0x00;

    /** 收费方式 */
    @DbFieldInfo(value = "fee_type", isRequired = true)
    public String feeType;

    /** 消息 */
    @DbFieldInfo("msg")
    public String message;
}
