package com.timevale.faceauth.dal.pfs.liveness;

import com.timevale.faceauth.dal.pfs.support.DbFieldInfo;
import com.timevale.faceauth.dal.pfs.support.DbTableName;

/**
 * 活体认证撤销对象
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/2/20
 */
@DbTableName("tb_liveness")
public class LivenessCancelledEntity {

  /** 活体认证 ID */
  @DbFieldInfo(value = "liveness_id", isPrimary = true)
  public String livenessId;

  /** 取消状态：取消（1）；未取消（0） */
  @DbFieldInfo("cancelled")
  public byte cancelled = 0x01;

  /** 取消时间 */
  @DbFieldInfo("cancelled_time")
  public long cancelledTime = System.currentTimeMillis();
}
