package com.timevale.faceauth.dal.pfs.liveness.support;

import com.timevale.faceauth.dal.pfs.liveness.ProviderLivenessVideoEntity;
import com.timevale.faceauth.dal.pfs.support.SqlFactory;
import org.apache.ibatis.annotations.Param;

/**
 * 第三方供应商活体认证视频库命令供应者
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/2/20
 */
public class ProviderLivenessVideoEntitySqlProvider {

  public String insert(@Param("e") ProviderLivenessVideoEntity entity) {
    return SqlFactory.sqlInsert(
        (String) "insert", ProviderLivenessVideoEntity.class, (String) "e");
  }

  public String getVideos(@Param("livenessId") String livenessId) {
    return SqlFactory.sqlSelect(
        (String) "getVideos",
        ProviderLivenessVideoEntity.class,
        () ->
            (new SqlFactory.WhereCondition[] {
              SqlFactory.column((String) "liveness_id")
                  .equals(SqlFactory.parameterName((String) "livenessId", (String) null))
            }));
  }
}
