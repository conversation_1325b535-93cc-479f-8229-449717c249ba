package com.timevale.faceauth.dal.pfs.dataobject;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Date;

@Data
public class SdkVersionDO extends ToString {
    private Long id;
    private String sdkVersionId;
    private String sdkVersion;
    private String status;
    private Date latestActivateTime;
    private String operator;
    private Date createTime;
    private Date modifyTime;
    // Constructors, Getters and Setters
}