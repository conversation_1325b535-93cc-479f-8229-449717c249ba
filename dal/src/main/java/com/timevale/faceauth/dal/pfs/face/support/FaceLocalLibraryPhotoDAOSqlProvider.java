package com.timevale.faceauth.dal.pfs.face.support;

import com.timevale.faceauth.dal.pfs.support.SqlFactory;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/28 11
 */
public class FaceLocalLibraryPhotoDAOSqlProvider {

  public String insert(@Param("e") FaceLocalLibraryPhotoDO e) {
    return SqlFactory.sqlInsert("insert", FaceLocalLibraryPhotoDO.class, "e");
  }

}
