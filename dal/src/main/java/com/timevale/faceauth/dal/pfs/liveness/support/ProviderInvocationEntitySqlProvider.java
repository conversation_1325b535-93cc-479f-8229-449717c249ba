package com.timevale.faceauth.dal.pfs.liveness.support;

import com.timevale.faceauth.dal.pfs.liveness.ProviderInvocationCompletedEntity;
import com.timevale.faceauth.dal.pfs.liveness.ProviderInvocationEntity;
import com.timevale.faceauth.dal.pfs.support.SqlFactory;
import org.apache.ibatis.annotations.Param;

/**
 * 供应商接口调用记录库命令供应者
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/2/20
 */
public class ProviderInvocationEntitySqlProvider {

  public String insert(@Param("e") ProviderInvocationEntity entity) {
    return SqlFactory.sqlInsert((String) "insert", ProviderInvocationEntity.class, (String) "e");
  }

  public String getByInvokeId(@Param("invokeId") String invokeId) {
    return SqlFactory.sqlSelect(
        (String) "getByInvokeId",
        ProviderInvocationEntity.class,
        () ->
            (new SqlFactory.WhereCondition[] {
              SqlFactory.column((String) "invoke_id")
                  .equals(SqlFactory.parameterName((String) "invokeId", (String) null))
            }));
  }

  public String getByBizId(@Param("bizCode") String bizCode, @Param("bizId") String bizId) {
    return SqlFactory.sqlSelectOne(
        (String) "getByBizId",
        ProviderInvocationEntity.class,
        () ->
            (new SqlFactory.WhereCondition[] {
              SqlFactory.column((String) "biz_code")
                  .equals(SqlFactory.parameterName((String) "bizCode", (String) null)),
              SqlFactory.column((String) "biz_id")
                  .equals(SqlFactory.parameterName((String) "bizId", (String) null))
            }));
  }

  public String completedInvocation(ProviderInvocationCompletedEntity entity) {
    return SqlFactory.sqlUpdate(
        (String) "completedInvocation", ProviderInvocationCompletedEntity.class);
  }
}
