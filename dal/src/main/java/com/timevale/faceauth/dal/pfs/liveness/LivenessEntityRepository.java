package com.timevale.faceauth.dal.pfs.liveness;

import com.timevale.faceauth.dal.pfs.liveness.support.LivenessEntitySqlProvider;
import org.apache.ibatis.annotations.*;

/**
 * 活体认证实体仓库
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/2/20
 */
public interface LivenessEntityRepository {

  @InsertProvider(type = LivenessEntitySqlProvider.class, method = "insert")
  @SelectKey(
      statement = "select last_insert_id()",
      keyProperty = "e.id",
      before = false,
      resultType = long.class)
  int insert(@Param("e") LivenessEntity e);

  @SelectProvider(type = LivenessEntitySqlProvider.class, method = "getByLivenessId")
  LivenessEntity getByLivenessId(@Param("livenessId") String livenessId);

  @SelectProvider(type = LivenessEntitySqlProvider.class, method = "getByBizId")
  LivenessEntity getByBizId(@Param("bizCode") String bizCode, @Param("bizId") String bizId);

  @UpdateProvider(type = LivenessEntitySqlProvider.class, method = "completedLiveness")
  int completedLiveness(LivenessCompletedEntity entity);

  @UpdateProvider(type = LivenessEntitySqlProvider.class, method = "cancelledLiveness")
  int cancelledLiveness(LivenessCancelledEntity entity);

  @UpdateProvider(type = LivenessEntitySqlProvider.class, method = "expiredLiveness")
  int expiredLiveness(LivenessExpiredEntity entity);
}
