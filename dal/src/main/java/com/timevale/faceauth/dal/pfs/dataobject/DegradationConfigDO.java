package com.timevale.faceauth.dal.pfs.dataobject;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * The table. 降级配置
 *
 * <AUTHOR> Kunpeng
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DegradationConfigDO extends ToString {

  /** id 自增主键. */
  private Long id;
  /** 降级配置ID. */
  private String configId;
  /** 供应商ID */
  private String providerId;
  /** 降级配置生效开关[0关闭,1开启] */
  private Integer switchEnabled;
  /** 触发告警ID */
  private String fireAlertIds;
  /** 自动恢复主通道开关[0关闭,1开启] */
  private Integer autoRecoveryEnabled;
  /** 自动恢复倒计时[分钟] */
  private Integer recoveryCountDown;
  /** 备用供应商ID */
  private String standbyProviderId;
  /** createTime 记录创建时间. */
  private Date createTime;
  /** modifyTime 记录修改时间. */
  private Date modifyTime;
}
