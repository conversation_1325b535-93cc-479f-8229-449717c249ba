package com.timevale.faceauth.dal.pfs.dao;

import com.timevale.faceauth.dal.pfs.dataobject.SdkCustomerDO;
import com.timevale.faceauth.dal.pfs.dataobject.query.SdkCustomerDOQuery;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2024/8/6 14:46
 */
public interface SdkCustomerDAO {

    @Insert("INSERT INTO tb_sdk_customer (sdk_customer_id, org_gid, org_name, status, operator) "
            + "VALUES (#{sdkCustomerId}, #{orgGid}, #{orgName}, #{status}, #{operator})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(SdkCustomerDO sdkCustomer);

    @Update("UPDATE tb_sdk_customer SET  "
            + " operator = #{operator} WHERE id = #{id}")
    int updateOperator(SdkCustomerDO sdkCustomer);



    @Select({
            "<script>",
            "select * from tb_sdk_customer ",
            "where ",
            "org_gid = #{orgGid} ",
            "order by id desc limit 1 ",
            "</script>"})
    SdkCustomerDO selectByGid(@Param("orgGid") String  orgGId);


    @Select({
            "<script>",
            "select * from tb_sdk_customer ",
            "where ",
            "org_gid = #{orgGid} ",
            "order by id desc  limit 1000",
            "</script>"})
    List<SdkCustomerDO> listByGid(@Param("orgGid") String  orgGId);

    @Select({
            "<script>",
            "select * from tb_sdk_customer ",
            "where ",
            "sdk_customer_id = #{sdkCustomerId} ",
            "order by id desc limit 1 ",
            "</script>"})
    SdkCustomerDO selectByCustomerId(@Param("sdkCustomerId") String  sdkCustomerId);


    @Select({
            "<script>",
            "select c.* from tb_sdk_customer c",
            "where  1=1 ",
            //orgGidList
            "<if test='orgGidList != null and orgGidList.size() > 0'>",
                "AND c.org_gid IN ",
                "<foreach item='item' collection='orgGidList' open='(' separator=',' close=')'>",
                "#{item}",
                "</foreach>",
            "</if>",
            //1、子查询
            "<if test='needChannelSql == true'>",
                "AND EXISTS (",
                "SELECT 1 FROM tb_sdk_customer_channel_mapping cm where  cm.sdk_customer_id = c.sdk_customer_id",
                // channelStatus
                "<if test='channelStatus != null'> and cm.status = #{channelStatus} </if>",
                //channelCodeList
                "<if test='channelCodeList != null and channelCodeList.size() > 0'>",
                    "AND cm.channel_code IN ",
                    "<foreach item='item' collection='channelCodeList' open='(' separator=',' close=')'>",
                    "#{item}",
                    "</foreach>",
                "</if>",
            ")",
            " </if>",
            //2、子查询
            "<if test='needSdkSql == true'>",
                "AND EXISTS (",
                "SELECT 1 from tb_sdk_customer_version_mapping vm where  vm.sdk_customer_id = c.sdk_customer_id",
                //sdkVersionList
                "<if test='sdkVersionList != null and sdkVersionList.size() > 0'>",
                    "AND vm.sdk_version IN ",
                    "<foreach item='item' collection='sdkVersionList' open='(' separator=',' close=')'>",
                    "#{item}",
                    "</foreach>",
                "</if>",
            ")",
            " </if>",
            //
            " order by c.id desc limit ${(pageNum-1)*pageSize}, ${pageSize}",
            "</script>"})
    List<SdkCustomerDO> page(SdkCustomerDOQuery query);

    @Select({
            "<script>",
            "select count(1) from tb_sdk_customer c",
            "where  1=1 ",
            //orgGidList
            "<if test='orgGidList != null and orgGidList.size() > 0'>",
            "AND c.org_gid IN ",
            "<foreach item='item' collection='orgGidList' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</if>",
            //1、子查询
            "<if test='needChannelSql == true'>",
            "AND EXISTS (",
            "SELECT 1 FROM tb_sdk_customer_channel_mapping cm where  cm.sdk_customer_id = c.sdk_customer_id",
            // channelStatus
            "<if test='channelStatus != null'> and cm.status = #{channelStatus} </if>",
            //channelCodeList
            "<if test='channelCodeList != null and channelCodeList.size() > 0'>",
            "AND cm.channel_code IN ",
            "<foreach item='item' collection='channelCodeList' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</if>",
            ")",
            " </if>",
            //2、子查询
            "<if test='needSdkSql == true'>",
            "AND EXISTS (",
            "SELECT 1 from tb_sdk_customer_version_mapping vm where  vm.sdk_customer_id = c.sdk_customer_id",
            //sdkVersionList
            "<if test='sdkVersionList != null and sdkVersionList.size() > 0'>",
            "AND vm.sdk_version IN ",
            "<foreach item='item' collection='sdkVersionList' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</if>",
            ")",
            " </if>",
            //
            "</script>"})
    Long count(SdkCustomerDOQuery query);






}
