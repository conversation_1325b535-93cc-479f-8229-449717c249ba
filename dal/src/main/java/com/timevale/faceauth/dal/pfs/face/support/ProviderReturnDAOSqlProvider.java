package com.timevale.faceauth.dal.pfs.face.support;

import com.timevale.faceauth.dal.pfs.support.SqlFactory;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/10 17
 */
public class ProviderReturnDAOSqlProvider {

  public String insert(@Param("e") ProviderReturnDO entity) {
    return SqlFactory.sqlInsert("insert", ProviderReturnDO.class, "e");
  }

  public String getProviderReturn(
      @Param("faceId") String faceId, @Param("returnType") String returnType) {
    return SqlFactory.sqlSelectOne(
        "getProviderReturn",
        ProviderReturnDO.class,
        () ->
            new SqlFactory.WhereCondition[] {
              SqlFactory.column("face_id").equals(SqlFactory.parameterName("faceId", null)),
              SqlFactory.column("return_type").equals(SqlFactory.parameterName("returnType", null))
            }, SqlFactory.Sorted.desc("id"));
  }

  public String getProviderReturnWithOutType(@Param("faceId") String faceId) {
    return SqlFactory.sqlSelectOne(
            "getProviderReturnWithOutType",
            ProviderReturnDO.class,
            () ->
                    new SqlFactory.WhereCondition[] {
                            SqlFactory.column("face_id").equals(SqlFactory.parameterName("faceId", null))
                    }, SqlFactory.Sorted.desc("id"));
  }
}
