package com.timevale.faceauth.dal.pfs.manager;

import com.timevale.faceauth.dal.pfs.dataobject.ProviderErrCodeEnumDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;

/** 供应商错误码枚举影子表 */
public interface ProviderErrCodeEnumShadowDAO {

  @Insert({
    "<script>",
    "INSERT INTO `provider_err_code_enum_shadow` (`p_code_id`, `provider_id`, `provider_key`, `mark_status`, `biz_md5`",
    "<if test=\"pCode != null and pCode != '' \"> <![CDATA[, p_code]]> </if> ",
    "<if test=\"pMsg != null and pMsg != '' \"> <![CDATA[, p_msg]]> </if> ",
    ") VALUES (#{pCodeId}, #{providerId}, #{providerKey}, #{markStatus}, #{bizMd5}",
    "<if test=\"pCode != null and pCode != '' \"> <![CDATA[, #{pCode}]]> </if> ",
    "<if test=\"pMsg != null and pMsg != '' \"> <![CDATA[, #{pMsg}]]> </if> ",
    ")",
    "</script>"
  })
  @Options(useGeneratedKeys = true, keyColumn = "id")
  int insert(ProviderErrCodeEnumDO entity);

  @Select({
    "<script>",
    "SELECT `p_code_id`,`provider_id`,`provider_key`, `mark_status`,`p_code`,`p_msg`,`biz_md5` FROM `provider_err_code_enum_shadow`",
    " WHERE `biz_md5`=#{md5} LIMIT 1",
    "</script>",
  })
  ProviderErrCodeEnumDO getByMd5(String md5);

  @Select({
    "<script>",
    "SELECT `p_code_id`,`provider_id`,`provider_key`, `mark_status`,`p_code`,`p_msg`,`biz_md5` FROM `provider_err_code_enum_shadow`",
    " WHERE `provider_id`=#{providerId} and `biz_md5`=#{md5} LIMIT 1",
    "</script>",
  })
  ProviderErrCodeEnumDO getByCodeMsg(String providerId, String md5);
}
