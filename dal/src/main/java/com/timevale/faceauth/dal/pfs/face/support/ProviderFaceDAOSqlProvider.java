package com.timevale.faceauth.dal.pfs.face.support;

import com.timevale.faceauth.dal.pfs.support.SqlFactory;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/10 17
 */
public class ProviderFaceDAOSqlProvider {

  public String insert(@Param("e") ProviderFaceDO entity) {
    return SqlFactory.sqlInsert("insert", ProviderFaceDO.class, "e");
  }

  public String getByFaceId(@Param("faceId") String faceId) {
    return SqlFactory.sqlSelect(
        "getByFaceId",
        ProviderFaceDO.class,
        () ->
            new SqlFactory.WhereCondition[] {
              SqlFactory.column("face_id").equals(SqlFactory.parameterName("faceId", null))
            });
  }

  public String getByProviderOrder(
      @Param("provider") String provider, @Param("order") String order) {
    return SqlFactory.sqlSelect(
        "getByProviderOrder",
        ProviderFaceDO.class,
        () ->
            new SqlFactory.WhereCondition[] {
              SqlFactory.column("provider").equals(SqlFactory.parameterName("provider", null)),
              SqlFactory.column("order_no").equals(SqlFactory.parameterName("order", null))
            });
  }

  public String completedProviderFace(ProviderFaceCompletionDO entity) {
    return SqlFactory.sqlUpdate("completedProviderFace", ProviderFaceCompletionDO.class);
  }
}
