package com.timevale.faceauth.dal.pfs.manager;

import com.timevale.faceauth.dal.pfs.dataobject.ProviderNotifyConfigDO;
import org.apache.ibatis.annotations.Select;

/** 供应商通知配置 */
public interface ProviderNotifyConfigDAO {

  @Select({
    "<script>",
    "SELECT `id`,`config_id`,`first_space_id`,`sms_enabled`,`ding_crop_enabled`,`ding_robot_enabled`,`ding_robot_token`,`notify_usernames`,`create_time`,`modify_time` FROM `provider_notify_config`",
    " WHERE `first_space_id`=#{firstSpaceId} LIMIT 1",
    "</script>",
  })
  ProviderNotifyConfigDO getByFirstSpaceId(String firstSpaceId);
}
