package com.timevale.faceauth.dal.pfs.laudiovideodual;

import com.timevale.faceauth.dal.pfs.laudiovideodual.support.AudioAndVideoDualEntitySqlProvider;
import org.apache.ibatis.annotations.*;

/**
 * 活体认证实体仓库
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/2/20
 */
public interface AudioAndVideoDualRepository {

  @InsertProvider(type = AudioAndVideoDualEntitySqlProvider.class, method = "insert")
  @SelectKey(
      statement = "select last_insert_id()",
      keyProperty = "e.id",
      before = false,
      resultType = long.class)
  int insert(@Param("e") AudioAndVideoDualEntity e);

  @SelectProvider(type = AudioAndVideoDualEntitySqlProvider.class, method = "getByCertifyId")
  AudioAndVideoDualEntity getByCertifyId(@Param("certifyId") String certifyId);

  @SelectProvider(type = AudioAndVideoDualEntitySqlProvider.class, method = "getByBizId")
  AudioAndVideoDualEntity getByBizId(@Param("bizId") String bizId);

  @UpdateProvider(type = AudioAndVideoDualEntitySqlProvider.class, method = "completed")
  int completed(AudioAndVideoDualCompletedEntity entity);


  @UpdateProvider(type = AudioAndVideoDualEntitySqlProvider.class, method = "aliayUser")
  int alipayUserUpdate(AudioAndVideoDualAlipayUserEntity entity);
}
