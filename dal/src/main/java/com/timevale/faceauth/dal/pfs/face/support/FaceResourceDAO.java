package com.timevale.faceauth.dal.pfs.face.support;

import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/28 11
 */
@Repository
public interface FaceResourceDAO {

    @InsertProvider(type = FaceResourceDAOSqlProvider.class, method = "insert")
    @SelectKey(
            statement = "select last_insert_id()",
            keyProperty = "e.id",
            before = false,
            resultType = long.class)
    int insert(@Param("e") FaceResourceDO e);


    @Select("select * from tb_face_resources where face_id = #{faceId}   order by id  limit 10")
    List<FaceResourceDO> getFaceResources(@Param("faceId") String faceId);

    @Delete("delete  from tb_face_resources where face_id = #{faceId} ")
    int delFaceResources(@Param("faceId") String faceId);

    @Select( "<script>"
            + "SELECT * FROM tb_face_resources "
            + "WHERE  face_id IN "
            + "<foreach item='item' index='index' collection='faceIds' open='(' separator=',' close=')'>"
            + "#{item}"
            + "</foreach> "
            + "  ORDER BY id DESC LIMIT 200"
            + "</script>")
    List<FaceResourceDO> getFaceResourcesBatch(
            @Param("faceIds") Set<String> faceIds );
}
