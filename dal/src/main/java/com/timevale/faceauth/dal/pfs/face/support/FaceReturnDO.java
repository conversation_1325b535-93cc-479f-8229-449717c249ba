package com.timevale.faceauth.dal.pfs.face.support;

import com.timevale.faceauth.dal.pfs.support.DbFieldInfo;
import com.timevale.faceauth.dal.pfs.support.DbTableName;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/10 16
 */
@DbTableName("tb_face")
public class FaceReturnDO extends FaceIdDO {

  // 上游业务Id
  @DbFieldInfo(value = "biz_id", isRequired = true)
  public String bizId;

  // 业务编码
  @DbFieldInfo(value = "biz_code", isRequired = true)
  public String bizCode;

  // 刷脸接入端类型
  @DbFieldInfo(value = "client_type", isRequired = true)
  public String clientType;

  // 当前刷脸成功回跳地址
  @DbFieldInfo("return_url")
  public String returnUrl;

  // 当前刷脸完成时回跳时间
  @DbFieldInfo(value = "return_time")
  public long returnTime = 0L;
}
