package com.timevale.faceauth.dal.pfs.dao;

import com.timevale.faceauth.dal.pfs.dataobject.FaceUserResourceControlRecordDO;
import com.timevale.faceauth.dal.pfs.dataobject.query.FaceUserResourceControlRecordQuery;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2025/7/1 17:16
 */
public interface FaceUserResourceControlRecordDAO {


    @Insert("INSERT INTO `tb_face_user_resource_control_record` " +
            "(`control_type`, " +
            "`operator`, " +
            "`status`, " +
            "`record_id`, " +
            "`app_id`, " +
            "`name`, " +
            "`id_no`, " +
            "`id_type`, " +
            "`mobile_no`, " +
            "`remark`, " +
            "`source_type`, " +
            "`source_biz_Id`, " +
            "`proof_documents` " +
            ") " +
            "VALUES " +
            "(#{controlType}, " +
            "#{operator}, " +
            "#{status}, " +
            "#{recordId}, " +
            "#{appId}, " +
            "#{name}, " +
            "#{idNo}, " +
            "#{idType}, " +
            "#{mobileNo}, " +
            "#{remark}, " +
            "#{sourceType}, " +
            "#{sourceBizId}, " +
            "#{proofDocuments} " +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(FaceUserResourceControlRecordDO faceSwitchDO);


    @Select({
            "<script>",
            "select * from tb_face_user_resource_control_record ",
            "where ",
            "record_id = #{recordId} ",
            "order by id desc limit 1 ",
            "</script>"})
    FaceUserResourceControlRecordDO selectOneByRecordId(@Param("recordId") String recordId);

    @Select({
            "<script>",
            "SELECT * FROM tb_face_user_resource_control_record",
            "<where>",
            // 根据 startTime 过滤
            "<if test='startTime != null'> AND create_time &gt;= #{startTime} </if>",
            // 根据 endTime 过滤
            "<if test='endTime != null'> AND create_time &lt;= #{endTime} </if>",
            // 根据 appIds 查询
            "<if test='appIds != null and appIds.size() > 0'>",
            "AND app_id IN",
            "<foreach item='item' collection='appIds' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</if>",
            // 根据 name 全匹配查询
            "<if test='name != null and name != \"\"'> AND name = #{name} </if>",
            // 根据 idNo 查询
            "<if test='idNo != null and idNo != \"\"'> AND id_no = #{idNo} </if>",
            // 根据 status 查询
            "<if test='status != null and idNo != \"\"'> AND status = #{status} </if>",
            // 根据 idType 查询
            "<if test='idType != null and idType != \"\"'> AND id_type = #{idType} </if>",
            "</where>",
            // 排序和分页
            "ORDER BY id DESC ",
            "LIMIT ${(pageNum - 1) * pageSize}, ${pageSize}",
            "</script>"
    })
    List<FaceUserResourceControlRecordDO> page(FaceUserResourceControlRecordQuery query);

    @Select({
            "<script>",
            "SELECT COUNT(1) FROM tb_face_user_resource_control_record",
            "<where>",
            // 根据 startTime 过滤
            "<if test='startTime != null'> AND create_time &gt;= #{startTime} </if>",
            // 根据 endTime 过滤
            "<if test='endTime != null'> AND create_time &lt;= #{endTime} </if>",
            // 根据 appIds 查询
            "<if test='appIds != null and appIds.size() > 0'>",
            "AND app_id IN",
            "<foreach item='item' collection='appIds' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</if>",
            // 根据 name 全匹配查询
            "<if test='name != null and name != \"\"'> AND name = #{name} </if>",
            // 根据 idNo 查询
            "<if test='idNo != null and idNo != \"\"'> AND id_no = #{idNo} </if>",
            // 根据 status 查询
            "<if test='status != null and idNo != \"\"'> AND status = #{status} </if>",
            // 根据 idType 查询
            "<if test='idType != null and idType != \"\"'> AND id_type = #{idType} </if>",
            "</where>",
            "</script>"
    })
    Long count(FaceUserResourceControlRecordQuery query);

    @Update({
            "<script>",
            "UPDATE tb_face_user_resource_control_record",
            "<set>",
            "   <if test='mobileNo != null and mobileNo != \"\"'>mobile_no = #{mobileNo},</if>",
            "   <if test='remark != null and remark != \"\"'>remark = #{remark},</if>",
            "   <if test='proofDocuments != null and proofDocuments != \"\"'>proof_documents = #{proofDocuments},</if>",
            "   <if test='status != null and status != \"\"'>status = #{status},</if>",
            "</set>",
            "WHERE record_id = #{recordId}",
            "</script>"
    })
    void modifyControlRecord(FaceUserResourceControlRecordDO input);


    @Update({
            "<script>",
            "UPDATE tb_face_user_resource_control_record set ",
            " status = #{status}  , remark =#{remark} ",
            "WHERE record_id = #{recordId}",
            "</script>"
    })
    void modifyStatusControlRecord(@Param("recordId") String recordId, @Param("status") String status, @Param("remark") String remark);

    @Select({
            "<script>",
            "select * from tb_face_user_resource_control_record ",
            "where ",
            "`name` = #{name} ",
            "and control_type = #{controlType} ",
            "and id_no = #{idNo} ",
            "and app_id = #{appId} ",
            "and status = #{status} ",
            "<if test='idType != null and idType != \"\"'> AND id_type = #{idType} </if>",
            "order by id desc limit 1 ",
            "</script>"})
    FaceUserResourceControlRecordDO selectOne(FaceUserResourceControlRecordDO input);

}

