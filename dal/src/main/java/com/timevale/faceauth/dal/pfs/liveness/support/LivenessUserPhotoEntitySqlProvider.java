package com.timevale.faceauth.dal.pfs.liveness.support;

import com.timevale.faceauth.dal.pfs.liveness.LivenessUserPhotoEntity;
import com.timevale.faceauth.dal.pfs.support.SqlFactory;
import org.apache.ibatis.annotations.Param;

/**
 * 活体认证用户有效照片库命令供应者
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/2/20
 */
public class LivenessUserPhotoEntitySqlProvider {

  public String insert(@Param("e") LivenessUserPhotoEntity entity) {
    return SqlFactory.sqlInsert((String) "insert", LivenessUserPhotoEntity.class, (String) "e");
  }

  public String getByHash(@Param("hash") String hash) {
    return SqlFactory.sqlSelectOne(
        (String) "getByHash",
        LivenessUserPhotoEntity.class,
        () ->
            (new SqlFactory.WhereCondition[] {
              SqlFactory.column((String) "hash")
                  .equals(SqlFactory.parameterName((String) "hash", (String) null))
            }));
  }
}
