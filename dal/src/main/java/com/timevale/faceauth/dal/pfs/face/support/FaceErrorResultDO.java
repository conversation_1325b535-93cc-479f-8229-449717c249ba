package com.timevale.faceauth.dal.pfs.face.support;

import com.timevale.faceauth.dal.pfs.support.DbFieldInfo;
import com.timevale.faceauth.dal.pfs.support.DbTableName;

/**
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/11/20 15
 */
@DbTableName("tb_face")
public class FaceErrorResultDO extends FaceIdDO {

  @DbFieldInfo("result_msg")
  public String resultMsg;

  @DbFieldInfo("result_code")
  public String resultCode;
}
