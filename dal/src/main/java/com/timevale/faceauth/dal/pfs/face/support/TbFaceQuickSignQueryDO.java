package com.timevale.faceauth.dal.pfs.face.support;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/10 16
 */
public class TbFaceQuickSignQueryDO {
  /** createTime 记录创建时间. */
  private Timestamp createTime;
  /** modifyTime 记录修改时间. */
  private Timestamp modifyTime;
  /** id 自增主键. */
  private Long id;
  /**transactionId. */
  private String tid;
  /** 刷脸请求唯一id */
  private String faceId;
  /**应用id. */
  private String appId;
  /**账户id. */
  private String oid;
  /**上游业务Id. */
  private String bizId;
  /**业务编码. */
  private String bizCode;
  /**刷脸接入端类型. */
  private String clientType;
  /**姓名. */
  private String name;
  /**证件号码. */
  private String idno;
  /**证件类型. */
  private String idType;
  /**比对照片. */
  private String photo;
  /**比对照片类型. */
  private String photoType;
  /**供应商. */
  private String provider;
  /**当前刷脸输入上下文. */
  private String input;
  /**当前刷脸成功状态。（已成功：1）. */
  private String isOk;
  /**当前刷脸成功回跳地址. */
  private String returnUrl;
  /**当前刷脸完成时回跳时间. */
  private String returnTime;
  /**当前刷脸完成时回跳失败原因. */
  private String returnErrMsg;
  /**当前刷脸上游回调地址. */
  private String callbackUrl;
  /**当前刷脸完成时回调上游业务开始时间. */
  private String callbackTime;
  /**当前刷脸完成时回调上游业务成功状态。（成功：1）. */
  private String callbackIsOk;
  /**当前刷脸完成时回调上游业务失败原因. */
  private String callbackErrMsg;

  public Timestamp getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Timestamp createTime) {
    this.createTime = createTime;
  }

  public Timestamp getModifyTime() {
    return modifyTime;
  }

  public void setModifyTime(Timestamp modifyTime) {
    this.modifyTime = modifyTime;
  }

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getTid() {
    return tid;
  }

  public void setTid(String tid) {
    this.tid = tid;
  }

  public String getFaceId() {
    return faceId;
  }

  public void setFaceId(String faceId) {
    this.faceId = faceId;
  }

  public String getAppId() {
    return appId;
  }

  public void setAppId(String appId) {
    this.appId = appId;
  }

  public String getOid() {
    return oid;
  }

  public void setOid(String oid) {
    this.oid = oid;
  }

  public String getBizId() {
    return bizId;
  }

  public void setBizId(String bizId) {
    this.bizId = bizId;
  }

  public String getBizCode() {
    return bizCode;
  }

  public void setBizCode(String bizCode) {
    this.bizCode = bizCode;
  }

  public String getClientType() {
    return clientType;
  }

  public void setClientType(String clientType) {
    this.clientType = clientType;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getIdno() {
    return idno;
  }

  public void setIdno(String idno) {
    this.idno = idno;
  }

  public String getIdType() {
    return idType;
  }

  public void setIdType(String idType) {
    this.idType = idType;
  }

  public String getPhoto() {
    return photo;
  }

  public void setPhoto(String photo) {
    this.photo = photo;
  }

  public String getPhotoType() {
    return photoType;
  }

  public void setPhotoType(String photoType) {
    this.photoType = photoType;
  }

  public String getProvider() {
    return provider;
  }

  public void setProvider(String provider) {
    this.provider = provider;
  }

  public String getInput() {
    return input;
  }

  public void setInput(String input) {
    this.input = input;
  }

  public String getIsOk() {
    return isOk;
  }

  public void setIsOk(String isOk) {
    this.isOk = isOk;
  }

  public String getReturnUrl() {
    return returnUrl;
  }

  public void setReturnUrl(String returnUrl) {
    this.returnUrl = returnUrl;
  }

  public String getReturnTime() {
    return returnTime;
  }

  public void setReturnTime(String returnTime) {
    this.returnTime = returnTime;
  }

  public String getReturnErrMsg() {
    return returnErrMsg;
  }

  public void setReturnErrMsg(String returnErrMsg) {
    this.returnErrMsg = returnErrMsg;
  }

  public String getCallbackUrl() {
    return callbackUrl;
  }

  public void setCallbackUrl(String callbackUrl) {
    this.callbackUrl = callbackUrl;
  }

  public String getCallbackTime() {
    return callbackTime;
  }

  public void setCallbackTime(String callbackTime) {
    this.callbackTime = callbackTime;
  }

  public String getCallbackIsOk() {
    return callbackIsOk;
  }

  public void setCallbackIsOk(String callbackIsOk) {
    this.callbackIsOk = callbackIsOk;
  }

  public String getCallbackErrMsg() {
    return callbackErrMsg;
  }

  public void setCallbackErrMsg(String callbackErrMsg) {
    this.callbackErrMsg = callbackErrMsg;
  }

  @Override
  public String toString() {
    return super.toString();
  }
}
