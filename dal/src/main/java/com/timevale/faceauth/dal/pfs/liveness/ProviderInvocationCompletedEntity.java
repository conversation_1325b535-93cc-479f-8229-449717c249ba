package com.timevale.faceauth.dal.pfs.liveness;

import com.timevale.faceauth.dal.pfs.support.DbFieldInfo;
import com.timevale.faceauth.dal.pfs.support.DbTableName;

/**
 * 供应商调用完成实体
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/2/20
 */
@DbTableName("tb_provider_invocation")
public class ProviderInvocationCompletedEntity {

    /** 业务调用 ID */
    @DbFieldInfo(value = "invoke_id", isPrimary = true)
    public String invokeId;

    /** 当前调用发生错误状态：发生错误（1）；未发生错误（0） */
    @DbFieldInfo("err")
    public byte err = 0x00;

    /** 当前调用发生的错误消息 */
    @DbFieldInfo(value = "err_msg")
    public String errMsg = "";

    /** 当前调用的响应状态：有响应（1）；无响应（0） */
    @DbFieldInfo("responsed")
    public byte responsed = 0x01;

    /** 响应数据内容，详细数据结合 content_location 字段获得 */
    @DbFieldInfo(value = "response")
    public String response = "";

    /** 响应时间 */
    @DbFieldInfo("responsed_time")
    public long responsedTime = System.currentTimeMillis();
}
