package com.timevale.faceauth.dal.pfs.dataobject;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Date;

@Data
public class SdkCustomerDO extends ToString {
    private Long id;
    private String sdkCustomerId;
    private String orgGid;
    private String orgName;
    private String status;
    private String operator;
    private Date createTime;
    private Date modifyTime;

    // Constructors, Getters and Setters
}