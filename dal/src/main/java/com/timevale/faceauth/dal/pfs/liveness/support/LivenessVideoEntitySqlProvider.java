package com.timevale.faceauth.dal.pfs.liveness.support;

import com.timevale.faceauth.dal.pfs.liveness.LivenessVideoEntity;
import com.timevale.faceauth.dal.pfs.support.SqlFactory;
import org.apache.ibatis.annotations.Param;

import java.util.function.Supplier;

/**
 * 活体认证视频库命令供应者
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/2/20
 */
public class LivenessVideoEntitySqlProvider {

  public String insert(@Param("e") LivenessVideoEntity entity) {
    return SqlFactory.sqlInsert((String) "insert", LivenessVideoEntity.class, (String) "e");
  }

  public String getVideos(@Param("livenessId") String livenessId) {
    return SqlFactory.sqlSelect(
        (String) "getVideos",
        LivenessVideoEntity.class,
        () ->
            (new SqlFactory.WhereCondition[] {
              SqlFactory.column((String) "liveness_id")
                  .equals(SqlFactory.parameterName((String) "livenessId", (String) null))
            }));
  }

  public String pageVideos(@Param("page") int page, @Param("size") int size) {
    return SqlFactory.sqlPage(
        LivenessVideoEntity.class,
        (Supplier<SqlFactory.WhereCondition[]>) null,
        new SqlFactory.Sorted[] {SqlFactory.Sorted.asc("id")},
        page,
        size);
  }

  public String count() {
    return SqlFactory.sqlCount(
        LivenessVideoEntity.class, (Supplier<SqlFactory.WhereCondition[]>) null);
  }
}
