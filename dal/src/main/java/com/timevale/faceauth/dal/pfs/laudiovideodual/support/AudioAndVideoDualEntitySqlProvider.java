package com.timevale.faceauth.dal.pfs.laudiovideodual.support;

import com.timevale.faceauth.dal.pfs.laudiovideodual.AudioAndVideoDualAlipayUserEntity;
import com.timevale.faceauth.dal.pfs.laudiovideodual.AudioAndVideoDualCompletedEntity;
import com.timevale.faceauth.dal.pfs.laudiovideodual.AudioAndVideoDualEntity;
import com.timevale.faceauth.dal.pfs.support.SqlFactory;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.jdbc.SQL;

/**
 * 智能视频认证命令供应器
 *
 * <AUTHOR>
 */
public class AudioAndVideoDualEntitySqlProvider {

  public String getTableName() {
    return "tb_audiovideodual";
  }

  public String insert(@Param("e") AudioAndVideoDualEntity e) {
    return SqlFactory.sqlInsert((String) "insert", AudioAndVideoDualEntity.class, (String) "e");
  }

  public String getByCertifyId(@Param("certifyId") String certifyId) {
    return SqlFactory.sqlSelect(
        (String) "getByCertifyId",
            AudioAndVideoDualEntity.class,
        () ->
            (new SqlFactory.WhereCondition[] {
              SqlFactory.column((String) "certify_id")
                  .equals(SqlFactory.parameterName((String) "certifyId", (String) null))
            }));
  }

  public String getByBizId(@Param("bizId") String bizId) {
    return SqlFactory.sqlSelect(
        (String) "getByBizId",
            AudioAndVideoDualEntity.class,
        () ->
            new SqlFactory.WhereCondition[] {
              SqlFactory.column((String) "biz_id")
                  .equals(SqlFactory.parameterName((String) "bizId", (String) null))
            });
  }


  /**
   * 更新
   *
   * @param entity
   * @return
   */
  public String completed(AudioAndVideoDualCompletedEntity entity) {
    return new SQL() {
      {
        UPDATE(getTableName());
        SET("completed_status=#{completedStatus}");
        SET("message=#{message}");
        SET("completed_time=#{completedTime}");
        WHERE("certify_id=#{certifyId}");
      }
    }.toString();
  }



  /**
   * 更新
   *
   * @param entity
   * @return
   */
  public String aliayUser(AudioAndVideoDualAlipayUserEntity entity) {
    return new SQL() {
      {
        UPDATE(getTableName());
        SET("alipay_user_id=#{alipayUserId}");
        SET("alipay_user_mobile=#{alipayUserMobile}");
        WHERE("certify_id=#{certifyId}");
      }
    }.toString();
  }


}
