package com.timevale.faceauth.dal.pfs.liveness;

import com.timevale.faceauth.dal.pfs.support.AutoIncrementIdentityStandardDO;
import com.timevale.faceauth.dal.pfs.support.DbFieldInfo;
import com.timevale.faceauth.dal.pfs.support.DbTableName;

/**
 * 活体认证有效的用户照片实体对象
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/2/20
 */
@DbTableName("tb_liveness_user_photo")
public class LivenessUserPhotoEntity extends AutoIncrementIdentityStandardDO {

  /** 证件类型 */
  @DbFieldInfo(value = "id_type", isRequired = true)
  public String idType;

  /** 证件号码 */
  @DbFieldInfo(value = "id_no", isRequired = true)
  public String idNo;

  /** 姓名 */
  @DbFieldInfo(value = "name", isRequired = true)
  public String name;

  /** 散列码 */
  @DbFieldInfo(value = "hash", isRequired = true)
  public String hash;

  /** 散列方式 */
  @DbFieldInfo(value = "hash_algorithm", isRequired = true)
  public String hashAlgorithm;

  /** 照片 */
  @DbFieldInfo(value = "photo", isRequired = true)
  public String photo;

  /** 照片位置，或者照片存储的方式 */
  @DbFieldInfo(value = "photo_location", isRequired = true)
  public String photoLocation;

  /** 照片类型 */
  @DbFieldInfo(value = "photo_type", isRequired = true)
  public String photoType;
}
