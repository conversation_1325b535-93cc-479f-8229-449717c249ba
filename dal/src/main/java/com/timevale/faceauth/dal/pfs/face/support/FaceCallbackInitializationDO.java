package com.timevale.faceauth.dal.pfs.face.support;

import com.timevale.faceauth.dal.pfs.support.DbFieldInfo;
import com.timevale.faceauth.dal.pfs.support.DbTableName;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/10 15
 */
@DbTableName("tb_face")
public class FaceCallbackInitializationDO extends FaceIdDO {

  // 当前刷脸完成时回调上游业务开始时间
  @DbFieldInfo(value = "callback_time", isRequired = true)
  public long callbackTime = 0L;
}
