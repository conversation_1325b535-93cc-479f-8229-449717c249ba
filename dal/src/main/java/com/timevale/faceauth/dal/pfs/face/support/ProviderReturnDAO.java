package com.timevale.faceauth.dal.pfs.face.support;

import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/10 17
 */
@Repository
public interface ProviderReturnDAO {

  @SelectKey(
      statement = "select last_insert_id()",
      keyProperty = "e.id",
      before = false,
      resultType = long.class)
  @InsertProvider(type = ProviderReturnDAOSqlProvider.class, method = "insert")
  int insert(@Param("e") ProviderReturnDO entity);

  @SelectProvider(type = ProviderReturnDAOSqlProvider.class, method = "getProviderReturn")
  ProviderReturnDO getProviderReturn(@Param("faceId") String faceId, @Param("returnType") String returnType);

  @SelectProvider(type = ProviderReturnDAOSqlProvider.class, method = "getProviderReturnWithOutType")
  ProviderReturnDO getProviderReturnWithOutType(@Param("faceId") String faceId);
}
