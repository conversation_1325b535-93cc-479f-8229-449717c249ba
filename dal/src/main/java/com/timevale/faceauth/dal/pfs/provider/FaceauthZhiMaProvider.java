package com.timevale.faceauth.dal.pfs.provider;

import com.timevale.faceauth.dal.pfs.dataobject.FaceauthZhimaDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.jdbc.SQL;

/**
 * <AUTHOR> on 2018/7/23
 *     <p>人脸识别芝麻副表provider
 */
public class FaceauthZhiMaProvider {

  private static final String TB_NAME = "faceauth_zhima";

  /**
   * 插入
   *
   * @param entity
   * @return
   */
  public String insert(FaceauthZhimaDO entity) {
    return new SQL() {
      {
        INSERT_INTO(TB_NAME);

        VALUES("id", "#{id}");
        VALUES("name", "#{name}");
        VALUES("id_no", "#{idNo}");
        VALUES("transaction_id", "#{transactionId}");
        VALUES("biz_no", "#{bizNo}");
        VALUES("passed", "#{passed}");
        VALUES("error_code", "#{errorCode}");
        VALUES("xcx", "#{xcx}");
        VALUES("app_id", "#{appId}");
        VALUES("callback_url", "#{callbackUrl}");
        VALUES("create_time", "now()");
      }
    }.toString();
  }

  /**
   * 根据id查询
   *
   * @param id
   * @return
   */
  public String findById(@Param("id") String id) {
    return new SQL() {
      {
        SELECT("*");
        FROM(TB_NAME);
        WHERE("id=#{id}");
      }
    }.toString();
  }

  /**
   * @param entity
   * @return
   */
  public String update(FaceauthZhimaDO entity) {
    return new SQL() {
      {
        UPDATE(TB_NAME);

        SET("error_code=#{errorCode}");
        SET("passed=#{passed}");
        SET("update_time=now()");

        WHERE("id=#{id}");
      }
    }.toString();
  }
}
