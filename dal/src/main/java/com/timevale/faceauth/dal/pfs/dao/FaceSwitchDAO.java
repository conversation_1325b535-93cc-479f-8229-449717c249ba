package com.timevale.faceauth.dal.pfs.dao;

import com.timevale.faceauth.dal.pfs.dataobject.FaceSwitchDO;
import com.timevale.faceauth.dal.pfs.support.FaceSwitchQuery;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ Id: FaceSwitchDAO.java, v0.1 2020年11月12日 17:25 WangYuWu $
 */
@Mapper
public interface FaceSwitchDAO {

  @Insert("INSERT INTO `tb_face_switch` " +
          "(`face_id`, " +
          "`id_no`, " +
          "`name`, " +
          "`face_mode`, " +
          "`err_msg`, " +
          "`is_switched`) " +
          "VALUES " +
          "(#{faceId}," +
          "#{idNo}," +
          "#{name}," +
          "#{faceMode}," +
          "#{errMsg}," +
          "#{isSwitched})")
  @Options(useGeneratedKeys = true, keyProperty = "id")
  int insert(FaceSwitchDO faceSwitchDO);

  @Select("SELECT `face_id`," +
          "`id_no`," +
          "`name`," +
          "`face_mode`," +
          "`err_msg`," +
          "`is_switched`," +
          "`extended_info`," +
          "`create_time`," +
          "`update_time` " +
          "FROM " +
          "`tb_face_switch` " +
          "WHERE " +
          "name = #{name} and  " +
          "id_no = #{idNo} and " +
          "face_mode = #{faceMode} and " +
          "is_switched = #{isSwitched} " +
          "ORDER BY create_time " +
          "DESC LIMIT 1")
  FaceSwitchDO queryLatestRecord(FaceSwitchDO faceSwitchDO);

  @Select("<script>" +
          "SELECT `face_id`," +
          "`id_no`," +
          "`name`," +
          "`face_mode`," +
          "`err_msg`," +
          "`is_switched`," +
          "`extended_info`," +
          "`create_time`," +
          "`update_time` " +
          "FROM " +
          "`tb_face_switch` " +
          "WHERE 1=1 " +
          "<if test=\"name != null and name != ''\">and name=#{name} </if>" +
          "<if test=\"idNo != null and idNo != ''\">and id_no=#{idNo} </if>" +
          "<if test=\"faceMode != null and faceMode != ''\">and face_mode=#{faceMode} </if>" +
          "order by create_time desc limit #{start}, #{pageSize}" +
          "</script>")
  List<FaceSwitchDO> queryFaceSwitchRecords(FaceSwitchQuery query);

  @Update("UPDATE `tb_face_switch` " +
          "SET " +
          "`is_switched` = #{isSwitched}, " +
          "`update_time` = now() " +
          "WHERE " +
          "`id_no` = #{idNo} and " +
          "`name` = #{name} and " +
          "`face_mode` = #{faceMode} and " +
          "`is_switched` = 0")
  int updateSwitchFlag(FaceSwitchDO faceSwitchDO);

}
