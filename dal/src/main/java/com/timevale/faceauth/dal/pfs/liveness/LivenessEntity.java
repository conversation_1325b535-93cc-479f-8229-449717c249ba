package com.timevale.faceauth.dal.pfs.liveness;

import com.timevale.faceauth.dal.pfs.support.AutoIncrementIdentityStandardDO;
import com.timevale.faceauth.dal.pfs.support.DbFieldInfo;
import com.timevale.faceauth.dal.pfs.support.DbTableName;

/**
 * 活体认证实体对象
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/2/20
 */
@DbTableName("tb_liveness")
public class LivenessEntity extends AutoIncrementIdentityStandardDO {

  /** 活体认证 ID */
  @DbFieldInfo(value = "liveness_id", isRequired = true)
  public String livenessId;

  /** 活体认证方式 */
  @DbFieldInfo(value = "liveness_type", isRequired = true)
  public String livenessType;

  /** 应用 ID */
  @DbFieldInfo(value = "app_id", isRequired = true)
  public String appId;

  /** 业务类型编码 */
  @DbFieldInfo(value = "biz_code", isRequired = true)
  public String bizCode;

  /** 是否显示协议页面，默认显示 */
  @DbFieldInfo(value = "show_agreement", isRequired = true)
  public int showAgreement;

  /** 业务 ID */
  @DbFieldInfo(value = "biz_id", isRequired = true)
  public String bizId;

  /** 证件类型 */
  @DbFieldInfo(value = "id_type", isRequired = true)
  public String idType;

  /** 证件号码 */
  @DbFieldInfo(value = "id_no", isRequired = true)
  public String idNo;

  /** 姓名 */
  @DbFieldInfo(value = "name", isRequired = true)
  public String name;

  /** 供应商代码 */
  @DbFieldInfo(value = "provider_code", isRequired = true)
  public String providerCode;

  /** 完成状态：完成（1）；未完成（0） */
  @DbFieldInfo("completed")
  public byte completed = 0x00;

  /** 完成时间 */
  @DbFieldInfo("completed_time")
  public long completedTime = 0L;

  /** 取消状态：取消（1）；未取消（0） */
  @DbFieldInfo("cancelled")
  public byte cancelled = 0x00;

  /** 取消时间 */
  @DbFieldInfo("cancelled_time")
  public long cancelledTime = 0L;

  /** 过期状态：过期（1）；未过期（0） */
  @DbFieldInfo("expired")
  public byte expired = 0x00;

  /** 过期时间 */
  @DbFieldInfo("expired_time")
  public long expiredTime = 0L;

  @DbFieldInfo("result_msg")
  public String resultMsg;

  @DbFieldInfo("result_code")
  public String resultCode;
}
