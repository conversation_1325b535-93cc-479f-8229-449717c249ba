package com.timevale.faceauth.dal.pfs.face.support;

import com.timevale.faceauth.dal.pfs.support.AutoIncrementIdentityStandardDO;
import com.timevale.faceauth.dal.pfs.support.DbFieldInfo;
import com.timevale.faceauth.dal.pfs.support.DbTableName;

import java.util.Date;

@DbTableName("tb_face_local_library_photo_v2")
public class FaceLocalLibraryPhotoV2DO extends AutoIncrementIdentityStandardDO {

    /**
     * 是否启用，1=启用，0=停用
     */
    @DbFieldInfo(value = "switch_enabled", isRequired = true)
    public Integer switchEnabled;

    /**
     * 供应商
     */
    @DbFieldInfo(value = "provider", isRequired = true)
    public String provider;

    /**
     * 姓名
     */
    @DbFieldInfo(value = "name", isRequired = true)
    public String name;

    /**
     * 姓名和身份证二要素的散列值
     */
    @DbFieldInfo(value = "hash_factor2", isRequired = true)
    public String hashFactor2;

    /**
     * 照片位置
     */
    @DbFieldInfo(value = "photo", isRequired = true)
    public String photo;

    /**
     * 照片类型
     */
    @DbFieldInfo(value = "photo_type", isRequired = true)
    public String photoType;

    /**
     * 来源ID
     */
    @DbFieldInfo(value = "source_id", isRequired = true)
    public String sourceId;

    /**
     * 来源类型
     */
    @DbFieldInfo(value = "source_type", isRequired = true)
    public String sourceType;

    /**
     * 失效时间
     */
    @DbFieldInfo(value = "expired_time", isRequired = true)
    public Date expiredTime;
}
