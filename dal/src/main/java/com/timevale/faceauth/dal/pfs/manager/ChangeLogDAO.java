package com.timevale.faceauth.dal.pfs.manager;

import com.timevale.faceauth.dal.pfs.dataobject.ChangeLogDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;

/** 操作记录流水 */
public interface ChangeLogDAO {

  @Insert({
    "<script>",
    "INSERT INTO `change_log` (`sql_command`, `table_name`, `sql_string`, `sql_parameter_list`",
    ") VALUES (#{sqlCommand}, #{tableName}, #{sqlString}, #{sqlParameterList}",
    ")",
    "</script>"
  })
  @Options(useGeneratedKeys = true, keyColumn = "id")
  int insert(ChangeLogDO entity);
}
