package com.timevale.faceauth.dal.pfs.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * The table. 操作记录流水
 *
 * <AUTHOR> Kunpeng
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChangeLogDO {

  /** id 自增主键. */
  private Long id;
  /** 操作人UserId. */
  private String operateUserId;
  /** SQL命令类型[INSERT,UPDATE,DELETE,SELECT]. */
  private String sqlCommand;
  /** 目标表名. */
  private String tableName;
  /** sql语句. */
  private String sqlString;
  /** sql参数. */
  private String sqlParameterList;
  /** 扩展字段 */
  private String extend;
  /** createTime 记录创建时间. */
  private Date createTime;
  /** modifyTime 记录更新时间. */
  private Date modifyTime;
}
