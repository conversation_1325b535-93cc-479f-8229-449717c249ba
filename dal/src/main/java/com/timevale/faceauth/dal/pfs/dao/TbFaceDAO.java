package com.timevale.faceauth.dal.pfs.dao;


import com.timevale.faceauth.dal.pfs.face.support.FaceAuthQueryRequestDO;
import com.timevale.faceauth.dal.pfs.face.support.TbFaceQuickSignQueryDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * The Table faceauth. tb_face
 *
 * <AUTHOR> Kunpeng
 */
@Mapper
public interface TbFaceDAO {

  /**
   * desc:专为快捷签出证查询使用
   *
   * @param faceAuthQueryRequestDO
   * @return List<QuickSignQueryDO>
   */
  @Select({
          "<script>",
          "select * from tb_face ",
          "where  1=1 ",
          "<if test='faceId != null'> and face_id = #{faceId} </if>",
          "<if test='bizId != null'> and biz_id = #{bizId} </if>",
          "<if test='appId != null'> and app_id = #{appId} </if>",
          "<if test='idno != null'> and id_no = #{idno} </if>",
          "<if test='isOk != null'> and is_ok = #{isOk} </if>",
          "<if test='bizCode != null'> and biz_code = #{bizCode} </if>",
          "<if test='updateTime != null'> and modify_time between 0 and #{updateTime} </if>",
          "order by modify_time desc limit #{currIndex},#{pageSize}",
          "</script>"})
  List<TbFaceQuickSignQueryDO> queryByUpdateTimeAndIdNo(FaceAuthQueryRequestDO faceAuthQueryRequestDO);

  @Select({
          "<script>",
          "select * from tb_face ",
          "where  1=1 ",
          "<if test='idno != null'> and id_no = #{idno} </if>",
          "<if test='appId != null'> and app_id = #{appId} </if>",
          "<if test='isOk != null'> and is_ok = #{isOk} </if>",
          "<if test='updateTime != null'> and modify_time between 0 and #{updateTime} </if>",
          "order by modify_time desc limit #{currIndex},#{pageSize}",
          "</script>"})
  List<TbFaceQuickSignQueryDO> quickSignQueryListByTbFace(FaceAuthQueryRequestDO faceAuthQueryRequestDO);

}
