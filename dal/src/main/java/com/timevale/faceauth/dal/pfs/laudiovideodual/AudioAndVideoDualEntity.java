package com.timevale.faceauth.dal.pfs.laudiovideodual;

import com.timevale.faceauth.dal.pfs.support.AutoIncrementIdentityStandardDO;
import com.timevale.faceauth.dal.pfs.support.DbFieldInfo;
import com.timevale.faceauth.dal.pfs.support.DbTableName;

/**
 * 智能视频认证实体对象
 *
 * <AUTHOR>
 */
@DbTableName("tb_audiovideodual")
public class AudioAndVideoDualEntity extends AutoIncrementIdentityStandardDO {

  /** 检测模式,1=图片检测 2=语音和图片检测 3=语音+图片+活体*/
  @DbFieldInfo(value = "check_mode", isRequired = true)
  public int checkMode;

  /** 认证 ID */
  @DbFieldInfo(value = "certify_id", isRequired = true)
  public String certifyId;

  /** 认证 token */
  @DbFieldInfo(value = "token", isRequired = true)
  public String token;

  /** 应用 ID */
  @DbFieldInfo(value = "app_id", isRequired = true)
  public String appId;

  /** 业务场景 */
  @DbFieldInfo(value = "biz_scene")
  public String bizScene;

  /** 业务 ID */
  @DbFieldInfo(value = "biz_id", isRequired = true)
  public String bizId;

  /** 证件类型 */
  @DbFieldInfo(value = "id_type", isRequired = true)
  public String idType;

  /** 证件号码 */
  @DbFieldInfo(value = "id_no", isRequired = true)
  public String idNo;

  /** 姓名 */
  @DbFieldInfo(value = "name", isRequired = true)
  public String name;


  @DbFieldInfo(value = "message")
  public String message;

  @DbFieldInfo(value = "home_page")
  public String homePage;

  @DbFieldInfo(value = "voice_result")
  public String voiceResult;

  @DbFieldInfo(value = "voice_template")
  public String voiceTemplate;

  @DbFieldInfo(value = "voice_template_append")
  public String voiceTemplateAppend;


  /** 完成状态：完成（1）；未完成（0） */
  @DbFieldInfo("completed_status")
  public int completedStatus = 0;

  /** 完成时间 */
  @DbFieldInfo("completed_time")
  public long completedTime = 0L;

  /** 过期状态：过期（1）；未过期（0） */
  @DbFieldInfo("expired")
  public byte expired = 0x00;

  /** 过期时间 */
  @DbFieldInfo("expired_time")
  public long expiredTime = 0L;

  /**
   * 支付宝用户ID
   */
  @DbFieldInfo(value = "alipay_user_id")
  public String alipayUserId;


  /**
   * 支付宝用户手机号
   */
  @DbFieldInfo(value = "alipay_user_mobile")
  public String alipayUserMobile;
}
