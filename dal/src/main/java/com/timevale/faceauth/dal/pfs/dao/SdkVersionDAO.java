package com.timevale.faceauth.dal.pfs.dao;


import com.timevale.faceauth.dal.pfs.dataobject.SdkVersionDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2024/8/6 14:46
 */
public interface SdkVersionDAO {


    @Insert("INSERT INTO tb_sdk_version (sdk_version_id, sdk_version, status,  operator) "
            + "VALUES (#{sdkVersionId}, #{sdkVersion}, #{status},  #{operator})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(SdkVersionDO sdkVersion);


    @Update("UPDATE tb_sdk_version SET "
            + "status = #{status},  operator = #{operator} "
            + "WHERE sdk_version_id = #{sdkVersionId}")
    int updateStatus(@Param("sdkVersionId") String sdkVersionId, @Param("status") String status, @Param("operator") String operator);


    @Update("UPDATE tb_sdk_version SET "
            + "latest_activate_time = #{latestActivateTime}"
            + "WHERE sdk_version_id = #{sdkVersionId}")
    int updateLatestActivateTime(SdkVersionDO sdkVersion);


    @Select("SELECT *" +
            "FROM " +
            "`tb_sdk_version` " +
            "ORDER BY id desc limit 200"
    )
    List<SdkVersionDO> listAll();


    @Select("SELECT *" +
            "FROM " +
            "`tb_sdk_version` " +
            "ORDER BY id desc   limit ${(pageNum-1)*pageSize}, ${pageSize}"
    )
    List<SdkVersionDO> query(@Param("pageNum") Integer pageNum, @Param("pageSize")Integer pageSize);

    @Select("SELECT count(1)" +
            "FROM " +
            "`tb_sdk_version` ")
    Long countALl();

    @Select({
            "<script>",
            "select * from tb_sdk_version ",
            "where ",
            "sdk_version = #{sdkVersion} ",
            "order by id desc limit 1 ",
            "</script>"})
    SdkVersionDO selectByVersion(@Param("sdkVersion") String sdkVersion);
}
