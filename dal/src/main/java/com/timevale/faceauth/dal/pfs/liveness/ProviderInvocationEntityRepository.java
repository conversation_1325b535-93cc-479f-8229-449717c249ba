package com.timevale.faceauth.dal.pfs.liveness;

import com.timevale.faceauth.dal.pfs.liveness.support.ProviderInvocationEntitySqlProvider;
import org.apache.ibatis.annotations.*;

/**
 * 供应商接口调用记录库
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/2/20
 */
public interface ProviderInvocationEntityRepository {

  @InsertProvider(type = ProviderInvocationEntitySqlProvider.class, method = "insert")
  @SelectKey(
      statement = "select last_insert_id()",
      keyProperty = "e.id",
      before = false,
      resultType = long.class)
  int insert(@Param("e") ProviderInvocationEntity entity);

  @SelectProvider(type = ProviderInvocationEntitySqlProvider.class, method = "getByInvokeId")
  ProviderInvocationEntity getByInvokeId(@Param("invokeId") String invokeId);

  @SelectProvider(type = ProviderInvocationEntitySqlProvider.class, method = "getByBizId")
  ProviderInvocationEntity getByBizId(
      @Param("bizCode") String bizCode, @Param("bizId") String bizId);

  @UpdateProvider(type = ProviderInvocationEntitySqlProvider.class, method = "completedInvocation")
  int completedInvocation(ProviderInvocationCompletedEntity entity);
}
