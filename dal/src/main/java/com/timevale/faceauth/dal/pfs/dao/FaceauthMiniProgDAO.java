package com.timevale.faceauth.dal.pfs.dao;

import com.timevale.faceauth.dal.pfs.dataobject.FaceauthMiniProgDO;
import com.timevale.faceauth.dal.pfs.provider.FaceauthMiniProgProvider;
import com.timevale.faceauth.dal.pfs.face.support.FaceAuthQueryRequestDO;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;

import java.util.List;

/**
 * The Table faceauth_zhima. faceauth_zhima
 *
 * <AUTHOR> Kunpeng
 */
public interface FaceauthMiniProgDAO {

  @InsertProvider(type = FaceauthMiniProgProvider.class, method = "insert")
  int insert(FaceauthMiniProgDO entity);

  @SelectProvider(type = FaceauthMiniProgProvider.class, method = "findById")
  FaceauthMiniProgDO getById(String faceAuthId);

  @UpdateProvider(type = FaceauthMiniProgProvider.class, method = "update")
  void update(FaceauthMiniProgDO faceauthMiniProgDO);

  /**
   * desc:专为快捷签出证查询使用
   *
   * @param faceAuthQueryRequestDO
   * @return List<InfoServiceBankthreeDO>
   */
  @Select({
          "<script>",
          "select * from faceauth_mini_prog ",
          "where  1=1 ",
          "<if test='idno != null'> and id_no = #{idno} </if>",
          "<if test='status != null'> and status = #{status} </if>",
          "<if test='modifyTime != null'> and update_time between 0 and #{modifyTime} </if>",
          "order by update_time desc limit #{currIndex},#{pageSize}",
          "</script>"})
  List<FaceauthMiniProgDO> queryByUpdateTimeAndIdNo(FaceAuthQueryRequestDO faceAuthQueryRequestDO);

  @Select({
          "<script>",
          "select * from faceauth_mini_prog mn inner join faceauth fu on mn.id = fu.id",
          "where  1=1 ",
          "<if test='idno != null'> and mn.id_no = #{idno} </if>",
          "<if test='status != null'> and mn.status = #{status} </if>",
          "<if test='modifyTime != null'> and mn.update_time between 0 and #{modifyTime} </if>",
          "order by mn.update_time desc limit #{currIndex},#{pageSize}",
          "</script>"})
  List<FaceauthMiniProgDO> quickSignQueryListByMiniProg(FaceAuthQueryRequestDO faceAuthQueryRequestDO);
}
