package com.timevale.faceauth.dal.pfs.dataobject;

import java.util.Date;

/**
 * The table. faceauth_zhima
 *
 * <AUTHOR> Kunpeng
 */
public class FaceauthZhimaDO {

  /** createTime CREATE_TIME. */
  private Date createTime;
  /** updateTime UPDATE_TIME. */
  private Date updateTime;
  /** id 主键， 人脸识别id. */
  private String id;
  /** idNo 身份证号. */
  private String idNo;
  /** name 姓名. */
  private String name;
  /** bizNo 芝麻信用业务ID. */
  private String bizNo;
  /** errorCode 芝麻返回错误码. */
  private String errorCode;
  /** callbackUrl 回调地址. */
  private String callbackUrl;
  /** transactionId 订单号. */
  private String transactionId;
  /** passed 芝麻人脸认证是否通过. 1为通过*/
  private Integer passed;

  /** 是否是小程序 0-不是小程序，h5 1-支付宝小程序 */
  private Integer xcx;

  /** 芝麻AppId */
  private String appId;

  /**
   * Get createTime CREATE_TIME.
   *
   * @return the string
   */
  public Date getCreateTime() {
    return createTime;
  }

  /** Set createTime CREATE_TIME. */
  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  /**
   * Get updateTime UPDATE_TIME.
   *
   * @return the string
   */
  public Date getUpdateTime() {
    return updateTime;
  }

  /** Set updateTime UPDATE_TIME. */
  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  /**
   * Get id 主键， 人脸识别id.
   *
   * @return the string
   */
  public String getId() {
    return id;
  }

  /** Set id 主键， 人脸识别id. */
  public void setId(String id) {
    this.id = id;
  }

  /**
   * Get idNo 身份证号.
   *
   * @return the string
   */
  public String getIdNo() {
    return idNo;
  }

  /** Set idNo 身份证号. */
  public void setIdNo(String idNo) {
    this.idNo = idNo;
  }

  /**
   * Get name 姓名.
   *
   * @return the string
   */
  public String getName() {
    return name;
  }

  /** Set name 姓名. */
  public void setName(String name) {
    this.name = name;
  }

  /**
   * Get bizNo 芝麻信用业务ID.
   *
   * @return the string
   */
  public String getBizNo() {
    return bizNo;
  }

  /** Set bizNo 芝麻信用业务ID. */
  public void setBizNo(String bizNo) {
    this.bizNo = bizNo;
  }

  /**
   * Get errorCode 芝麻返回错误码.
   *
   * @return the string
   */
  public String getErrorCode() {
    return errorCode;
  }

  /** Set errorCode 芝麻返回错误码. */
  public void setErrorCode(String errorCode) {
    this.errorCode = errorCode;
  }

  /**
   * Get callbackUrl 回调地址.
   *
   * @return the string
   */
  public String getCallbackUrl() {
    return callbackUrl;
  }

  /** Set callbackUrl 回调地址. */
  public void setCallbackUrl(String callbackUrl) {
    this.callbackUrl = callbackUrl;
  }

  /**
   * Get transactionId 订单号.
   *
   * @return the string
   */
  public String getTransactionId() {
    return transactionId;
  }

  /** Set transactionId 订单号. */
  public void setTransactionId(String transactionId) {
    this.transactionId = transactionId;
  }

  /**
   * Get passed 芝麻人脸认证是否通过.
   *
   * @return the string
   */
  public Integer getPassed() {
    return passed;
  }

  /** Set passed 芝麻人脸认证是否通过. */
  public void setPassed(Integer passed) {
    this.passed = passed;
  }

  public Integer getXcx() {
    return xcx;
  }

  public void setXcx(Integer xcx) {
    this.xcx = xcx;
  }

  public String getAppId() {
    return appId;
  }

  public void setAppId(String appId) {
    this.appId = appId;
  }

  @Override
  public String toString() {
    return super.toString();
  }
}
