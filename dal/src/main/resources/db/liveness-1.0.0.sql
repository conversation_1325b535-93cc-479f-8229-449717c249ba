-- 活体认证相关列表 T-SQL 脚本

drop table if exists `tb_liveness`;
create table `tb_liveness`
(
   `id`                   bigint        not null auto_increment comment '主键',
   `liveness_id`          varchar(32)   not null comment '活体认证 ID',
   `liveness_type`        varchar(32)   not null comment '活体认证方式',
   `app_id`               varchar(32)   not null comment '应用 ID',
   `biz_code`             varchar(32)   not null comment '业务类型编码',
   `biz_id`               varchar(128)  not null comment '业务 ID',
   `id_type`              varchar(64)   not null comment '证件类型',
   `id_no`                varchar(255)  not null comment '证件号码',
   `name`                 varchar(128)  not null comment '姓名',
   `provider_code`        varchar(32)   not null comment '供应商代码',
   `completed`            bit           not null comment '完成状态：完成（1）；未完成（0）',
   `completed_time`       bigint        not null default 0 comment '完成时间',
   `cancelled`            bit           not null comment '取消状态：取消（1）；未取消（0）',
   `cancelled_time`       bigint        not null default 0 comment '取消时间',
   `expired`              bit           not null comment '过期状态：过期（1）；未过期（0）',
   `expired_time`         bigint        not null default 0 comment '过期时间',
   `create_time`          timestamp     not null default current_timestamp comment '创建时间',
   `modify_time`          timestamp     not null default current_timestamp on update current_timestamp comment '更新时间',
   primary key (id),
   unique index `uqx_liveness_id`(`liveness_id`),
   index `idx_liveness_type`(`liveness_type`),
   index `idx_app_id`(`app_id`),
   index `idx_biz_id_code`(`biz_id`,`biz_code`),
   index `idx_biz_id`(`biz_id`),
   index `idx_biz_code`(`biz_code`),
   index `idx_id_no`(`id_no`),
   index `idx_name`(`name`)
)engine = InnoDB auto_increment=1 default charset=utf8 comment='保存活体认证的主要数据，一条记录代表一次活体认证。';

drop table if exists `tb_liveness_video`;
create table `tb_liveness_video`
(
   `id`                   bigint        not null auto_increment comment '主键',
   `liveness_id`          varchar(32)   not null comment '活体认证 ID',
   `actions`              varchar(8)    not null comment '描述当前视频的活体行为方式',
   `video`                varchar(255)  not null comment '视频，或者存储视频的位置键值（key）',
   `video_location`       varchar(255)  not null comment '视频位置，或者视频存储方式',
   `create_time`          timestamp     not null default current_timestamp comment '创建时间',
   `modify_time`          timestamp     not null default current_timestamp on update current_timestamp comment '更新时间',
   primary key (id),
   index `idx_liveness_id`(`liveness_id`)
)engine = InnoDB auto_increment=1 default charset=utf8 comment='记录活体认证时发生的视频内容';

drop table if exists `tb_liveness_provider_order`;
create table `tb_liveness_provider_order`
(
   `id`                   bigint        not null auto_increment comment '主键',
   `liveness_id`          varchar(32)   not null comment '活体认证 ID',
   `provider_code`        varchar(32)   not null comment '供应商代码',
   `provider_name`        varchar(128)  not null comment '供应商名称',
   `order_no`             varchar(128)  not null comment '订单号',
   `actions`              varchar(32)   default '' comment '活体行为',
   `expired`              bit           not null default 0 comment '过期状态：过期（1）；未过期（0）',
   `expired_time`         bigint        not null comment '过期时间',
   `completed`            bit           not null default 0 comment '完成状态：完成（1）；未完成（0）',
   `completed_time`       bigint        not null default 0 comment '完成时间',
   `photo_src`            varchar(255)  default '' comment '源照片或者存储源照片的键值（key）',
   `photo`                varchar(255)  default '' comment '认证完成照片或者存储认证完成照片的键值（key）',
   `photo_location`       varchar(255)  not null comment '照片位置，或者照片存储的方式',
   `photo_type`           varchar(32)   not null comment '照片类型',
   `liveness_rate`        float         not null default -1 comment '活体率分值',
   `sim`                  float         not null default -1 comment '相似度分值',
   `fee`                  bit           not null default 0 comment '收费状态：收费（1）；不收费（0）',
   `fee_type`             varchar(32)   not null comment '收费方式',
   `msg`                  varchar(255)  default '' comment '完成消息',
   `create_time`          timestamp     not null default current_timestamp comment '创建时间',
   `modify_time`          timestamp     not null default current_timestamp on update current_timestamp comment '更新时间',
   primary key (id),
   index `idx_liveness_id`(`liveness_id`),
   index `idx_provider_code`(`provider_code`),
   index `idx_order_no`(`order_no`)
)engine = InnoDB auto_increment=1 default charset=utf8 comment='记录供应商活体认证的订单';

drop table if exists `tb_liveness_provider_video`;
create table `tb_liveness_provider_video`
(
   `id`                   bigint        not null auto_increment comment '主键',
   `liveness_id`          varchar(32)   not null comment '活体认证 ID',
   `provider_code`        varchar(32)   not null comment '供应商代码',
   `actions`              varchar(8)    not null comment '描述当前视频的活体行为方式',
   `video`                varchar(255)  not null comment '视频，或者存储视频的位置键值（key）',
   `video_location`       varchar(255)  not null comment '视频位置，或者视频存储方式',
   `create_time`          timestamp     not null default current_timestamp comment '创建时间',
   `modify_time`          timestamp     not null default current_timestamp on update current_timestamp comment '更新时间',
   primary key (id),
   index `idx_liveness_id`(`liveness_id`),
   index `idx_provider_code`(`provider_code`)
)engine = InnoDB auto_increment=1 default charset=utf8 comment='记录供应商活体认证时使用的视频信息，基于一个供应商使用多个视频的可能性设计当前表';

drop table if exists `tb_provider_invocation`;
create table `tb_provider_invocation`
(
   `id`                   bigint        not null auto_increment comment '主键',
   `invoke_id`            varchar(32)   not null comment '业务调用 ID',
   `biz_code`             varchar(32)   not null comment '业务类型编码',
   `biz_id`               varchar(32)   not null comment '业务类型 ID',
   `provider_code`        varchar(32)   not null comment '供应商编码',
   `request_id`           varchar(128)  default '' comment '请求 ID',
   `request_type`         varchar(32)   default '' comment '请求方式',
   `request_api`          varchar(255)  default '' comment '请求 API',
   `request_method`       varchar(255)  default '' comment '请求方法',
   `err`                  bit           not null comment '当前调用发生错误状态：发生错误（1）；未发生错误（0）',
   `err_msg`              varchar(512)  default '' comment '当前调用发生的错误消息',
   `request`              varchar(255)  not null comment '请求内容，详细数据结合 content_location 字段获得',
   `request_time`         bigint        not null default 0 comment '请求时间',
   `responsed`            bit           not null default 0 comment '当前调用的响应状态：有响应（1）；无响应（0）',
   `response`             varchar(255)  default '' comment '响应数据内容，详细数据结合 content_location 字段获得',
   `responsed_time`       bigint        default 0 comment '响应时间',
   `content_location`     varchar(255)  not null comment '当前调用发生的数据内容存放的位置',
   `create_time`          timestamp     not null default current_timestamp comment '创建时间',
   `modify_time`          timestamp     not null default current_timestamp on update current_timestamp comment '更新时间',
   primary key (id),
   unique index `uqx_invoke_id`(invoke_id),
   index `idx_provider_code`(`provider_code`),
   index `idx_biz_id_code`(`biz_id`,`biz_code`),
   index `idx_biz_id`(`biz_id`),
   index `idx_biz_code`(`biz_code`)
)engine = InnoDB auto_increment=1 default charset=utf8 comment='记录第三方供应商接口调用信息';

drop table if exists `tb_liveness_user_photo`;
create table `tb_liveness_user_photo`
(
   `id`                   bigint        not null auto_increment comment '主键',
   `id_type`              varchar(64)   not null comment '证件类型',
   `id_no`                varchar(255)  not null comment '证件号码',
   `name`                 varchar(128)  not null comment '姓名',
   `hash`                 varchar(512)  not null comment '散列码',
   `hash_algorithm`       varchar(32)   not null comment '散列方式',
   `photo`                varchar(255)  not null comment '照片',
   `photo_location`       varchar(255)  not null comment '照片位置，或者照片存储的方式',
   `photo_type`           varchar(32)   not null comment '照片类型',
   `create_time`          timestamp     not null default current_timestamp comment '创建时间',
   `modify_time`          timestamp     not null default current_timestamp on update current_timestamp comment '更新时间',
   primary key (id),
   index `idx_id_no`(id_no),
   index `idx_name`(`name`),
   index `idx_hash`(`hash`),
   index `idx_photo_type`(`photo_type`)
)engine = InnoDB auto_increment=1 default charset=utf8 comment='记录活体认证成功时发生的有效用户照片内容';
