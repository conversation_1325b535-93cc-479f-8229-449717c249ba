drop table if exists `tb_face`;
create table `tb_face`
(
    `id`                 bigint       not null auto_increment,
    `tid`                varchar(64)           default '' comment 'transactionId',
    `face_id`            varchar(32)  not null comment '刷脸请求唯一id',
    `app_id`             varchar(16)  not null comment '应用id',
    `oid`                varchar(64)  not null comment '账户id',
    `biz_id`             varchar(128) not null comment '上游业务Id',
    `biz_code`           varchar(32)  not null comment '业务编码',
    `client_type`        varchar(32)  not null comment '刷脸接入端类型',
    `name`               varchar(128) not null comment '姓名',
    `id_no`              varchar(32)  not null comment '证件号码',
    `id_type`            varchar(32)  not null comment '证件类型',
    `photo`              varchar(512) not null comment '比对照片',
    `photo_type`         varchar(32)  not null comment '比对照片类型',
    `provider`           varchar(32)  not null comment '供应商',
    `input`              text                  default '' comment '当前刷脸输入上下文',
    `is_ok`              tinyint      not null default 0 comment '当前刷脸成功状态。（已成功：1）',
    `return_url`         varchar(2048)         default '' comment '当前刷脸成功回跳地址',
    `return_time`        bigint       not null default 0 comment '当前刷脸完成时回跳时间',
    `return_err_msg`     varchar(512)          default '' comment '当前刷脸完成时回跳失败原因',
    `callback_url`       varchar(2048)         default '' comment '当前刷脸上游回调地址',
    `callback_time`      bigint       not null default 0 comment '当前刷脸完成时回调上游业务开始时间',
    `callback_is_done`   tinyint      not null default 0 comment '当前刷脸完成时上游业务回调完成状态。（已完成：1）',
    `callback_done_time` bigint       not null default 0 comment '当前刷脸完成时回调上游业务完成时间',
    `callback_is_ok`     tinyint      not null default 0 comment '当前刷脸完成时回调上游业务成功状态。（成功：1）',
    `callback_err_msg`   varchar(512)          default '' comment '当前刷脸完成时回调上游业务失败原因',
    `create_time`        timestamp    not null default current_timestamp comment '当前刷脸开始时间',
    `modify_time`        timestamp    not null default current_timestamp on update current_timestamp comment '更新时间',
    primary key (`id`),
    unique index `uqx_face_id` (`face_id`),
    index `idx_app_id` (`app_id`),
    index `idx_oid` (`oid`),
    index `idx_biz_id` (`biz_id`),
    index `idx_biz_code` (`biz_code`),
    index `idx_biz_id_code` (`biz_id`, `biz_code`),
    index `idx_id_no` (`id_no`),
    index `idx_name` (`name`),
    index `idx_provider` (`provider`),
    index `idx_ok` (`is_ok`),
    index `idx_provider_ok` (`provider`, `is_ok`)
) engine = InnoDB
  auto_increment = 1
  default charset = utf8 comment ='刷脸认证请求记录';

drop table if exists `tb_provider_face`;
create table `tb_provider_face`
(
    `id`           bigint        not null auto_increment,
    `face_id`      varchar(32)   not null comment '刷脸请求唯一id',
    `provider`     varchar(32)   not null comment '供应商',
    `full_name`    varchar(512)           default 'UNKNOWN' comment '供应商全称',
    `face_input`   text                   default '' comment '认证上下文',
    `face_data`    varchar(4096) not null comment '认证地址',
    `order_no`     varchar(128)  not null comment '供应商订单号',
    `is_done`      tinyint       not null default 0 comment '当前供应商刷脸完成状态。（刷脸完成：1）',
    `done_time`    bigint        not null default 0 comment '完成时间',
    `thirdpart_id` varchar(255)           default '' comment '供应商业务标识，当前字段用来持久化除额外订单号（order_no）的之外的其他业务标识',
    `create_time`  timestamp     not null default current_timestamp comment '创建时间',
    `modify_time`  timestamp     not null default current_timestamp on update current_timestamp comment '更新时间',
    primary key (`id`),
    index `idx_face_id` (`face_id`),
    index `idx_order_no` (`order_no`),
    index `idx_provider` (`provider`),
    index `idx_provider_order` (`provider`, `order_no`)
) engine = InnoDB
  auto_increment = 1
  default charset = utf8 comment ='供应商刷脸记录';

drop table if exists `tb_provider_return`;
create table `tb_provider_return`
(
    `id`          bigint       not null auto_increment,
    `face_id`     varchar(32)  not null comment '刷脸请求唯一id',
    `provider`    varchar(32)  not null comment '供应商',
    `return_type` varchar(32)  not null comment '供应商数据返回方式',
    `data`        text                  default '' comment '供应商返回数据正文',
    `is_ok`       tinyint      not null default 0 comment '成功状态：成功（1）；失败（0）。',
    `err_msg`     varchar(512) null     default '' comment '失败时，错误内容',
    `create_time` timestamp    not null default current_timestamp comment '创建时间',
    `modify_time` timestamp    not null default current_timestamp on update current_timestamp comment '创建时间',
    primary key (`id`),
    index `idx_face_id` (`face_id`),
    index `idx_provider` (`provider`)
) engine = InnoDB
  auto_increment = 1
  default charset = utf8 comment ='供应商返回';

  drop table if exists `tb_user_photo`;
  create table `tb_user_photo`(
      `id` bigint not null auto_increment,
      `id_no` varchar(32) not null comment '证件号码',
      `photo` varchar(255) not null comment '照片位置',
      `photo_type` varchar(32) not null comment '照片类型',
      `create_time` timestamp    not null default current_timestamp comment '创建时间',
      `modify_time` timestamp    not null default current_timestamp on update current_timestamp comment '创建时间',
      primary key (`id`),
      index `idx_id_no` (`id_no`)
  ) engine=InnoDB auto_increment=1 default  charset =utf8 comment '用户照片';

drop table if exists `tb_face_resources`;
create table `tb_face_resources`(
    `id` bigint not null auto_increment,
    `face_id` varchar(32) not null comment '刷脸请求唯一id',
    `content` varchar(255) not null comment '资源内容',
    `resource_type` varchar(32) not null comment '资源类型',
    `save_type` varchar(32) not null comment  '存储方式',
    `create_time` timestamp    not null default current_timestamp comment '创建时间',
    `modify_time` timestamp    not null default current_timestamp on update current_timestamp comment '创建时间',
    primary key (`id`),
    index `idx_face_id` (`face_id`)
)engine =InnoDB auto_increment=1 default charset =utf8 comment '刷脸资源';