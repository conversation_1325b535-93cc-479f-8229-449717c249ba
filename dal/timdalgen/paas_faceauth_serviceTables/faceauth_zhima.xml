<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="faceauth_zhima" physicalName="faceauth_zhima" remark="faceauth_zhima">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,ID_NO,NAME,BIZ_NO,ERROR_CODE,CALLBACK_URL,TRANSACTION_ID,PASSED,CREATE_TIME,UPDATE_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.ID_NO,sf.NAME,sf.BIZ_NO,sf.ERROR_CODE,sf.CALLBACK_URL,sf.TRANSACTION_ID,sf.PASSED,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:faceauth_zhima">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO faceauth_zhima(
            ID
            ,ID_NO
            ,NAME
            ,BIZ_NO
            ,ERROR_CODE
            ,CALLBACK_URL
            ,TRANSACTION_ID
            ,PASSED
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES(
             null
            , #{idNo,jdbcType=VARCHAR}
            , #{name,jdbcType=VARCHAR}
            , #{bizNo,jdbcType=VARCHAR}
            , #{errorCode,jdbcType=VARCHAR}
            , #{callbackUrl,jdbcType=VARCHAR}
            , #{transactionId,jdbcType=VARCHAR}
            , #{passed,jdbcType=TINYINT}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{updateTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:faceauth_zhima">
        INSERT INTO faceauth_zhima(
            ID
            ,ID_NO
            ,NAME
            ,BIZ_NO
            ,ERROR_CODE
            ,CALLBACK_URL
            ,TRANSACTION_ID
            ,PASSED
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.idNo,jdbcType=VARCHAR}
                , #{item.name,jdbcType=VARCHAR}
                , #{item.bizNo,jdbcType=VARCHAR}
                , #{item.errorCode,jdbcType=VARCHAR}
                , #{item.callbackUrl,jdbcType=VARCHAR}
                , #{item.transactionId,jdbcType=VARCHAR}
                , #{item.passed,jdbcType=TINYINT}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>


    <!-- 根据唯一约束操作数据 -->
    <operation name="updateByTransactionid" paramtype="object" remark="根据唯一约束Transactionid更新表:faceauth_zhima">
        <![CDATA[
        UPDATE faceauth_zhima
        SET
            ID              = #{id,jdbcType=VARCHAR}
            ,ID_NO           = #{idNo,jdbcType=VARCHAR}
            ,NAME            = #{name,jdbcType=VARCHAR}
            ,BIZ_NO          = #{bizNo,jdbcType=VARCHAR}
            ,ERROR_CODE      = #{errorCode,jdbcType=VARCHAR}
            ,CALLBACK_URL    = #{callbackUrl,jdbcType=VARCHAR}
            ,PASSED          = #{passed,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            TRANSACTION_ID  = #{transactionId,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="deleteByTransactionid" remark="根据唯一约束Transactionid删除数据:faceauth_zhima">
        <![CDATA[
        DELETE FROM faceauth_zhima
        WHERE
            TRANSACTION_ID  = #{transactionId,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="getByTransactionid" multiplicity="one" remark="根据唯一约束Transactionid获取数据:faceauth_zhima">
        SELECT *
        FROM faceauth_zhima
        WHERE
        <![CDATA[
            TRANSACTION_ID  = #{transactionId,jdbcType=VARCHAR}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdno" multiplicity="many" remark="根据普通索引Idno获取数据:faceauth_zhima">
        SELECT *
        FROM faceauth_zhima
        WHERE
        <![CDATA[
            ID_NO           = #{idNo,jdbcType=VARCHAR}

        ]]>
    </operation>
</table>
