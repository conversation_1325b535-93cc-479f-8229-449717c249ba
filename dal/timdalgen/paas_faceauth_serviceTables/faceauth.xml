<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="faceauth" physicalName="faceauth" remark="faceauth">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,TYPE,AUTH_STATUS,CREATE_TIME,UPDATE_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.TYPE,sf.AUTH_STATUS,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:faceauth">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO faceauth(
            ID
            ,TYPE
            ,AUTH_STATUS
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES(
             null
            , #{type,jdbcType=TINYINT}
            , #{authStatus,jdbcType=TINYINT}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{updateTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:faceauth">
        INSERT INTO faceauth(
            ID
            ,TYPE
            ,AUTH_STATUS
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.type,jdbcType=TINYINT}
                , #{item.authStatus,jdbcType=TINYINT}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:faceauth">
        <![CDATA[
        UPDATE faceauth
        SET
            TYPE            = #{type,jdbcType=TINYINT}
            ,AUTH_STATUS     = #{authStatus,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=VARCHAR}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:faceauth">
        <![CDATA[
        DELETE FROM faceauth
        WHERE
            ID = #{id,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:faceauth">
        SELECT *
        FROM faceauth
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=VARCHAR}
        ]]>
    </operation>
</table>
