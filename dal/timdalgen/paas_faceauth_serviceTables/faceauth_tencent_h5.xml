<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="faceauth_tencent_h5" physicalName="faceauth_tencent_h5" remark="faceauth_tencent_h5">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,CODE,ID_NO,NAME,ID_TYPE,USER_ID,ORDER_NO,BIZ_SEQ_NO,LIVE_RATE,FACE_AUTH_ID,PHOTO_INPUT,SIMILARITY,CALLBACK_URL,PHOTO_TYPE,AUTH_STATUS,CREATE_TIME,UPDATE_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.CODE,sf.ID_NO,sf.NAME,sf.ID_TYPE,sf.USER_ID,sf.ORDER_NO,sf.BIZ_SEQ_NO,sf.LIVE_RATE,sf.FACE_AUTH_ID,sf.PHOTO_INPUT,sf.SIMILARITY,sf.CALLBACK_URL,sf.PHOTO_TYPE,sf.AUTH_STATUS,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:faceauth_tencent_h5">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO faceauth_tencent_h5(
            ID
            ,CODE
            ,ID_NO
            ,NAME
            ,ID_TYPE
            ,USER_ID
            ,ORDER_NO
            ,BIZ_SEQ_NO
            ,LIVE_RATE
            ,FACE_AUTH_ID
            ,PHOTO_INPUT
            ,SIMILARITY
            ,CALLBACK_URL
            ,PHOTO_TYPE
            ,AUTH_STATUS
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES(
             null
            , #{code,jdbcType=VARCHAR}
            , #{idNo,jdbcType=VARCHAR}
            , #{name,jdbcType=VARCHAR}
            , #{idType,jdbcType=VARCHAR}
            , #{userId,jdbcType=VARCHAR}
            , #{orderNo,jdbcType=VARCHAR}
            , #{bizSeqNo,jdbcType=VARCHAR}
            , #{liveRate,jdbcType=VARCHAR}
            , #{faceAuthId,jdbcType=VARCHAR}
            , #{photoInput,jdbcType=VARCHAR}
            , #{similarity,jdbcType=VARCHAR}
            , #{callbackUrl,jdbcType=VARCHAR}
            , #{photoType,jdbcType=TINYINT}
            , #{authStatus,jdbcType=TINYINT}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{updateTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:faceauth_tencent_h5">
        INSERT INTO faceauth_tencent_h5(
            ID
            ,CODE
            ,ID_NO
            ,NAME
            ,ID_TYPE
            ,USER_ID
            ,ORDER_NO
            ,BIZ_SEQ_NO
            ,LIVE_RATE
            ,FACE_AUTH_ID
            ,PHOTO_INPUT
            ,SIMILARITY
            ,CALLBACK_URL
            ,PHOTO_TYPE
            ,AUTH_STATUS
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.code,jdbcType=VARCHAR}
                , #{item.idNo,jdbcType=VARCHAR}
                , #{item.name,jdbcType=VARCHAR}
                , #{item.idType,jdbcType=VARCHAR}
                , #{item.userId,jdbcType=VARCHAR}
                , #{item.orderNo,jdbcType=VARCHAR}
                , #{item.bizSeqNo,jdbcType=VARCHAR}
                , #{item.liveRate,jdbcType=VARCHAR}
                , #{item.faceAuthId,jdbcType=VARCHAR}
                , #{item.photoInput,jdbcType=VARCHAR}
                , #{item.similarity,jdbcType=VARCHAR}
                , #{item.callbackUrl,jdbcType=VARCHAR}
                , #{item.photoType,jdbcType=TINYINT}
                , #{item.authStatus,jdbcType=TINYINT}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:faceauth_tencent_h5">
        <![CDATA[
        UPDATE faceauth_tencent_h5
        SET
            CODE            = #{code,jdbcType=VARCHAR}
            ,ID_NO           = #{idNo,jdbcType=VARCHAR}
            ,NAME            = #{name,jdbcType=VARCHAR}
            ,ID_TYPE         = #{idType,jdbcType=VARCHAR}
            ,USER_ID         = #{userId,jdbcType=VARCHAR}
            ,ORDER_NO        = #{orderNo,jdbcType=VARCHAR}
            ,BIZ_SEQ_NO      = #{bizSeqNo,jdbcType=VARCHAR}
            ,LIVE_RATE       = #{liveRate,jdbcType=VARCHAR}
            ,FACE_AUTH_ID    = #{faceAuthId,jdbcType=VARCHAR}
            ,PHOTO_INPUT     = #{photoInput,jdbcType=VARCHAR}
            ,SIMILARITY      = #{similarity,jdbcType=VARCHAR}
            ,CALLBACK_URL    = #{callbackUrl,jdbcType=VARCHAR}
            ,PHOTO_TYPE      = #{photoType,jdbcType=TINYINT}
            ,AUTH_STATUS     = #{authStatus,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=VARCHAR}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:faceauth_tencent_h5">
        <![CDATA[
        DELETE FROM faceauth_tencent_h5
        WHERE
            ID = #{id,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:faceauth_tencent_h5">
        SELECT *
        FROM faceauth_tencent_h5
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=VARCHAR}
        ]]>
    </operation>

    <!-- 根据唯一约束操作数据 -->
    <operation name="updateByIndexBizseqno" paramtype="object" remark="根据唯一约束IndexBizseqno更新表:faceauth_tencent_h5">
        <![CDATA[
        UPDATE faceauth_tencent_h5
        SET
            CODE            = #{code,jdbcType=VARCHAR}
            ,ID_NO           = #{idNo,jdbcType=VARCHAR}
            ,NAME            = #{name,jdbcType=VARCHAR}
            ,ID_TYPE         = #{idType,jdbcType=VARCHAR}
            ,USER_ID         = #{userId,jdbcType=VARCHAR}
            ,ORDER_NO        = #{orderNo,jdbcType=VARCHAR}
            ,LIVE_RATE       = #{liveRate,jdbcType=VARCHAR}
            ,FACE_AUTH_ID    = #{faceAuthId,jdbcType=VARCHAR}
            ,PHOTO_INPUT     = #{photoInput,jdbcType=VARCHAR}
            ,SIMILARITY      = #{similarity,jdbcType=VARCHAR}
            ,CALLBACK_URL    = #{callbackUrl,jdbcType=VARCHAR}
            ,PHOTO_TYPE      = #{photoType,jdbcType=TINYINT}
            ,AUTH_STATUS     = #{authStatus,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            BIZ_SEQ_NO      = #{bizSeqNo,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="deleteByIndexBizseqno" remark="根据唯一约束IndexBizseqno删除数据:faceauth_tencent_h5">
        <![CDATA[
        DELETE FROM faceauth_tencent_h5
        WHERE
            BIZ_SEQ_NO      = #{bizSeqNo,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="getByIndexBizseqno" multiplicity="one" remark="根据唯一约束IndexBizseqno获取数据:faceauth_tencent_h5">
        SELECT *
        FROM faceauth_tencent_h5
        WHERE
        <![CDATA[
            BIZ_SEQ_NO      = #{bizSeqNo,jdbcType=VARCHAR}

        ]]>
    </operation>

    <!-- 根据唯一约束操作数据 -->
    <operation name="updateByIndexFaceauthid" paramtype="object" remark="根据唯一约束IndexFaceauthid更新表:faceauth_tencent_h5">
        <![CDATA[
        UPDATE faceauth_tencent_h5
        SET
            CODE            = #{code,jdbcType=VARCHAR}
            ,ID_NO           = #{idNo,jdbcType=VARCHAR}
            ,NAME            = #{name,jdbcType=VARCHAR}
            ,ID_TYPE         = #{idType,jdbcType=VARCHAR}
            ,USER_ID         = #{userId,jdbcType=VARCHAR}
            ,ORDER_NO        = #{orderNo,jdbcType=VARCHAR}
            ,BIZ_SEQ_NO      = #{bizSeqNo,jdbcType=VARCHAR}
            ,LIVE_RATE       = #{liveRate,jdbcType=VARCHAR}
            ,PHOTO_INPUT     = #{photoInput,jdbcType=VARCHAR}
            ,SIMILARITY      = #{similarity,jdbcType=VARCHAR}
            ,CALLBACK_URL    = #{callbackUrl,jdbcType=VARCHAR}
            ,PHOTO_TYPE      = #{photoType,jdbcType=TINYINT}
            ,AUTH_STATUS     = #{authStatus,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            FACE_AUTH_ID    = #{faceAuthId,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="deleteByIndexFaceauthid" remark="根据唯一约束IndexFaceauthid删除数据:faceauth_tencent_h5">
        <![CDATA[
        DELETE FROM faceauth_tencent_h5
        WHERE
            FACE_AUTH_ID    = #{faceAuthId,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="getByIndexFaceauthid" multiplicity="one" remark="根据唯一约束IndexFaceauthid获取数据:faceauth_tencent_h5">
        SELECT *
        FROM faceauth_tencent_h5
        WHERE
        <![CDATA[
            FACE_AUTH_ID    = #{faceAuthId,jdbcType=VARCHAR}

        ]]>
    </operation>

    <!-- 根据唯一约束操作数据 -->
    <operation name="updateByIndexOrderno" paramtype="object" remark="根据唯一约束IndexOrderno更新表:faceauth_tencent_h5">
        <![CDATA[
        UPDATE faceauth_tencent_h5
        SET
            CODE            = #{code,jdbcType=VARCHAR}
            ,ID_NO           = #{idNo,jdbcType=VARCHAR}
            ,NAME            = #{name,jdbcType=VARCHAR}
            ,ID_TYPE         = #{idType,jdbcType=VARCHAR}
            ,USER_ID         = #{userId,jdbcType=VARCHAR}
            ,BIZ_SEQ_NO      = #{bizSeqNo,jdbcType=VARCHAR}
            ,LIVE_RATE       = #{liveRate,jdbcType=VARCHAR}
            ,FACE_AUTH_ID    = #{faceAuthId,jdbcType=VARCHAR}
            ,PHOTO_INPUT     = #{photoInput,jdbcType=VARCHAR}
            ,SIMILARITY      = #{similarity,jdbcType=VARCHAR}
            ,CALLBACK_URL    = #{callbackUrl,jdbcType=VARCHAR}
            ,PHOTO_TYPE      = #{photoType,jdbcType=TINYINT}
            ,AUTH_STATUS     = #{authStatus,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            ORDER_NO        = #{orderNo,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="deleteByIndexOrderno" remark="根据唯一约束IndexOrderno删除数据:faceauth_tencent_h5">
        <![CDATA[
        DELETE FROM faceauth_tencent_h5
        WHERE
            ORDER_NO        = #{orderNo,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="getByIndexOrderno" multiplicity="one" remark="根据唯一约束IndexOrderno获取数据:faceauth_tencent_h5">
        SELECT *
        FROM faceauth_tencent_h5
        WHERE
        <![CDATA[
            ORDER_NO        = #{orderNo,jdbcType=VARCHAR}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIndexIdno" multiplicity="many" remark="根据普通索引IndexIdno获取数据:faceauth_tencent_h5">
        SELECT *
        FROM faceauth_tencent_h5
        WHERE
        <![CDATA[
            ID_NO           = #{idNo,jdbcType=VARCHAR}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIndexName" multiplicity="many" remark="根据普通索引IndexName获取数据:faceauth_tencent_h5">
        SELECT *
        FROM faceauth_tencent_h5
        WHERE
        <![CDATA[
            NAME            = #{name,jdbcType=VARCHAR}

        ]]>
    </operation>
</table>
